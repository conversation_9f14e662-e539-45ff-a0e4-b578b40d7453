{"name": "jupyterlab-jupytext", "version": "1.4.4", "description": "Save Jupyter Notebooks as Scripts or Markdown files that work well with version control & external text editors", "keywords": ["jup<PERSON><PERSON>", "jupytext", "jupyterlab", "jupyterlab-extension"], "homepage": "https://github.com/mwouts/jupytext/tree/main/jupyterlab/packages/jupyterlab-jupytext", "bugs": {"url": "https://github.com/mwouts/jupytext/issues"}, "license": "MIT", "author": "<PERSON>", "files": ["lib/**/*.{d.ts,eot,gif,html,jpg,js,js.map,json,png,svg,woff2,ttf}", "style/**/*.{css,eot,gif,html,jpg,json,png,svg,woff2,ttf}"], "main": "lib/index.js", "types": "lib/index.d.ts", "repository": {"type": "git", "url": "https://github.com/mwouts/jupytext.git"}, "workspaces": ["ui-tests"], "scripts": {"build": "jlpm run build:lib && jlpm run build:labextension:dev && jlpm run copy:extensioncfgfile", "build:labextension": "jupyter labextension build .", "build:labextension:dev": "jupyter labextension build --development True .", "build:lib": "tsc", "build:prod": "jlpm run build:lib && jlpm run build:labextension && jlpm run copy:extensioncfgfile", "copy:extensioncfgfile": "cp ../../install.json ../../jupyterlab_jupytext/labextension", "clean": "jlpm run clean:lib", "clean:all": "jlpm run clean:lib && jlpm run clean:labextension", "clean:labextension": "rimraf ../../jupyterlab_jupytext/labextension", "clean:lib": "rimraf lib tsconfig.tsbuildinfo", "install:extension": "python ../../scripts/install_extension.py", "watch": "run-p watch:src watch:labextension", "watch:labextension": "jupyter labextension watch .", "watch:src": "tsc -w"}, "jupyterlab": {"discovery": {"server": {"managers": ["pip", "conda"], "base": {"name": "jupyterlab_jupytext"}}}, "extension": true, "schemaDir": "schema", "outputDir": "../../jupyterlab_jupytext/labextension", "sharedPackages": {"jupyterlab-rise": {"singleton": true}}}, "dependencies": {"@jupyterlab/application": "^4.3.3", "@jupyterlab/apputils": "^4.3.3", "@jupyterlab/codeeditor": "^4.3.3", "@jupyterlab/docregistry": "^4.3.3", "@jupyterlab/filebrowser": "^4.3.3", "@jupyterlab/launcher": "^4.3.3", "@jupyterlab/nbformat": "^4.3.3", "@jupyterlab/notebook": "^4.3.3", "@jupyterlab/rendermime": "^4.3.3", "@jupyterlab/settingregistry": "^4.3.3", "@jupyterlab/translation": "^4.3.3", "@jupyterlab/ui-components": "^4.3.3", "@lumino/commands": "^2.3.1", "@lumino/coreutils": "^2.2.0", "@lumino/disposable": "^2.1.3", "buffer": "^6.0.3", "jupyterlab-rise": "^0.43.1"}, "devDependencies": {"@jupyterlab/builder": "^4.3.3", "npm-run-all": "^4.1.5", "rimraf": "^6.0.1", "typescript": "~5.8.2"}}