# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 6
  cacheKey: 8

"@antfu/install-pkg@npm:^1.0.0":
  version: 1.0.0
  resolution: "@antfu/install-pkg@npm:1.0.0"
  dependencies:
    package-manager-detector: ^0.2.8
    tinyexec: ^0.3.2
  checksum: 0fdae280f5185d7225e41ed8f19aa14f96716043366d7aeec5e6bea4f995a826bb250dd01d6e2d9886bbd2c023435ad624096bad9e4c8d6cc3d025b6b9ca32a9
  languageName: node
  linkType: hard

"@antfu/utils@npm:^8.1.0":
  version: 8.1.1
  resolution: "@antfu/utils@npm:8.1.1"
  checksum: 42ded916c4ff7f45a2f462eb020c801d24f2eee830cba4dbeef5a8bb774b6af22238f0c3efdbcb068296eb948aa13bdc32169186e261c99aae13c12360c02580
  languageName: node
  linkType: hard

"@arcanis/slice-ansi@npm:^1.1.1":
  version: 1.1.1
  resolution: "@arcanis/slice-ansi@npm:1.1.1"
  dependencies:
    grapheme-splitter: ^1.0.4
  checksum: 14ed60cb45750d386c64229ac7bab20e10eedc193503fa4decff764162d329d6d3363ed2cd3debec833186ee54affe4f824f6e8eff531295117fd1ebda200270
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.16.7, @babel/runtime@npm:^7.20.6":
  version: 7.27.0
  resolution: "@babel/runtime@npm:7.27.0"
  dependencies:
    regenerator-runtime: ^0.14.0
  checksum: 3e73d9e65f76fad8f99802b5364c941f4a60c693b3eca66147bb0bfa54cf0fbe017232155e16e3fd83c0a049b51b8d7239efbd73626534abe8b54a6dd57dcb1b
  languageName: node
  linkType: hard

"@braintree/sanitize-url@npm:^7.0.4":
  version: 7.1.1
  resolution: "@braintree/sanitize-url@npm:7.1.1"
  checksum: bdfb6add95e97c5a611597197cd8385c6592d340a688bfbb176a1799bde64b9ffa1e723a7bac908d61fdecfccf4301332cdebaa4a1650c2616b5269084d9c8e4
  languageName: node
  linkType: hard

"@chevrotain/cst-dts-gen@npm:11.0.3":
  version: 11.0.3
  resolution: "@chevrotain/cst-dts-gen@npm:11.0.3"
  dependencies:
    "@chevrotain/gast": 11.0.3
    "@chevrotain/types": 11.0.3
    lodash-es: 4.17.21
  checksum: 414229a827e06b4564e271ca3a02ed6f475d400a184dc5ae05308bbc6e966959b84a40a063dacf7debd8f9a1dba5bf8785a891e7b588eafd9f821b43ec16b109
  languageName: node
  linkType: hard

"@chevrotain/gast@npm:11.0.3":
  version: 11.0.3
  resolution: "@chevrotain/gast@npm:11.0.3"
  dependencies:
    "@chevrotain/types": 11.0.3
    lodash-es: 4.17.21
  checksum: 5190ba3a3f03f6f58331dbd108c36172b90314f60675b88dfefca25f704549164577796a1127fa407dd546aefa9f221d6c043e5b95298a0852ffd060b4fff117
  languageName: node
  linkType: hard

"@chevrotain/regexp-to-ast@npm:11.0.3":
  version: 11.0.3
  resolution: "@chevrotain/regexp-to-ast@npm:11.0.3"
  checksum: 5d665b3340493e302f245c9bbcd73de9b973ca79d0e59c4fbed6cc733b665998b41a2b8a5963bc2e90c763c8b4ba30f6e53736325c40f3fccef0ad3de2095ff2
  languageName: node
  linkType: hard

"@chevrotain/types@npm:11.0.3":
  version: 11.0.3
  resolution: "@chevrotain/types@npm:11.0.3"
  checksum: 4496bf1955f1db2b08c188f508db23d9f1cbecdf0bfa7f23f8d8dcd3f9ca450529b71acc83a941c59c0f8188b54c0f5687f6e203dcd7dca622ac4ea6291df316
  languageName: node
  linkType: hard

"@chevrotain/utils@npm:11.0.3":
  version: 11.0.3
  resolution: "@chevrotain/utils@npm:11.0.3"
  checksum: 099f0aa65ff82a7d49ffefd7a90182efcc1518b89b88d516d2125ca730eaa38d61e36ee40fad6c21f7896b6e8393b1e6810b6a69122fabff283f0522ee49eaa5
  languageName: node
  linkType: hard

"@codemirror/autocomplete@npm:^6.0.0, @codemirror/autocomplete@npm:^6.18.6, @codemirror/autocomplete@npm:^6.3.2, @codemirror/autocomplete@npm:^6.7.1":
  version: 6.18.6
  resolution: "@codemirror/autocomplete@npm:6.18.6"
  dependencies:
    "@codemirror/language": ^6.0.0
    "@codemirror/state": ^6.0.0
    "@codemirror/view": ^6.17.0
    "@lezer/common": ^1.0.0
  checksum: 1d3657d5fbd2bbf983edf7fb14568b1f813a15f03848bef3833835dd3a30985d881e093842f7b3def23789b542db4eb81ec07bfa313d1ee1d54cb1b273027dea
  languageName: node
  linkType: hard

"@codemirror/commands@npm:^6.8.1":
  version: 6.8.1
  resolution: "@codemirror/commands@npm:6.8.1"
  dependencies:
    "@codemirror/language": ^6.0.0
    "@codemirror/state": ^6.4.0
    "@codemirror/view": ^6.27.0
    "@lezer/common": ^1.1.0
  checksum: 838365af4f12e985c35f4bc59e38eb809e951fd3e35d5ad43548e61c26deda050276346dd031b9c6ed7fe13a777d59c37b9b1e46609d1d79e622d908340a468e
  languageName: node
  linkType: hard

"@codemirror/lang-cpp@npm:^6.0.2":
  version: 6.0.2
  resolution: "@codemirror/lang-cpp@npm:6.0.2"
  dependencies:
    "@codemirror/language": ^6.0.0
    "@lezer/cpp": ^1.0.0
  checksum: bb9eba482cca80037ce30c7b193cf45eff19ccbb773764fddf2071756468ecc25aa53c777c943635054f89095b0247b9b50c339e107e41e68d34d12a7295f9a9
  languageName: node
  linkType: hard

"@codemirror/lang-css@npm:^6.0.0, @codemirror/lang-css@npm:^6.3.1":
  version: 6.3.1
  resolution: "@codemirror/lang-css@npm:6.3.1"
  dependencies:
    "@codemirror/autocomplete": ^6.0.0
    "@codemirror/language": ^6.0.0
    "@codemirror/state": ^6.0.0
    "@lezer/common": ^1.0.2
    "@lezer/css": ^1.1.7
  checksum: ed175d75d75bc0a059d1e60b3dcd8464d570da14fc97388439943c9c43e1e9146e37b83fe2ccaad9cd387420b7b411ea1d24ede78ecd1f2045a38acbb4dd36bc
  languageName: node
  linkType: hard

"@codemirror/lang-html@npm:^6.0.0, @codemirror/lang-html@npm:^6.4.9":
  version: 6.4.9
  resolution: "@codemirror/lang-html@npm:6.4.9"
  dependencies:
    "@codemirror/autocomplete": ^6.0.0
    "@codemirror/lang-css": ^6.0.0
    "@codemirror/lang-javascript": ^6.0.0
    "@codemirror/language": ^6.4.0
    "@codemirror/state": ^6.0.0
    "@codemirror/view": ^6.17.0
    "@lezer/common": ^1.0.0
    "@lezer/css": ^1.1.0
    "@lezer/html": ^1.3.0
  checksum: ac8c3ceb0396f2e032752c5079bd950124dca708bc64e96fc147dc5fe7133e5cee0814fe951abdb953ec1d11fa540e4b30a712b5149d9a36016a197a28de45d7
  languageName: node
  linkType: hard

"@codemirror/lang-java@npm:^6.0.1":
  version: 6.0.1
  resolution: "@codemirror/lang-java@npm:6.0.1"
  dependencies:
    "@codemirror/language": ^6.0.0
    "@lezer/java": ^1.0.0
  checksum: 4679104683cbffcd224ac04c7e5d144b787494697b26470b07017259035b7bb3fa62609d9a61bfbc566f1756d9f972f9f26d96a3c1362dd48881c1172f9a914d
  languageName: node
  linkType: hard

"@codemirror/lang-javascript@npm:^6.0.0, @codemirror/lang-javascript@npm:^6.2.3":
  version: 6.2.3
  resolution: "@codemirror/lang-javascript@npm:6.2.3"
  dependencies:
    "@codemirror/autocomplete": ^6.0.0
    "@codemirror/language": ^6.6.0
    "@codemirror/lint": ^6.0.0
    "@codemirror/state": ^6.0.0
    "@codemirror/view": ^6.17.0
    "@lezer/common": ^1.0.0
    "@lezer/javascript": ^1.0.0
  checksum: c56407ddedc80e417dd105a39f11f837fad6fd4d91fe7934c61e48c54227350e4e8f940f81d26030a6c4ff9da16f734361cd1eaed63ba22aadf71fcf6172cbd5
  languageName: node
  linkType: hard

"@codemirror/lang-json@npm:^6.0.1":
  version: 6.0.1
  resolution: "@codemirror/lang-json@npm:6.0.1"
  dependencies:
    "@codemirror/language": ^6.0.0
    "@lezer/json": ^1.0.0
  checksum: e9e87d50ff7b81bd56a6ab50740b1dd54e9a93f1be585e1d59d0642e2148842ea1528ac7b7221eb4ddc7fe84bbc28065144cc3ab86f6e06c6aeb2d4b4e62acf1
  languageName: node
  linkType: hard

"@codemirror/lang-markdown@npm:^6.3.2":
  version: 6.3.2
  resolution: "@codemirror/lang-markdown@npm:6.3.2"
  dependencies:
    "@codemirror/autocomplete": ^6.7.1
    "@codemirror/lang-html": ^6.0.0
    "@codemirror/language": ^6.3.0
    "@codemirror/state": ^6.0.0
    "@codemirror/view": ^6.0.0
    "@lezer/common": ^1.2.1
    "@lezer/markdown": ^1.0.0
  checksum: f136d50156f13619d7ceb4fae28fc2342064be371a6cb057ba304658d885cf029d2d0d69b03b3c591c86a2c9b46bb2b3820549d5ff936a9b6aabaf692923c84a
  languageName: node
  linkType: hard

"@codemirror/lang-php@npm:^6.0.1":
  version: 6.0.1
  resolution: "@codemirror/lang-php@npm:6.0.1"
  dependencies:
    "@codemirror/lang-html": ^6.0.0
    "@codemirror/language": ^6.0.0
    "@codemirror/state": ^6.0.0
    "@lezer/common": ^1.0.0
    "@lezer/php": ^1.0.0
  checksum: c003a29a426486453fdfddbf7302982fa2aa7f059bf6f1ce4cbf08341b0162eee5e2f50e0d71c418dcd358491631780156d846fe352754d042576172c5d86721
  languageName: node
  linkType: hard

"@codemirror/lang-python@npm:^6.2.0":
  version: 6.2.0
  resolution: "@codemirror/lang-python@npm:6.2.0"
  dependencies:
    "@codemirror/autocomplete": ^6.3.2
    "@codemirror/language": ^6.8.0
    "@codemirror/state": ^6.0.0
    "@lezer/common": ^1.2.1
    "@lezer/python": ^1.1.4
  checksum: 2326932a59af8c56dc4e8a621f542a59944b93d4e5f50b23dab65d15486cc7a59a6f5fe11595ac478974270084db4770942621dbd834021247f34f2d34063757
  languageName: node
  linkType: hard

"@codemirror/lang-rust@npm:^6.0.1":
  version: 6.0.1
  resolution: "@codemirror/lang-rust@npm:6.0.1"
  dependencies:
    "@codemirror/language": ^6.0.0
    "@lezer/rust": ^1.0.0
  checksum: 8a439944cb22159b0b3465ca4fa4294c69843219d5d30e278ae6df8e48f30a7a9256129723c025ec9b5e694d31a3560fb004300b125ffcd81c22d13825845170
  languageName: node
  linkType: hard

"@codemirror/lang-sql@npm:^6.8.0":
  version: 6.8.0
  resolution: "@codemirror/lang-sql@npm:6.8.0"
  dependencies:
    "@codemirror/autocomplete": ^6.0.0
    "@codemirror/language": ^6.0.0
    "@codemirror/state": ^6.0.0
    "@lezer/common": ^1.2.0
    "@lezer/highlight": ^1.0.0
    "@lezer/lr": ^1.0.0
  checksum: 1b5a3c8129b09f24039d8c0906fc4cb8d0f706a424a1d56721057bd1e647797c2b1240bb53eed9bf2bac5806a4e0363e555a3963f04c478efa05829890c537f7
  languageName: node
  linkType: hard

"@codemirror/lang-wast@npm:^6.0.2":
  version: 6.0.2
  resolution: "@codemirror/lang-wast@npm:6.0.2"
  dependencies:
    "@codemirror/language": ^6.0.0
    "@lezer/common": ^1.2.0
    "@lezer/highlight": ^1.0.0
    "@lezer/lr": ^1.0.0
  checksum: 72119d4a7d726c54167aa227c982ae9fa785c8ad97a158d8350ae95eecfbd8028a803eef939f7e6c5c6e626fcecda1dc37e9dffc6d5d6ec105f686aeda6b2c24
  languageName: node
  linkType: hard

"@codemirror/lang-xml@npm:^6.1.0":
  version: 6.1.0
  resolution: "@codemirror/lang-xml@npm:6.1.0"
  dependencies:
    "@codemirror/autocomplete": ^6.0.0
    "@codemirror/language": ^6.4.0
    "@codemirror/state": ^6.0.0
    "@codemirror/view": ^6.0.0
    "@lezer/common": ^1.0.0
    "@lezer/xml": ^1.0.0
  checksum: 3a1b7af07b29ad7e53b77bf584245580b613bc92256059f175f2b1d7c28c4e39b75654fe169b9a8a330a60164b53ff5254bdb5b8ee8c6e6766427ee115c4e229
  languageName: node
  linkType: hard

"@codemirror/language@npm:^6.0.0":
  version: 6.11.0
  resolution: "@codemirror/language@npm:6.11.0"
  dependencies:
    "@codemirror/state": ^6.0.0
    "@codemirror/view": ^6.23.0
    "@lezer/common": ^1.1.0
    "@lezer/highlight": ^1.0.0
    "@lezer/lr": ^1.0.0
    style-mod: ^4.0.0
  checksum: 5556dc163d5bd1d771a4f64e2750d3d1dc1f39030bc6e4b9a4704e4de7501e8d3511002e0f8f96cd8deef782730e0b49b576e30f0ea820e1c632995bd75caddd
  languageName: node
  linkType: hard

"@codemirror/legacy-modes@npm:^6.5.1":
  version: 6.5.1
  resolution: "@codemirror/legacy-modes@npm:6.5.1"
  dependencies:
    "@codemirror/language": ^6.0.0
  checksum: ad92399fdd5f7342d2b8d1ef450ac01cee96f2266938ca09de5047998bf6ac7a085dfe9941feb9ef6a924fda80aa7a1dc0ddc5dd6ce9c3ceaa36bcc14c5b2264
  languageName: node
  linkType: hard

"@codemirror/lint@npm:^6.0.0":
  version: 6.2.0
  resolution: "@codemirror/lint@npm:6.2.0"
  dependencies:
    "@codemirror/state": ^6.0.0
    "@codemirror/view": ^6.0.0
    crelt: ^1.0.5
  checksum: b97e55a07bca9f7e357e495853ba189ae0ff7dfe7e7ae445d7a0d6c6926ec792c7f5c6b6c13a1f137fd9fedf44a6624e9d500f76d0d46a3c3e9d19c2cda9d28a
  languageName: node
  linkType: hard

"@codemirror/search@npm:^6.5.10":
  version: 6.5.10
  resolution: "@codemirror/search@npm:6.5.10"
  dependencies:
    "@codemirror/state": ^6.0.0
    "@codemirror/view": ^6.0.0
    crelt: ^1.0.5
  checksum: a30048004ce7cc1ee77a7618485ab5399939eab72b64329d57be98245ea39cb68ba54ad944ee679cc2aeac5bf9f202b2073ad0916f0788497fb64a07073399e8
  languageName: node
  linkType: hard

"@codemirror/state@npm:^6.2.0":
  version: 6.5.2
  resolution: "@codemirror/state@npm:6.5.2"
  dependencies:
    "@marijn/find-cluster-break": ^1.0.0
  checksum: 4473a79475070d73f2e72f2eaaee5b69d2833b5020faa9714609d95dd03f0e5ad02cad8031a541dcd748436842a300332a2925317b39ffa09e3b4831145d98bc
  languageName: node
  linkType: hard

"@codemirror/view@npm:^6.9.6":
  version: 6.36.6
  resolution: "@codemirror/view@npm:6.36.6"
  dependencies:
    "@codemirror/state": ^6.5.0
    style-mod: ^4.1.0
    w3c-keyname: ^2.2.4
  checksum: a36662e61743d379a4bc8f5a5e441b23d6612e029e4d4f494aec974adb81752488be2fc55a9105a8b6c0e846b4a26ed570d9d1df4577affb3fc7c63de9407a45
  languageName: node
  linkType: hard

"@cypress/request@npm:3.0.6":
  version: 3.0.6
  resolution: "@cypress/request@npm:3.0.6"
  dependencies:
    aws-sign2: ~0.7.0
    aws4: ^1.8.0
    caseless: ~0.12.0
    combined-stream: ~1.0.6
    extend: ~3.0.2
    forever-agent: ~0.6.1
    form-data: ~4.0.0
    http-signature: ~1.4.0
    is-typedarray: ~1.0.0
    isstream: ~0.1.2
    json-stringify-safe: ~5.0.1
    mime-types: ~2.1.19
    performance-now: ^2.1.0
    qs: 6.13.0
    safe-buffer: ^5.1.2
    tough-cookie: ^5.0.0
    tunnel-agent: ^0.6.0
    uuid: ^8.3.2
  checksum: 017e1898123eca7af4b95b89fa5a03ed6cb5e841b8ed926cb709b5ad88b5f55b713436e74bce6f13752f80d0399c01cd5b0b3212aaa972e064967f5c78237ebb
  languageName: node
  linkType: hard

"@discoveryjs/json-ext@npm:0.5.7, @discoveryjs/json-ext@npm:^0.5.0":
  version: 0.5.7
  resolution: "@discoveryjs/json-ext@npm:0.5.7"
  checksum: 2176d301cc258ea5c2324402997cf8134ebb212469c0d397591636cea8d3c02f2b3cf9fd58dcb748c7a0dade77ebdc1b10284fa63e608c033a1db52fddc69918
  languageName: node
  linkType: hard

"@fortawesome/fontawesome-free@npm:^5.12.0":
  version: 5.15.4
  resolution: "@fortawesome/fontawesome-free@npm:5.15.4"
  checksum: 32281c3df4075290d9a96dfc22f72fadb3da7055d4117e48d34046b8c98032a55fa260ae351b0af5d6f6fb57a2f5d79a4abe52af456da35195f7cb7dda27b4a2
  languageName: node
  linkType: hard

"@gar/promisify@npm:^1.1.3":
  version: 1.1.3
  resolution: "@gar/promisify@npm:1.1.3"
  checksum: 4059f790e2d07bf3c3ff3e0fec0daa8144fe35c1f6e0111c9921bd32106adaa97a4ab096ad7dab1e28ee6a9060083c4d1a4ada42a7f5f3f7a96b8812e2b757c1
  languageName: node
  linkType: hard

"@iconify/types@npm:^2.0.0":
  version: 2.0.0
  resolution: "@iconify/types@npm:2.0.0"
  checksum: 029f58542c160e9d4a746869cf2e475b603424d3adf3994c5cc8d0406c47e6e04a3b898b2707840c1c5b9bd5563a1660a34b110d89fce43923baca5222f4e597
  languageName: node
  linkType: hard

"@iconify/utils@npm:^2.1.33":
  version: 2.3.0
  resolution: "@iconify/utils@npm:2.3.0"
  dependencies:
    "@antfu/install-pkg": ^1.0.0
    "@antfu/utils": ^8.1.0
    "@iconify/types": ^2.0.0
    debug: ^4.4.0
    globals: ^15.14.0
    kolorist: ^1.8.0
    local-pkg: ^1.0.0
    mlly: ^1.7.4
  checksum: b723397c09bdfd116c907db714fd1cd65119283e06d008502d9389593b14520c7084474b2e0b0bae2d46eabc3be2146ab02fba81c9ad199a33b6f1394e23890c
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: ^5.1.2
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: ^7.0.1
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: ^8.1.0
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 4a473b9b32a7d4d3cfb7a614226e555091ff0c5a29a1734c28c72a182c2f6699b26fc6b5c2131dfd841e86b185aea714c72201d7c98c2fba5f17709333a67aeb
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.5":
  version: 0.3.5
  resolution: "@jridgewell/gen-mapping@npm:0.3.5"
  dependencies:
    "@jridgewell/set-array": ^1.2.1
    "@jridgewell/sourcemap-codec": ^1.4.10
    "@jridgewell/trace-mapping": ^0.3.24
  checksum: ff7a1764ebd76a5e129c8890aa3e2f46045109dabde62b0b6c6a250152227647178ff2069ea234753a690d8f3c4ac8b5e7b267bbee272bffb7f3b0a370ab6e52
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.0
  resolution: "@jridgewell/resolve-uri@npm:3.1.0"
  checksum: b5ceaaf9a110fcb2780d1d8f8d4a0bfd216702f31c988d8042e5f8fbe353c55d9b0f55a1733afdc64806f8e79c485d2464680ac48a0d9fcadb9548ee6b81d267
  languageName: node
  linkType: hard

"@jridgewell/set-array@npm:^1.2.1":
  version: 1.2.1
  resolution: "@jridgewell/set-array@npm:1.2.1"
  checksum: 832e513a85a588f8ed4f27d1279420d8547743cc37fcad5a5a76fc74bb895b013dfe614d0eed9cb860048e6546b798f8f2652020b4b2ba0561b05caa8c654b10
  languageName: node
  linkType: hard

"@jridgewell/source-map@npm:^0.3.3":
  version: 0.3.6
  resolution: "@jridgewell/source-map@npm:0.3.6"
  dependencies:
    "@jridgewell/gen-mapping": ^0.3.5
    "@jridgewell/trace-mapping": ^0.3.25
  checksum: c9dc7d899397df95e3c9ec287b93c0b56f8e4453cd20743e2b9c8e779b1949bc3cccf6c01bb302779e46560eb45f62ea38d19fedd25370d814734268450a9f30
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10, @jridgewell/sourcemap-codec@npm:^1.4.14":
  version: 1.4.14
  resolution: "@jridgewell/sourcemap-codec@npm:1.4.14"
  checksum: 61100637b6d173d3ba786a5dff019e1a74b1f394f323c1fee337ff390239f053b87266c7a948777f4b1ee68c01a8ad0ab61e5ff4abb5a012a0b091bec391ab97
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.20, @jridgewell/trace-mapping@npm:^0.3.24, @jridgewell/trace-mapping@npm:^0.3.25":
  version: 0.3.25
  resolution: "@jridgewell/trace-mapping@npm:0.3.25"
  dependencies:
    "@jridgewell/resolve-uri": ^3.1.0
    "@jridgewell/sourcemap-codec": ^1.4.14
  checksum: 9d3c40d225e139987b50c48988f8717a54a8c994d8a948ee42e1412e08988761d0754d7d10b803061cc3aebf35f92a5dbbab493bd0e1a9ef9e89a2130e83ba34
  languageName: node
  linkType: hard

"@jupyter/react-components@npm:^0.16.6":
  version: 0.16.6
  resolution: "@jupyter/react-components@npm:0.16.6"
  dependencies:
    "@jupyter/web-components": ^0.16.6
    react: ">=17.0.0 <19.0.0"
  checksum: 4619ae8a41a987aa7529d8625d1b2f7921f977091e710f47a6c60190ef49c15ac9849289ffcf0327d18ef2d0493959c7aac7010a205f9328569301a248b6ecfb
  languageName: node
  linkType: hard

"@jupyter/web-components@npm:^0.16.6":
  version: 0.16.6
  resolution: "@jupyter/web-components@npm:0.16.6"
  dependencies:
    "@microsoft/fast-colors": ^5.3.1
    "@microsoft/fast-element": ^1.12.0
    "@microsoft/fast-foundation": ^2.49.4
    "@microsoft/fast-web-utilities": ^5.4.1
  checksum: 41a4a2b5bb2177cbb562c858c8327a868eab9205679dbc8f99105b1580882a2d14a6e2bd9202a4acdec539c44cee8d5a5b64f7fea6a03baa3ad2905b95a6538c
  languageName: node
  linkType: hard

"@jupyter/ydoc@npm:^3.0.0-a3":
  version: 3.0.4
  resolution: "@jupyter/ydoc@npm:3.0.4"
  dependencies:
    "@jupyterlab/nbformat": ^3.0.0 || ^4.0.0-alpha.21 || ^4.0.0
    "@lumino/coreutils": ^1.11.0 || ^2.0.0
    "@lumino/disposable": ^1.10.0 || ^2.0.0
    "@lumino/signaling": ^1.10.0 || ^2.0.0
    y-protocols: ^1.0.5
    yjs: ^13.5.40
  checksum: 85ca033a51c0f26080bcea7c0aac7cbd4ef66bc745fd48786aa1a2f9bdf06b99b67f40d8775ff04bb700e78782fbfdc6c97d2e94b45bd65ad5288c44ca158e19
  languageName: node
  linkType: hard

"@jupyterlab/application-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/application-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/property-inspector": ^4.4.4
    "@jupyterlab/settingregistry": ^4.4.4
    "@jupyterlab/statedb": ^4.4.4
    "@jupyterlab/statusbar": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/algorithm": ^2.0.3
    "@lumino/commands": ^2.3.2
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/widgets": ^2.7.1
    react: ^18.2.0
  checksum: 36af271b5374b4e74bf43d45c45e60a0234bbcb0a89f61b0cb2c2a0187d383401146c0f18cce2c98939e20fb9e7b9d1c5790999abd23c0659f745be1e3fc1ec4
  languageName: node
  linkType: hard

"@jupyterlab/application-top@workspace:.":
  version: 0.0.0-use.local
  resolution: "@jupyterlab/application-top@workspace:."
  dependencies:
    "@jupyterlab/application": ~4.4.4
    "@jupyterlab/application-extension": ~4.4.4
    "@jupyterlab/apputils-extension": ~4.4.4
    "@jupyterlab/builder": ^4.4.4
    "@jupyterlab/buildutils": ^4.4.4
    "@jupyterlab/cell-toolbar-extension": ~4.4.4
    "@jupyterlab/celltags-extension": ~4.4.4
    "@jupyterlab/codemirror-extension": ~4.4.4
    "@jupyterlab/completer-extension": ~4.4.4
    "@jupyterlab/console-extension": ~4.4.4
    "@jupyterlab/coreutils": ~6.4.4
    "@jupyterlab/csvviewer-extension": ~4.4.4
    "@jupyterlab/debugger-extension": ~4.4.4
    "@jupyterlab/docmanager-extension": ~4.4.4
    "@jupyterlab/documentsearch-extension": ~4.4.4
    "@jupyterlab/extensionmanager-extension": ~4.4.4
    "@jupyterlab/filebrowser-extension": ~4.4.4
    "@jupyterlab/fileeditor-extension": ~4.4.4
    "@jupyterlab/help-extension": ~4.4.4
    "@jupyterlab/htmlviewer-extension": ~4.4.4
    "@jupyterlab/hub-extension": ~4.4.4
    "@jupyterlab/imageviewer-extension": ~4.4.4
    "@jupyterlab/inspector-extension": ~4.4.4
    "@jupyterlab/javascript-extension": ~4.4.4
    "@jupyterlab/json-extension": ~4.4.4
    "@jupyterlab/launcher-extension": ~4.4.4
    "@jupyterlab/logconsole-extension": ~4.4.4
    "@jupyterlab/lsp-extension": ~4.4.4
    "@jupyterlab/mainmenu-extension": ~4.4.4
    "@jupyterlab/markdownviewer-extension": ~4.4.4
    "@jupyterlab/markedparser-extension": ~4.4.4
    "@jupyterlab/mathjax-extension": ~4.4.4
    "@jupyterlab/mermaid-extension": ~4.4.4
    "@jupyterlab/metadataform-extension": ~4.4.4
    "@jupyterlab/notebook-extension": ~4.4.4
    "@jupyterlab/pdf-extension": ~4.4.4
    "@jupyterlab/pluginmanager-extension": ~4.4.4
    "@jupyterlab/rendermime-extension": ~4.4.4
    "@jupyterlab/running-extension": ~4.4.4
    "@jupyterlab/services-extension": ~4.4.4
    "@jupyterlab/settingeditor-extension": ~4.4.4
    "@jupyterlab/shortcuts-extension": ~5.2.4
    "@jupyterlab/statusbar-extension": ~4.4.4
    "@jupyterlab/terminal-extension": ~4.4.4
    "@jupyterlab/theme-dark-extension": ~4.4.4
    "@jupyterlab/theme-dark-high-contrast-extension": ~4.4.4
    "@jupyterlab/theme-light-extension": ~4.4.4
    "@jupyterlab/toc-extension": ~6.4.4
    "@jupyterlab/tooltip-extension": ~4.4.4
    "@jupyterlab/translation-extension": ~4.4.4
    "@jupyterlab/ui-components-extension": ~4.4.4
    "@jupyterlab/vega5-extension": ~4.4.4
    "@jupyterlab/workspaces-extension": ~4.4.4
    chokidar: ^3.4.0
    css-loader: ^6.7.1
    duplicate-package-checker-webpack-plugin: ^3.0.0
    fs-extra: ^10.1.0
    glob: ~7.1.6
    handlebars: ^4.7.8
    html-loader: ~1.3.0
    html-webpack-plugin: ^5.5.0
    license-webpack-plugin: ^4.0.2
    mini-css-extract-plugin: ^2.7.0
    mini-svg-data-uri: ^1.4.4
    rimraf: ~5.0.5
    sort-package-json: ~1.53.1
    source-map-loader: ~1.0.2
    style-loader: ~3.3.1
    terser-webpack-plugin: ^5.3.7
    webpack: ^5.76.1
    webpack-bundle-analyzer: ^4.8.0
    webpack-cli: ^5.0.1
    webpack-merge: ^5.8.0
    worker-loader: ^3.0.2
  languageName: unknown
  linkType: soft

"@jupyterlab/application@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/application@npm:4.4.4"
  dependencies:
    "@fortawesome/fontawesome-free": ^5.12.0
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/docregistry": ^4.4.4
    "@jupyterlab/rendermime": ^4.4.4
    "@jupyterlab/rendermime-interfaces": ^3.12.4
    "@jupyterlab/services": ^7.4.4
    "@jupyterlab/statedb": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/algorithm": ^2.0.3
    "@lumino/application": ^2.4.4
    "@lumino/commands": ^2.3.2
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/messaging": ^2.0.3
    "@lumino/polling": ^2.1.4
    "@lumino/properties": ^2.0.3
    "@lumino/signaling": ^2.1.4
    "@lumino/widgets": ^2.7.1
  checksum: 2fa77fdaccee449b029c36b5a07500c39da11aff46e8ef2b6ee59b8518d628c71053b8c06beae12b4745b0af698074a0d9909ed1e8211d49134e8c4b88da622b
  languageName: node
  linkType: hard

"@jupyterlab/apputils-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/apputils-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/docregistry": ^4.4.4
    "@jupyterlab/mainmenu": ^4.4.4
    "@jupyterlab/rendermime-interfaces": ^3.12.4
    "@jupyterlab/services": ^7.4.4
    "@jupyterlab/settingregistry": ^4.4.4
    "@jupyterlab/statedb": ^4.4.4
    "@jupyterlab/statusbar": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@jupyterlab/workspaces": ^4.4.4
    "@lumino/algorithm": ^2.0.3
    "@lumino/commands": ^2.3.2
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/domutils": ^2.0.3
    "@lumino/polling": ^2.1.4
    "@lumino/widgets": ^2.7.1
    react: ^18.2.0
    react-dom: ^18.2.0
    react-toastify: ^9.0.8
  checksum: 695c62438f08946189dd2b790d567458e94e6fd5e8f02b0857c7b9bf568451071d28c9afa9abbea598a099714fe810a3ce28ac2a4a0c96f599319067610d5712
  languageName: node
  linkType: hard

"@jupyterlab/apputils@npm:~4.5.4":
  version: 4.5.4
  resolution: "@jupyterlab/apputils@npm:4.5.4"
  dependencies:
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/observables": ^5.4.4
    "@jupyterlab/rendermime-interfaces": ^3.12.4
    "@jupyterlab/services": ^7.4.4
    "@jupyterlab/settingregistry": ^4.4.4
    "@jupyterlab/statedb": ^4.4.4
    "@jupyterlab/statusbar": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/algorithm": ^2.0.3
    "@lumino/commands": ^2.3.2
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/domutils": ^2.0.3
    "@lumino/messaging": ^2.0.3
    "@lumino/signaling": ^2.1.4
    "@lumino/virtualdom": ^2.0.3
    "@lumino/widgets": ^2.7.1
    "@types/react": ^18.0.26
    react: ^18.2.0
    sanitize-html: ~2.12.1
  checksum: 7fe4506f7c105c9f4b7d36c5e20c57ef33722bccf6b262b334eea86b7660070620978c0adddd1007911066ce2a14dfc22502f1efd4a33e911042720ae652cdd5
  languageName: node
  linkType: hard

"@jupyterlab/attachments@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/attachments@npm:4.4.4"
  dependencies:
    "@jupyterlab/nbformat": ^4.4.4
    "@jupyterlab/observables": ^5.4.4
    "@jupyterlab/rendermime": ^4.4.4
    "@jupyterlab/rendermime-interfaces": ^3.12.4
    "@lumino/disposable": ^2.1.4
    "@lumino/signaling": ^2.1.4
  checksum: a258bd8a953d555341c2bd4b44dd902e5297885a7ba75439b8039081db51e546e29aecdee8689ed159eed20689a12b67036dbe700736792f6382be3977cc38e7
  languageName: node
  linkType: hard

"@jupyterlab/builder@npm:^4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/builder@npm:4.4.4"
  dependencies:
    "@lumino/algorithm": ^2.0.3
    "@lumino/application": ^2.4.4
    "@lumino/commands": ^2.3.2
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/domutils": ^2.0.3
    "@lumino/dragdrop": ^2.1.6
    "@lumino/messaging": ^2.0.3
    "@lumino/properties": ^2.0.3
    "@lumino/signaling": ^2.1.4
    "@lumino/virtualdom": ^2.0.3
    "@lumino/widgets": ^2.7.1
    ajv: ^8.12.0
    commander: ^9.4.1
    css-loader: ^6.7.1
    duplicate-package-checker-webpack-plugin: ^3.0.0
    fs-extra: ^10.1.0
    glob: ~7.1.6
    license-webpack-plugin: ^2.3.14
    mini-css-extract-plugin: ^2.7.0
    mini-svg-data-uri: ^1.4.4
    path-browserify: ^1.0.0
    process: ^0.11.10
    source-map-loader: ~1.0.2
    style-loader: ~3.3.1
    supports-color: ^7.2.0
    terser-webpack-plugin: ^5.3.7
    webpack: ^5.76.1
    webpack-cli: ^5.0.1
    webpack-merge: ^5.8.0
    worker-loader: ^3.0.2
  bin:
    build-labextension: lib/build-labextension.js
  checksum: 7b9689a55212799b7945d2fdcf84bc0a63bfe321733afebaeff3c5de347aac1adfee596de2600c10c27c6c2cfd6c8cc394d90f2d8cc795baab38ff78c4f37ea6
  languageName: node
  linkType: hard

"@jupyterlab/buildutils@npm:^4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/buildutils@npm:4.4.4"
  dependencies:
    "@yarnpkg/core": ^4.0.3
    "@yarnpkg/parsers": ^3.0.0
    commander: ^9.4.1
    crypto: ~1.0.1
    dependency-graph: ^0.11.0
    fs-extra: ^10.1.0
    glob: ~7.1.6
    inquirer: ^9.1.4
    micromatch: ^4.0.2
    minimatch: ~3.0.4
    os: ~0.1.1
    package-json: ^7.0.0
    prettier: ~2.6.0
    process: ^0.11.10
    semver: ^7.5.2
    sort-package-json: ~1.53.1
    typescript: ~5.5.4
    verdaccio: ^5.33.0
  bin:
    get-dependency: lib/get-dependency.js
    local-repository: lib/local-repository.js
    remove-dependency: lib/remove-dependency.js
    update-dependency: lib/update-dependency.js
    update-dist-tag: lib/update-dist-tag.js
    update-staging-lock: lib/update-staging-lock.js
  checksum: 606d20b8f76be672b29c7df281b5f12e2ae90e9b43cfa52c92bfe6f0c2eccbc09db669362ff69381632d0ac14cb4add62d9bd6710158563ef84d71096c320fd5
  languageName: node
  linkType: hard

"@jupyterlab/cell-toolbar-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/cell-toolbar-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/cell-toolbar": ^4.4.4
    "@jupyterlab/settingregistry": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
  checksum: 1b564fb16d7d7a37b7514c95d8f9ece3b1dfacf2e29bf1707b74cf77e58aa7062702d963771d459dc99794d1d587a6e4c182fedd4faea71dbba5d669f6357635
  languageName: node
  linkType: hard

"@jupyterlab/cell-toolbar@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/cell-toolbar@npm:4.4.4"
  dependencies:
    "@jupyter/ydoc": ^3.0.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/cells": ^4.4.4
    "@jupyterlab/docregistry": ^4.4.4
    "@jupyterlab/notebook": ^4.4.4
    "@jupyterlab/observables": ^5.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/algorithm": ^2.0.3
    "@lumino/commands": ^2.3.2
    "@lumino/disposable": ^2.1.4
    "@lumino/signaling": ^2.1.4
    "@lumino/widgets": ^2.7.1
  checksum: 45f0f30557fc15a4f8609db3dfba2dd54325aaf98e48df77ca1c310f4a7a7ba90a89e5dea47d1ded75132db1c71cca568a48b1f28f351e35b482d851241544d9
  languageName: node
  linkType: hard

"@jupyterlab/cells@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/cells@npm:4.4.4"
  dependencies:
    "@codemirror/state": ^6.5.2
    "@codemirror/view": ^6.36.6
    "@jupyter/ydoc": ^3.0.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/attachments": ^4.4.4
    "@jupyterlab/codeeditor": ^4.4.4
    "@jupyterlab/codemirror": ^4.4.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/documentsearch": ^4.4.4
    "@jupyterlab/filebrowser": ^4.4.4
    "@jupyterlab/nbformat": ^4.4.4
    "@jupyterlab/observables": ^5.4.4
    "@jupyterlab/outputarea": ^4.4.4
    "@jupyterlab/rendermime": ^4.4.4
    "@jupyterlab/services": ^7.4.4
    "@jupyterlab/toc": ^6.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/algorithm": ^2.0.3
    "@lumino/coreutils": ^2.2.1
    "@lumino/domutils": ^2.0.3
    "@lumino/dragdrop": ^2.1.6
    "@lumino/messaging": ^2.0.3
    "@lumino/polling": ^2.1.4
    "@lumino/signaling": ^2.1.4
    "@lumino/virtualdom": ^2.0.3
    "@lumino/widgets": ^2.7.1
    react: ^18.2.0
  checksum: 8a5f45cb08c417566809ec0f58196062b2b26e801bbeaa5cb919b0f710086317c9745973a8c5d3b333bdaba0fd91dfeabacdd874cb44deaf4b5e68feebeb7820
  languageName: node
  linkType: hard

"@jupyterlab/celltags-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/celltags-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/notebook": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/algorithm": ^2.0.3
    "@rjsf/utils": ^5.13.4
    react: ^18.2.0
  checksum: 3387bc8992a97c6c37e856d6f1e591b3a96cb32a06affa81ab571451b60f45e965c84e7b3d3dec39cd36021c96a1e4164077d95253a62796c6711cc1440c82be
  languageName: node
  linkType: hard

"@jupyterlab/codeeditor@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/codeeditor@npm:4.4.4"
  dependencies:
    "@codemirror/state": ^6.5.2
    "@jupyter/ydoc": ^3.0.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/nbformat": ^4.4.4
    "@jupyterlab/observables": ^5.4.4
    "@jupyterlab/statusbar": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/dragdrop": ^2.1.6
    "@lumino/messaging": ^2.0.3
    "@lumino/signaling": ^2.1.4
    "@lumino/widgets": ^2.7.1
    react: ^18.2.0
  checksum: 5ba5ce9015297a019df22fe33ad94d399d2352a3b4e8f932d684fe4bbff4c65c8d96afb3c9015eaafa623aa1d237bc36668ffe848832ef631ff972b1e852d3f4
  languageName: node
  linkType: hard

"@jupyterlab/codemirror-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/codemirror-extension@npm:4.4.4"
  dependencies:
    "@codemirror/commands": ^6.8.1
    "@codemirror/lang-markdown": ^6.3.2
    "@codemirror/language": ^6.11.0
    "@codemirror/legacy-modes": ^6.5.1
    "@codemirror/search": ^6.5.10
    "@codemirror/view": ^6.36.6
    "@jupyter/ydoc": ^3.0.4
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/codeeditor": ^4.4.4
    "@jupyterlab/codemirror": ^4.4.4
    "@jupyterlab/settingregistry": ^4.4.4
    "@jupyterlab/statusbar": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/coreutils": ^2.2.1
    "@lumino/widgets": ^2.7.1
    "@rjsf/utils": ^5.13.4
    "@rjsf/validator-ajv8": ^5.13.4
    react: ^18.2.0
  checksum: 437b6314f63d976f14273695f742133267d2eef4955d8b86acc989751711125e2c3fb6401cec9083a03c7594c74c0e1cf356f10fb95a8e1b020693148b7b7527
  languageName: node
  linkType: hard

"@jupyterlab/codemirror@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/codemirror@npm:4.4.4"
  dependencies:
    "@codemirror/autocomplete": ^6.18.6
    "@codemirror/commands": ^6.8.1
    "@codemirror/lang-cpp": ^6.0.2
    "@codemirror/lang-css": ^6.3.1
    "@codemirror/lang-html": ^6.4.9
    "@codemirror/lang-java": ^6.0.1
    "@codemirror/lang-javascript": ^6.2.3
    "@codemirror/lang-json": ^6.0.1
    "@codemirror/lang-markdown": ^6.3.2
    "@codemirror/lang-php": ^6.0.1
    "@codemirror/lang-python": ^6.2.0
    "@codemirror/lang-rust": ^6.0.1
    "@codemirror/lang-sql": ^6.8.0
    "@codemirror/lang-wast": ^6.0.2
    "@codemirror/lang-xml": ^6.1.0
    "@codemirror/language": ^6.11.0
    "@codemirror/legacy-modes": ^6.5.1
    "@codemirror/search": ^6.5.10
    "@codemirror/state": ^6.5.2
    "@codemirror/view": ^6.36.6
    "@jupyter/ydoc": ^3.0.4
    "@jupyterlab/codeeditor": ^4.4.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/documentsearch": ^4.4.4
    "@jupyterlab/nbformat": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@lezer/common": ^1.2.1
    "@lezer/generator": ^1.7.0
    "@lezer/highlight": ^1.2.0
    "@lezer/markdown": ^1.3.0
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/signaling": ^2.1.4
    yjs: ^13.5.40
  checksum: eda449dbd891dcb34285621683939d176bb5474bb45aab268c0aae93719508258a5f859df18ff4fcd6c2966f6840869730ec11dde7aa28bb1580ca691d86bf07
  languageName: node
  linkType: hard

"@jupyterlab/completer-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/completer-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/codeeditor": ^4.4.4
    "@jupyterlab/completer": ^4.4.4
    "@jupyterlab/settingregistry": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/commands": ^2.3.2
    "@lumino/coreutils": ^2.2.1
    "@rjsf/utils": ^5.13.4
    react: ^18.2.0
  checksum: 18450066e59d005017cc2156d749fc9627a516afe4aa2753f29b690bde3a55dd992f62596af1b3ae734ef6a592ccb77f51d59d0a34330a9d4cdc66834d09e76c
  languageName: node
  linkType: hard

"@jupyterlab/completer@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/completer@npm:4.4.4"
  dependencies:
    "@codemirror/state": ^6.5.2
    "@codemirror/view": ^6.36.6
    "@jupyter/ydoc": ^3.0.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/codeeditor": ^4.4.4
    "@jupyterlab/codemirror": ^4.4.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/rendermime": ^4.4.4
    "@jupyterlab/services": ^7.4.4
    "@jupyterlab/settingregistry": ^4.4.4
    "@jupyterlab/statedb": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/algorithm": ^2.0.3
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/domutils": ^2.0.3
    "@lumino/messaging": ^2.0.3
    "@lumino/signaling": ^2.1.4
    "@lumino/widgets": ^2.7.1
  checksum: e5f2421c45e02cd25627412da4cd030df4ddedfc348aad9c7e0f15f72af6e22a3d9e532a56cb85172476c81eb8753be68f3feab1d85107498406b8371f9a63f4
  languageName: node
  linkType: hard

"@jupyterlab/console-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/console-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/codeeditor": ^4.4.4
    "@jupyterlab/completer": ^4.4.4
    "@jupyterlab/console": ^4.4.4
    "@jupyterlab/filebrowser": ^4.4.4
    "@jupyterlab/launcher": ^4.4.4
    "@jupyterlab/mainmenu": ^4.4.4
    "@jupyterlab/rendermime": ^4.4.4
    "@jupyterlab/settingregistry": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/algorithm": ^2.0.3
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/properties": ^2.0.3
    "@lumino/widgets": ^2.7.1
  checksum: b8b2da1adb040f1e2a9a46d0049b65b940ea2b47de973fc4a2dbf8b50d8958e01118ebb5a1f4d619ed854eea1b7a070077325dfd7cf9121dc863831a05885b43
  languageName: node
  linkType: hard

"@jupyterlab/console@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/console@npm:4.4.4"
  dependencies:
    "@jupyter/ydoc": ^3.0.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/cells": ^4.4.4
    "@jupyterlab/codeeditor": ^4.4.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/nbformat": ^4.4.4
    "@jupyterlab/observables": ^5.4.4
    "@jupyterlab/rendermime": ^4.4.4
    "@jupyterlab/services": ^7.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/dragdrop": ^2.1.6
    "@lumino/messaging": ^2.0.3
    "@lumino/signaling": ^2.1.4
    "@lumino/widgets": ^2.7.1
  checksum: 6cc5f2785271ef3d7ae35bf6767289445772cc9d3d7bf9ddbda0664442b4af766cdb2f8846f92dfcf7b9c686f1377c67dbe75a91c45019c2715e18a25d26fdb3
  languageName: node
  linkType: hard

"@jupyterlab/coreutils@npm:~6.4.4":
  version: 6.4.4
  resolution: "@jupyterlab/coreutils@npm:6.4.4"
  dependencies:
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/signaling": ^2.1.4
    minimist: ~1.2.0
    path-browserify: ^1.0.0
    url-parse: ~1.5.4
  checksum: 00e56eeb06f61a0dca4dbf0c6b45c11a64473c1f5040c24b6a47012758155e21700c727f3862e70274406749ac5b63e5585173186ef8911fe42e7f2e614b3604
  languageName: node
  linkType: hard

"@jupyterlab/csvviewer-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/csvviewer-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/csvviewer": ^4.4.4
    "@jupyterlab/docregistry": ^4.4.4
    "@jupyterlab/documentsearch": ^4.4.4
    "@jupyterlab/mainmenu": ^4.4.4
    "@jupyterlab/observables": ^5.4.4
    "@jupyterlab/settingregistry": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@lumino/datagrid": ^2.5.2
    "@lumino/widgets": ^2.7.1
  checksum: 0fe7b4de2f09ee822c8df90eb207c38f81f5dc5c3a9e5baa29ca6e95ec048fd8fb4b493c18ba87c65a5bc104514fb015d27541c25f4d6e4d83c236f910270907
  languageName: node
  linkType: hard

"@jupyterlab/csvviewer@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/csvviewer@npm:4.4.4"
  dependencies:
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/docregistry": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/coreutils": ^2.2.1
    "@lumino/datagrid": ^2.5.2
    "@lumino/disposable": ^2.1.4
    "@lumino/messaging": ^2.0.3
    "@lumino/signaling": ^2.1.4
    "@lumino/widgets": ^2.7.1
  checksum: 3090b3a8db35909384548393db3aca34b3908022363ff17dff423796ab39fdb9d3597cd4d39677464160099e2b38a011ef1ccf67b5869945a9fc6882b15f8b32
  languageName: node
  linkType: hard

"@jupyterlab/debugger-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/debugger-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/cells": ^4.4.4
    "@jupyterlab/codeeditor": ^4.4.4
    "@jupyterlab/console": ^4.4.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/debugger": ^4.4.4
    "@jupyterlab/docregistry": ^4.4.4
    "@jupyterlab/fileeditor": ^4.4.4
    "@jupyterlab/logconsole": ^4.4.4
    "@jupyterlab/notebook": ^4.4.4
    "@jupyterlab/rendermime": ^4.4.4
    "@jupyterlab/services": ^7.4.4
    "@jupyterlab/settingregistry": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@lumino/commands": ^2.3.2
  checksum: e17e982823f5ea60c6b567c22a33117ceef6bfb177030f3ee0047b3b5301ed8201d4a450d44e7f1577538c0782905b08e3ef87113c16398404e3ecdb9cfcb5b8
  languageName: node
  linkType: hard

"@jupyterlab/debugger@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/debugger@npm:4.4.4"
  dependencies:
    "@codemirror/state": ^6.5.2
    "@codemirror/view": ^6.36.6
    "@jupyter/react-components": ^0.16.6
    "@jupyter/ydoc": ^3.0.4
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/cells": ^4.4.4
    "@jupyterlab/codeeditor": ^4.4.4
    "@jupyterlab/codemirror": ^4.4.4
    "@jupyterlab/console": ^4.4.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/docregistry": ^4.4.4
    "@jupyterlab/fileeditor": ^4.4.4
    "@jupyterlab/notebook": ^4.4.4
    "@jupyterlab/observables": ^5.4.4
    "@jupyterlab/rendermime": ^4.4.4
    "@jupyterlab/services": ^7.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/algorithm": ^2.0.3
    "@lumino/commands": ^2.3.2
    "@lumino/coreutils": ^2.2.1
    "@lumino/datagrid": ^2.5.2
    "@lumino/disposable": ^2.1.4
    "@lumino/messaging": ^2.0.3
    "@lumino/polling": ^2.1.4
    "@lumino/signaling": ^2.1.4
    "@lumino/widgets": ^2.7.1
    "@vscode/debugprotocol": ^1.51.0
    react: ^18.2.0
  checksum: dab06142e0cfcaed0987e653191ad32214058dfd60c97d35edea54f4cc1707403b001354e580c4c3fb3a83c8b113f21a4a05f922cb6ecab76feef8ffe3c01b31
  languageName: node
  linkType: hard

"@jupyterlab/docmanager-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/docmanager-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/docmanager": ^4.4.4
    "@jupyterlab/docregistry": ^4.4.4
    "@jupyterlab/services": ^7.4.4
    "@jupyterlab/settingregistry": ^4.4.4
    "@jupyterlab/statedb": ^4.4.4
    "@jupyterlab/statusbar": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/algorithm": ^2.0.3
    "@lumino/commands": ^2.3.2
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/signaling": ^2.1.4
    "@lumino/widgets": ^2.7.1
    react: ^18.2.0
  checksum: f53fd70927c0752cca3126d346886c6fd330ad0141778043c47850649b4b77b0fe59ea52ff224c1dacd63c168a7fa96847bed68bce9ba51585f91e540831ebb8
  languageName: node
  linkType: hard

"@jupyterlab/docmanager@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/docmanager@npm:4.4.4"
  dependencies:
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/docregistry": ^4.4.4
    "@jupyterlab/services": ^7.4.4
    "@jupyterlab/statedb": ^4.4.4
    "@jupyterlab/statusbar": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/algorithm": ^2.0.3
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/messaging": ^2.0.3
    "@lumino/polling": ^2.1.4
    "@lumino/properties": ^2.0.3
    "@lumino/signaling": ^2.1.4
    "@lumino/widgets": ^2.7.1
    react: ^18.2.0
  checksum: eb7e27ff4e25cfe03f6d8c80fcba59f2b6bbaab0e4f7fcc279e15addc2283635854196ca9210fc4f4bb671c9363de1fa78207f58c659fd58e30641cd1f70d8b0
  languageName: node
  linkType: hard

"@jupyterlab/docregistry@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/docregistry@npm:4.4.4"
  dependencies:
    "@jupyter/ydoc": ^3.0.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/codeeditor": ^4.4.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/observables": ^5.4.4
    "@jupyterlab/rendermime": ^4.4.4
    "@jupyterlab/rendermime-interfaces": ^3.12.4
    "@jupyterlab/services": ^7.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/algorithm": ^2.0.3
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/messaging": ^2.0.3
    "@lumino/properties": ^2.0.3
    "@lumino/signaling": ^2.1.4
    "@lumino/widgets": ^2.7.1
    react: ^18.2.0
  checksum: 062351e5606b0bf94c79bc6bdde2aa794dd2872dc675421f2de102bb7351c660db2c9255e0e7e025b5b08c9eb85df0059a50848944868d6e7c5cc5b17306584b
  languageName: node
  linkType: hard

"@jupyterlab/documentsearch-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/documentsearch-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/documentsearch": ^4.4.4
    "@jupyterlab/settingregistry": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@lumino/commands": ^2.3.2
    "@lumino/widgets": ^2.7.1
  checksum: 46aa86e818ed28debf5e4e6dc66cabbe60e86deca934e76dd7397d3f6faf24fc64ab93e87bbc51274dea289966a3c4177f20e2e7e3812a0393cf7e96010a26cf
  languageName: node
  linkType: hard

"@jupyterlab/documentsearch@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/documentsearch@npm:4.4.4"
  dependencies:
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/commands": ^2.3.2
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/messaging": ^2.0.3
    "@lumino/polling": ^2.1.4
    "@lumino/signaling": ^2.1.4
    "@lumino/widgets": ^2.7.1
    react: ^18.2.0
  checksum: 34e5835fc981c2a9477a7589edfecec6423397d22aa94e8ceda5b11ff9d16f50a9f6cc483bc894627cd34ca777fc3102c01847abe571b3a0273488c8925b7ac0
  languageName: node
  linkType: hard

"@jupyterlab/extensionmanager-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/extensionmanager-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/extensionmanager": ^4.4.4
    "@jupyterlab/settingregistry": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
  checksum: a1472368919b589ef8f7037ab5eecd04a08571396f893b4309458300e680849d5b23af868c75189a64f84456677b9a823fef185a4fc2cf3dc053bb9ba3dfb400
  languageName: node
  linkType: hard

"@jupyterlab/extensionmanager@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/extensionmanager@npm:4.4.4"
  dependencies:
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/services": ^7.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/messaging": ^2.0.3
    "@lumino/polling": ^2.1.4
    "@lumino/widgets": ^2.7.1
    react: ^18.2.0
    react-paginate: ^6.3.2
    semver: ^7.5.2
  checksum: 6b2a83b871c0db11f8ee7c0d3ca88595c46abe9cab86a8a2dae79cb44cb20ce8d58bc9023be1ff6066615f57b7af639bbb9026946cdec8998239fc5703607aa2
  languageName: node
  linkType: hard

"@jupyterlab/filebrowser-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/filebrowser-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/docmanager": ^4.4.4
    "@jupyterlab/docregistry": ^4.4.4
    "@jupyterlab/filebrowser": ^4.4.4
    "@jupyterlab/services": ^7.4.4
    "@jupyterlab/settingregistry": ^4.4.4
    "@jupyterlab/statedb": ^4.4.4
    "@jupyterlab/statusbar": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/algorithm": ^2.0.3
    "@lumino/commands": ^2.3.2
    "@lumino/widgets": ^2.7.1
  checksum: 4c020ab8afddf7f7fd1b8d8b2e1d4250602c5a265049ac71aa9d0bfd2c288eb3315a284b68cb582d4af16056bf6710227347ff947e464922e59bd6e68c3f58cc
  languageName: node
  linkType: hard

"@jupyterlab/filebrowser@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/filebrowser@npm:4.4.4"
  dependencies:
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/docmanager": ^4.4.4
    "@jupyterlab/docregistry": ^4.4.4
    "@jupyterlab/services": ^7.4.4
    "@jupyterlab/statedb": ^4.4.4
    "@jupyterlab/statusbar": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/algorithm": ^2.0.3
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/domutils": ^2.0.3
    "@lumino/dragdrop": ^2.1.6
    "@lumino/messaging": ^2.0.3
    "@lumino/polling": ^2.1.4
    "@lumino/signaling": ^2.1.4
    "@lumino/virtualdom": ^2.0.3
    "@lumino/widgets": ^2.7.1
    react: ^18.2.0
  checksum: 3e689698beb72b5f9c527e7c86422df8899e863780d6b8ede0382968b2d28cdcbaadb8a29f0a1527f5b440b31c5d5c45208350b606c421574de7f4ee33c38439
  languageName: node
  linkType: hard

"@jupyterlab/fileeditor-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/fileeditor-extension@npm:4.4.4"
  dependencies:
    "@codemirror/commands": ^6.8.1
    "@codemirror/search": ^6.5.10
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/codeeditor": ^4.4.4
    "@jupyterlab/codemirror": ^4.4.4
    "@jupyterlab/completer": ^4.4.4
    "@jupyterlab/console": ^4.4.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/docregistry": ^4.4.4
    "@jupyterlab/documentsearch": ^4.4.4
    "@jupyterlab/filebrowser": ^4.4.4
    "@jupyterlab/fileeditor": ^4.4.4
    "@jupyterlab/launcher": ^4.4.4
    "@jupyterlab/lsp": ^4.4.4
    "@jupyterlab/mainmenu": ^4.4.4
    "@jupyterlab/observables": ^5.4.4
    "@jupyterlab/rendermime-interfaces": ^3.12.4
    "@jupyterlab/services": ^7.4.4
    "@jupyterlab/settingregistry": ^4.4.4
    "@jupyterlab/statusbar": ^4.4.4
    "@jupyterlab/toc": ^6.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/algorithm": ^2.0.3
    "@lumino/commands": ^2.3.2
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/widgets": ^2.7.1
  checksum: dd854348f0b0f223695ff77ee4281d33acd993783670afc5c7d4032c4053c3bf24d6b33c6001abb1170c6edf75e6d01d8f243f60646589ae11b5d755d188b724
  languageName: node
  linkType: hard

"@jupyterlab/fileeditor@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/fileeditor@npm:4.4.4"
  dependencies:
    "@jupyter/ydoc": ^3.0.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/codeeditor": ^4.4.4
    "@jupyterlab/codemirror": ^4.4.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/docregistry": ^4.4.4
    "@jupyterlab/documentsearch": ^4.4.4
    "@jupyterlab/lsp": ^4.4.4
    "@jupyterlab/statusbar": ^4.4.4
    "@jupyterlab/toc": ^6.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/commands": ^2.3.2
    "@lumino/coreutils": ^2.2.1
    "@lumino/messaging": ^2.0.3
    "@lumino/widgets": ^2.7.1
    react: ^18.2.0
    regexp-match-indices: ^1.0.2
  checksum: bfdb36e6dc9dd6f943cc00fa582fe868f507289a1e595634f99877f5686719504b7a2e334a5045d89d8fcf0b3f0324241bb2c362874587c7b88e4268a10607fb
  languageName: node
  linkType: hard

"@jupyterlab/help-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/help-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/mainmenu": ^4.4.4
    "@jupyterlab/services": ^7.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/widgets": ^2.7.1
    react: ^18.2.0
  checksum: 71f990fd5da306974ac4e64a5eb3f72b37de7341a3f7399d810787e24a7f0807768fb4df775e0486be2f3cf2c4d8f1ace589aa9bdedb45baf8787d9ca8e60c71
  languageName: node
  linkType: hard

"@jupyterlab/htmlviewer-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/htmlviewer-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/docregistry": ^4.4.4
    "@jupyterlab/htmlviewer": ^4.4.4
    "@jupyterlab/observables": ^5.4.4
    "@jupyterlab/settingregistry": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
  checksum: bd1160188255843a7a8595469b452bd10f9c83c9b44548d1bc2c776ee0644d2c62e938703445af1b16456fa67b9e5592695e8bc3b930d3ddeb93f138b233b4bd
  languageName: node
  linkType: hard

"@jupyterlab/htmlviewer@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/htmlviewer@npm:4.4.4"
  dependencies:
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/docregistry": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/coreutils": ^2.2.1
    "@lumino/signaling": ^2.1.4
    "@lumino/widgets": ^2.7.1
    react: ^18.2.0
  checksum: 70b2919cd947f4ff70486cf9fab64b2a441ea51fac21bccd53dab9e06f020a61f3b4fc916556eb722d76e60028a37118d6598b50907bbf1279169110261abdb6
  languageName: node
  linkType: hard

"@jupyterlab/hub-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/hub-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/services": ^7.4.4
    "@jupyterlab/translation": ^4.4.4
  checksum: 30045b8261cf6933dde97bc8047efe582e16a1222d86b9050728ca6fae4894e07452545e3353f5ecbb22279724256c718343a88e937dbe161fdac5ff1b0a2ef7
  languageName: node
  linkType: hard

"@jupyterlab/imageviewer-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/imageviewer-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/docregistry": ^4.4.4
    "@jupyterlab/imageviewer": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
  checksum: 4963cd61434d794d4fbb792f4b4d82af352f62b4adc8553cec8bf11beae6421edbae1980f08ad3d518c2e0027cb3a94af6e72d653ec0183fa6c468ea9f6d948c
  languageName: node
  linkType: hard

"@jupyterlab/imageviewer@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/imageviewer@npm:4.4.4"
  dependencies:
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/docregistry": ^4.4.4
    "@lumino/coreutils": ^2.2.1
    "@lumino/messaging": ^2.0.3
    "@lumino/widgets": ^2.7.1
  checksum: d4b1226e7c791be567cd4fa8ecd3bbdf84ef9f42da435a91f117bdfd8887fd91de9a9a0f48216e822d51ed4b1494a55db49b24f35276c1eef7b31f5dd3e3cbbd
  languageName: node
  linkType: hard

"@jupyterlab/inspector-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/inspector-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/console": ^4.4.4
    "@jupyterlab/inspector": ^4.4.4
    "@jupyterlab/launcher": ^4.4.4
    "@jupyterlab/notebook": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/widgets": ^2.7.1
  checksum: ad4ea30cf6ee39a44dfe816c399fe6be2e6cb91bfb32f12a557421ce5982e441442ab7c08187448103421880e2c3fe1782e4fcb434a3e01a18cd32ebfa1d75bb
  languageName: node
  linkType: hard

"@jupyterlab/inspector@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/inspector@npm:4.4.4"
  dependencies:
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/codeeditor": ^4.4.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/rendermime": ^4.4.4
    "@jupyterlab/services": ^7.4.4
    "@jupyterlab/statedb": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/polling": ^2.1.4
    "@lumino/signaling": ^2.1.4
    "@lumino/widgets": ^2.7.1
  checksum: cf57008e847463b6ae5b136fd3840b9a3a7e52a8e559b61b29bd3ca358c6d03b2a83783aa59021c024c98bccf616cd4aaec2b647714aa0c0bb1f668140cd0227
  languageName: node
  linkType: hard

"@jupyterlab/javascript-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/javascript-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/rendermime": ^4.4.4
    "@jupyterlab/rendermime-interfaces": ^3.12.4
  checksum: c034ec6619e6b563edc3b488b5dd3ba622375ee784abe792470b4c33785b239095c775cade7914cddefa12c7048b16f1035ad46ecafda9067d803e9de5ccc624
  languageName: node
  linkType: hard

"@jupyterlab/json-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/json-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/codemirror": ^4.4.4
    "@jupyterlab/rendermime-interfaces": ^3.12.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lezer/highlight": ^1.2.0
    "@lumino/coreutils": ^2.2.1
    "@lumino/messaging": ^2.0.3
    "@lumino/widgets": ^2.7.1
    react: ^18.2.0
    react-dom: ^18.2.0
    react-highlight-words: ^0.20.0
    react-json-tree: ^0.18.0
    style-mod: ^4.0.0
  checksum: 2ef1ac5d4b8ace34ad368621a8873ce1e0930f656bbdaef6d21397dd30205093e095b615a666a506380c2275b67bacf0cfeea47d7693819fcf7d09b7574f7d7a
  languageName: node
  linkType: hard

"@jupyterlab/launcher-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/launcher-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/filebrowser": ^4.4.4
    "@jupyterlab/launcher": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/algorithm": ^2.0.3
    "@lumino/coreutils": ^2.2.1
    "@lumino/widgets": ^2.7.1
  checksum: 8b5bee3432f27c1399e4ba614e4e9285e86aec6b15e886a711dbda41453e6690a1b9279e0eb6a862171b18258a8e4e17d6f33114828bb2734b6bbd3647f5d757
  languageName: node
  linkType: hard

"@jupyterlab/launcher@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/launcher@npm:4.4.4"
  dependencies:
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/algorithm": ^2.0.3
    "@lumino/commands": ^2.3.2
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/properties": ^2.0.3
    "@lumino/widgets": ^2.7.1
    react: ^18.2.0
  checksum: 17f293869945c5a8ba3975786e43579c78c222d7a089941e59bf7ac6338f39c8cdc4acf6da31023ba0ae3a89a27b90a06f10a53a0c8085a10448ed812be23a2a
  languageName: node
  linkType: hard

"@jupyterlab/logconsole-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/logconsole-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/docregistry": ^4.4.4
    "@jupyterlab/logconsole": ^4.4.4
    "@jupyterlab/rendermime": ^4.4.4
    "@jupyterlab/settingregistry": ^4.4.4
    "@jupyterlab/statusbar": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/coreutils": ^2.2.1
    "@lumino/signaling": ^2.1.4
    "@lumino/widgets": ^2.7.1
    react: ^18.2.0
  checksum: 89b0fcdeb03ead6c9132c39ec2090b81eb156debb3eda79fe7ce6b68bbb6c07e468e370217a600bd4e63e39349a9788b9378250aaf73c8ca7d3c0d66213bd31b
  languageName: node
  linkType: hard

"@jupyterlab/logconsole@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/logconsole@npm:4.4.4"
  dependencies:
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/nbformat": ^4.4.4
    "@jupyterlab/outputarea": ^4.4.4
    "@jupyterlab/rendermime": ^4.4.4
    "@jupyterlab/services": ^7.4.4
    "@jupyterlab/translation": ^4.4.4
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/messaging": ^2.0.3
    "@lumino/signaling": ^2.1.4
    "@lumino/widgets": ^2.7.1
  checksum: 476b242cbbc52cb2e876961894600edc1e543e9dd883258f940cdf59ec3207d1a132bc7267d5e56aeba993f861abb5e11af237bf7b28e82857430ab51e9764d7
  languageName: node
  linkType: hard

"@jupyterlab/lsp-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/lsp-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/lsp": ^4.4.4
    "@jupyterlab/running": ^4.4.4
    "@jupyterlab/settingregistry": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/coreutils": ^2.2.1
    "@lumino/polling": ^2.1.4
    "@lumino/signaling": ^2.1.4
    "@rjsf/utils": ^5.13.4
    react: ^18.2.0
  checksum: 319d5fa23721b3bf3ca36fc6c7b15e038536611d56d7c7231b7c8041ca3b9206105ad941f18069d50f5bd37af4e020ce0686a5a0b7d5d10c66242dc8524e5082
  languageName: node
  linkType: hard

"@jupyterlab/lsp@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/lsp@npm:4.4.4"
  dependencies:
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/codeeditor": ^4.4.4
    "@jupyterlab/codemirror": ^4.4.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/docregistry": ^4.4.4
    "@jupyterlab/services": ^7.4.4
    "@jupyterlab/translation": ^4.4.4
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/signaling": ^2.1.4
    "@lumino/widgets": ^2.7.1
    lodash.mergewith: ^4.6.1
    vscode-jsonrpc: ^6.0.0
    vscode-languageserver-protocol: ^3.17.0
    vscode-ws-jsonrpc: ~1.0.2
  checksum: f09adab9478baac78deac6c0f6af13f6745b71accd25f20a17ab246980b4e92445fd957ca6597cb59a453f0fa855f1233e48664c72f05f361fe52d794663115c
  languageName: node
  linkType: hard

"@jupyterlab/mainmenu-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/mainmenu-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/docmanager": ^4.4.4
    "@jupyterlab/filebrowser": ^4.4.4
    "@jupyterlab/mainmenu": ^4.4.4
    "@jupyterlab/services": ^7.4.4
    "@jupyterlab/settingregistry": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/algorithm": ^2.0.3
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/messaging": ^2.0.3
    "@lumino/widgets": ^2.7.1
  checksum: d442a120459ddfd445bdb6c240d9f7b099ea47c497dd01c9da43b712e14a7a3aac261c9c9e40b6801b8201822ca8a3b57afaec17ba415477eebb4ac3471700c6
  languageName: node
  linkType: hard

"@jupyterlab/mainmenu@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/mainmenu@npm:4.4.4"
  dependencies:
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/algorithm": ^2.0.3
    "@lumino/commands": ^2.3.2
    "@lumino/coreutils": ^2.2.1
    "@lumino/widgets": ^2.7.1
  checksum: 1bcacdeb021c829fbe8d896d7441a4870cb373d1d99dceeaa8a67b3e207d896a583a2847cd719d159ac6f5a162cb8d2621420ebd2904817834d1ed28dbde5542
  languageName: node
  linkType: hard

"@jupyterlab/markdownviewer-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/markdownviewer-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/markdownviewer": ^4.4.4
    "@jupyterlab/rendermime": ^4.4.4
    "@jupyterlab/settingregistry": ^4.4.4
    "@jupyterlab/toc": ^6.4.4
    "@jupyterlab/translation": ^4.4.4
  checksum: afd9f332333a9aee82ec1c24b995b3a211f5af73c0fa9a3aabe51aa50de2a3db18d850198b81ca7bc7f669af59ee880fbefefda53f65e17609d531c71b22c500
  languageName: node
  linkType: hard

"@jupyterlab/markdownviewer@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/markdownviewer@npm:4.4.4"
  dependencies:
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/docregistry": ^4.4.4
    "@jupyterlab/rendermime": ^4.4.4
    "@jupyterlab/toc": ^6.4.4
    "@jupyterlab/translation": ^4.4.4
    "@lumino/coreutils": ^2.2.1
    "@lumino/messaging": ^2.0.3
    "@lumino/signaling": ^2.1.4
    "@lumino/widgets": ^2.7.1
  checksum: 15cd5038a37eca2f38ad331405a19e1c591efce58271b4ac6f72fff911d2ebbad51844cd499687cdf5f2f7325c4ff6fe7c4c94d4dbbd055dda25b4d61659dc4e
  languageName: node
  linkType: hard

"@jupyterlab/markedparser-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/markedparser-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/codemirror": ^4.4.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/mermaid": ^4.4.4
    "@jupyterlab/rendermime": ^4.4.4
    "@lumino/coreutils": ^2.2.1
    marked: ^15.0.7
    marked-gfm-heading-id: ^4.1.1
    marked-mangle: ^1.1.10
  checksum: 2ea8758f4a341b96fcabdb3b24e21f2a4ac139d62efd53570492e1fa66864e4b49382cf658bea3a3b7f96fb566df4aa1145140900d134c9b078467a976d9c091
  languageName: node
  linkType: hard

"@jupyterlab/mathjax-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/mathjax-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/rendermime": ^4.4.4
    "@lumino/coreutils": ^2.2.1
    mathjax-full: ^3.2.2
  checksum: 977d17cafa37e969c138bb27135bd5c1421fe7387708061b468f5de68d6b08c8e2d6623a0cd61aff23bf5aaf3f150b89ffdbd8c61179b0652762b934c8959cda
  languageName: node
  linkType: hard

"@jupyterlab/mermaid-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/mermaid-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/mermaid": ^4.4.4
    "@jupyterlab/rendermime-interfaces": ^3.12.4
    "@jupyterlab/translation": ^4.4.4
  checksum: 7d9c45e1caf32d166bb7448215ebf44a99dd0f540aba4e55bad285cda5cdfbbf53eb6c07cd7edcc9eea32006ac27b0e2fc554dcacd84aae2c6b42f7f43e6f30f
  languageName: node
  linkType: hard

"@jupyterlab/mermaid@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/mermaid@npm:4.4.4"
  dependencies:
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/rendermime-interfaces": ^3.12.4
    "@lumino/coreutils": ^2.2.1
    "@lumino/widgets": ^2.7.1
    "@mermaid-js/layout-elk": ^0.1.7
    mermaid: ^11.6.0
  checksum: 9a50ef746bfdd94a1aa23513f21e258d7fb08ad69bf4333f80e629f5296fcc4859ce4115478bf69acd4185b0a9b2b2a113188a44ab44a99103e3f9320c3e5ede
  languageName: node
  linkType: hard

"@jupyterlab/metadataform-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/metadataform-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/metadataform": ^4.4.4
    "@jupyterlab/notebook": ^4.4.4
    "@jupyterlab/settingregistry": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/coreutils": ^2.2.1
  checksum: 89eed9f4b9d63fef617db24c2dd142eab1f87c5264408da252d139a24b66e0ecb09fd4615a396ded0a89104d0921f4d33e1458abe6d1fabb6aa062b278c8534b
  languageName: node
  linkType: hard

"@jupyterlab/metadataform@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/metadataform@npm:4.4.4"
  dependencies:
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/nbformat": ^4.4.4
    "@jupyterlab/notebook": ^4.4.4
    "@jupyterlab/settingregistry": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/coreutils": ^2.2.1
    "@lumino/messaging": ^2.0.3
    "@lumino/widgets": ^2.7.1
    "@rjsf/core": ^5.13.4
    "@rjsf/validator-ajv8": ^5.13.4
    json-schema: ^0.4.0
    react: ^18.2.0
  checksum: 6978b503fb6fc557fbeeac69007ba04dd8c99ff6d775606428438f55712107579e4d16ed539f5d1faf20e9eae05e2cf3a7cae20d288d52c1f092a68f78f751cf
  languageName: node
  linkType: hard

"@jupyterlab/nbformat@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/nbformat@npm:4.4.4"
  dependencies:
    "@lumino/coreutils": ^2.2.1
  checksum: 976230c78fc3691a259fa41f28770431c20772687b61321814a9870ccac13d7e552e0edeeac54264dbd19a60070e0a9535974ba8581e2bf6e5cf0a9d08dc308b
  languageName: node
  linkType: hard

"@jupyterlab/notebook-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/notebook-extension@npm:4.4.4"
  dependencies:
    "@jupyter/ydoc": ^3.0.4
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/cell-toolbar": ^4.4.4
    "@jupyterlab/cells": ^4.4.4
    "@jupyterlab/codeeditor": ^4.4.4
    "@jupyterlab/codemirror": ^4.4.4
    "@jupyterlab/completer": ^4.4.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/docmanager": ^4.4.4
    "@jupyterlab/docmanager-extension": ^4.4.4
    "@jupyterlab/docregistry": ^4.4.4
    "@jupyterlab/documentsearch": ^4.4.4
    "@jupyterlab/filebrowser": ^4.4.4
    "@jupyterlab/launcher": ^4.4.4
    "@jupyterlab/logconsole": ^4.4.4
    "@jupyterlab/lsp": ^4.4.4
    "@jupyterlab/mainmenu": ^4.4.4
    "@jupyterlab/metadataform": ^4.4.4
    "@jupyterlab/nbformat": ^4.4.4
    "@jupyterlab/notebook": ^4.4.4
    "@jupyterlab/observables": ^5.4.4
    "@jupyterlab/property-inspector": ^4.4.4
    "@jupyterlab/rendermime": ^4.4.4
    "@jupyterlab/services": ^7.4.4
    "@jupyterlab/settingregistry": ^4.4.4
    "@jupyterlab/statedb": ^4.4.4
    "@jupyterlab/statusbar": ^4.4.4
    "@jupyterlab/toc": ^6.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/algorithm": ^2.0.3
    "@lumino/commands": ^2.3.2
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/messaging": ^2.0.3
    "@lumino/polling": ^2.1.4
    "@lumino/widgets": ^2.7.1
    "@rjsf/utils": ^5.13.4
    react: ^18.2.0
  checksum: a2f1b723800418a7bd9a6fdb46ebe6625950e28fa40aeb12b9f04b8765f7ff3b739ad3e6fbf1c3b993f7778a6bad250318fda232c840314d7090be770028e58a
  languageName: node
  linkType: hard

"@jupyterlab/notebook@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/notebook@npm:4.4.4"
  dependencies:
    "@jupyter/ydoc": ^3.0.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/cells": ^4.4.4
    "@jupyterlab/codeeditor": ^4.4.4
    "@jupyterlab/codemirror": ^4.4.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/docregistry": ^4.4.4
    "@jupyterlab/documentsearch": ^4.4.4
    "@jupyterlab/lsp": ^4.4.4
    "@jupyterlab/nbformat": ^4.4.4
    "@jupyterlab/observables": ^5.4.4
    "@jupyterlab/rendermime": ^4.4.4
    "@jupyterlab/services": ^7.4.4
    "@jupyterlab/settingregistry": ^4.4.4
    "@jupyterlab/statusbar": ^4.4.4
    "@jupyterlab/toc": ^6.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/algorithm": ^2.0.3
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/domutils": ^2.0.3
    "@lumino/dragdrop": ^2.1.6
    "@lumino/messaging": ^2.0.3
    "@lumino/polling": ^2.1.4
    "@lumino/properties": ^2.0.3
    "@lumino/signaling": ^2.1.4
    "@lumino/virtualdom": ^2.0.3
    "@lumino/widgets": ^2.7.1
    react: ^18.2.0
  checksum: 05d16bdb8817144ed8938ca12ce6f15bb8d3bef1a4b232d0a887d9979a759d6fd5765e834f8d2a9079fa26daa65e8e6e166f1e6f74c0a9ff26530a3ec55ab95e
  languageName: node
  linkType: hard

"@jupyterlab/observables@npm:~5.4.4":
  version: 5.4.4
  resolution: "@jupyterlab/observables@npm:5.4.4"
  dependencies:
    "@lumino/algorithm": ^2.0.3
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/messaging": ^2.0.3
    "@lumino/signaling": ^2.1.4
  checksum: efd088c70a4d52370d7eebd3542d830e6eea9338dcb146628c0ee932d22917822fb056ea59ae58c71ad542e761ab36d641f699369d7d4b2c2a1452efb12af3c0
  languageName: node
  linkType: hard

"@jupyterlab/outputarea@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/outputarea@npm:4.4.4"
  dependencies:
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/nbformat": ^4.4.4
    "@jupyterlab/observables": ^5.4.4
    "@jupyterlab/rendermime": ^4.4.4
    "@jupyterlab/rendermime-interfaces": ^3.12.4
    "@jupyterlab/services": ^7.4.4
    "@jupyterlab/translation": ^4.4.4
    "@lumino/algorithm": ^2.0.3
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/messaging": ^2.0.3
    "@lumino/properties": ^2.0.3
    "@lumino/signaling": ^2.1.4
    "@lumino/widgets": ^2.7.1
  checksum: cf7777d3908d67c1f5c2834a0fc77ffad6f865ac175b88903657364fddec8ea0bd0d4cf2cdfc4d8829e9aaaad143119ef084f44af7816dd1cc6693ce5b402c32
  languageName: node
  linkType: hard

"@jupyterlab/pdf-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/pdf-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/rendermime-interfaces": ^3.12.4
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/widgets": ^2.7.1
  checksum: a6a7775194742b4ab24e7da5c16c24b3569d757f8aa70f4753a36415a71a8e9b8a464ad006fc41c0a51749109aa5717c5410fdf0b371bd379b62a92b04d99fed
  languageName: node
  linkType: hard

"@jupyterlab/pluginmanager-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/pluginmanager-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/pluginmanager": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/coreutils": ^2.2.1
  checksum: 5a5962b0bb76d33050cfe877b612b8eaab35056c5be911725567caaf581592bd5c1e73eada0c4422a51fc96a90a7769d023b77508101d28bce26e7c3d81e4e4d
  languageName: node
  linkType: hard

"@jupyterlab/pluginmanager@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/pluginmanager@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/services": ^7.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/coreutils": ^2.2.1
    "@lumino/signaling": ^2.1.4
    "@lumino/widgets": ^2.7.1
    react: ^18.2.0
  checksum: dee9fa032c9320d161e62a1d80f4add271023b7fc006914bac6be4cd96313ebfab746e8db86e8755b121896c21341aea232014a7f0754b2bb1a50db2f8037a80
  languageName: node
  linkType: hard

"@jupyterlab/property-inspector@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/property-inspector@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/signaling": ^2.1.4
    "@lumino/widgets": ^2.7.1
    react: ^18.2.0
  checksum: 3231ef5bf20c2a06f7d04476feb5d71ea8cc3c6f5d02cb3be72a811c02c81b32713063ed065102f9a89aef04ccbd2da7915612ee37defaf6f90e3d466d148b87
  languageName: node
  linkType: hard

"@jupyterlab/rendermime-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/rendermime-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/docmanager": ^4.4.4
    "@jupyterlab/rendermime": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
  checksum: ac856e35046ae70897b4c7d726f1d00f01c755721e5cedbdf55f3e3d5fe4f715b31a515064680092a45b992e37b24d2cb777aa3119bd35a03779b57c4b40d413
  languageName: node
  linkType: hard

"@jupyterlab/rendermime-interfaces@npm:~3.12.4":
  version: 3.12.4
  resolution: "@jupyterlab/rendermime-interfaces@npm:3.12.4"
  dependencies:
    "@lumino/coreutils": ^1.11.0 || ^2.2.1
    "@lumino/widgets": ^1.37.2 || ^2.7.1
  checksum: f90e1b83b5ebb576f15ec49e4d4adf7f19b2acc4d2fdfc6bad955e9337e5fa229af45bada70d5ecae95bf2bfef9989003bad99fa0c0a03108055395ef687fae5
  languageName: node
  linkType: hard

"@jupyterlab/rendermime@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/rendermime@npm:4.4.4"
  dependencies:
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/nbformat": ^4.4.4
    "@jupyterlab/observables": ^5.4.4
    "@jupyterlab/rendermime-interfaces": ^3.12.4
    "@jupyterlab/services": ^7.4.4
    "@jupyterlab/translation": ^4.4.4
    "@lumino/coreutils": ^2.2.1
    "@lumino/messaging": ^2.0.3
    "@lumino/signaling": ^2.1.4
    "@lumino/widgets": ^2.7.1
    lodash.escape: ^4.0.1
  checksum: fceb4b2a412e3c74ec289661142107aa15433e4154e57f9ac921413affbb503f80a2e8b6ddd78a9671cfb4b0bd3eb59bb351d2386f3817e5d233efc646ec3ca2
  languageName: node
  linkType: hard

"@jupyterlab/running-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/running-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/docmanager": ^4.4.4
    "@jupyterlab/docregistry": ^4.4.4
    "@jupyterlab/rendermime-interfaces": ^3.12.4
    "@jupyterlab/running": ^4.4.4
    "@jupyterlab/services": ^7.4.4
    "@jupyterlab/statedb": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/commands": ^2.3.2
    "@lumino/polling": ^2.1.4
    "@lumino/signaling": ^2.1.4
    "@lumino/widgets": ^2.7.1
    react: ^18.2.0
  checksum: b54eca82d870b5791f8bb8e143a97c562b2b5a985f34039585b90557a8049075c7ff6a60fb7651a10a9278d19fd74a70f24a2280408648aaeb503d02d1de898a
  languageName: node
  linkType: hard

"@jupyterlab/running@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/running@npm:4.4.4"
  dependencies:
    "@jupyter/react-components": ^0.16.6
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/statedb": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/domutils": ^2.0.3
    "@lumino/messaging": ^2.0.3
    "@lumino/signaling": ^2.1.4
    "@lumino/widgets": ^2.7.1
    react: ^18.2.0
  checksum: 3bb5a70935206f39c6241db8ce3b863f78e8a929454ff989e07bce490243e5a21a27ed44329bd2bfdfffa1b42122d86a7a1d02c7212fc0da64ee87403e2db1c5
  languageName: node
  linkType: hard

"@jupyterlab/services-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/services-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/services": ^7.4.4
    "@lumino/coreutils": ^2.2.1
  checksum: fd99050ef39b43dc359397f501f7288df0e4f4946a6107bd2b20817e30ebc52121a7f24c4ae9b8b640b1e786c55e3d3a35a9add175ad72c1a7d48786b3fc43cc
  languageName: node
  linkType: hard

"@jupyterlab/services@npm:~7.4.4":
  version: 7.4.4
  resolution: "@jupyterlab/services@npm:7.4.4"
  dependencies:
    "@jupyter/ydoc": ^3.0.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/nbformat": ^4.4.4
    "@jupyterlab/settingregistry": ^4.4.4
    "@jupyterlab/statedb": ^4.4.4
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/polling": ^2.1.4
    "@lumino/properties": ^2.0.3
    "@lumino/signaling": ^2.1.4
    ws: ^8.11.0
  checksum: 162bf9f908103eff736df4dfa9d32d79d6af40e09975bbd39275f1e19f30e8688f355b543ca4c3c5ad720f017d13e4fe01a3e98fc477069157c3c7e1c23ebe10
  languageName: node
  linkType: hard

"@jupyterlab/settingeditor-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/settingeditor-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/codeeditor": ^4.4.4
    "@jupyterlab/pluginmanager": ^4.4.4
    "@jupyterlab/rendermime": ^4.4.4
    "@jupyterlab/settingeditor": ^4.4.4
    "@jupyterlab/settingregistry": ^4.4.4
    "@jupyterlab/statedb": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/disposable": ^2.1.4
    react: ^18.2.0
  checksum: ade4e71d3cc6f667bbd50ba3b76f5be5b313a7ed617d19ce677dfdc8de4171b0efb2914eb1c4d8d88bee168040999059cf8b31d18c3e1322c179036c5073df22
  languageName: node
  linkType: hard

"@jupyterlab/settingeditor@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/settingeditor@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/codeeditor": ^4.4.4
    "@jupyterlab/inspector": ^4.4.4
    "@jupyterlab/rendermime": ^4.4.4
    "@jupyterlab/settingregistry": ^4.4.4
    "@jupyterlab/statedb": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/algorithm": ^2.0.3
    "@lumino/commands": ^2.3.2
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/messaging": ^2.0.3
    "@lumino/polling": ^2.1.4
    "@lumino/signaling": ^2.1.4
    "@lumino/widgets": ^2.7.1
    "@rjsf/core": ^5.13.4
    "@rjsf/utils": ^5.13.4
    "@rjsf/validator-ajv8": ^5.13.4
    json-schema: ^0.4.0
    react: ^18.2.0
  checksum: ceed6a2f5d07b0e4c45879a95b71e859c00eb0b3337eb5bddb1c648f2073c6c5645dcdd98dfd8cf4d5a5bd0d65109381ced226c831856a818f9b4a5a5d66fab3
  languageName: node
  linkType: hard

"@jupyterlab/settingregistry@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/settingregistry@npm:4.4.4"
  dependencies:
    "@jupyterlab/nbformat": ^4.4.4
    "@jupyterlab/statedb": ^4.4.4
    "@lumino/commands": ^2.3.2
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/signaling": ^2.1.4
    "@rjsf/utils": ^5.13.4
    ajv: ^8.12.0
    json5: ^2.2.3
  peerDependencies:
    react: ">=16"
  checksum: 542a6703a0c26c2ad775ec6c2734704a34d2e6c48c39c51fe143cd8af03383748b4682b3ae4ea9bbead658c77c55112760720e5b41b60738f671ca7869b47334
  languageName: node
  linkType: hard

"@jupyterlab/shortcuts-extension@npm:~5.2.4":
  version: 5.2.4
  resolution: "@jupyterlab/shortcuts-extension@npm:5.2.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/settingregistry": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/algorithm": ^2.0.3
    "@lumino/commands": ^2.3.2
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/domutils": ^2.0.3
    "@lumino/keyboard": ^2.0.3
    "@lumino/signaling": ^2.1.4
    react: ^18.2.0
  checksum: 0889fd08e13ae918abe5e4b445d8c1bdc85fac707e47ba30e5838bc39e68ab8ed9609b7b7e59270abfaffc72f866b196c8f4f66ba9b190919939443f2ddc392d
  languageName: node
  linkType: hard

"@jupyterlab/statedb@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/statedb@npm:4.4.4"
  dependencies:
    "@lumino/commands": ^2.3.2
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/properties": ^2.0.3
    "@lumino/signaling": ^2.1.4
  checksum: 9b98f6cebdb812f3d586fd55a8ca4ac0198aa9f3492b2dc7d844209b58992ae63e432a74139e18e29e49094f86770ac5c10cb6d6e44b4ed97b47997b08220a80
  languageName: node
  linkType: hard

"@jupyterlab/statusbar-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/statusbar-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/settingregistry": ^4.4.4
    "@jupyterlab/statusbar": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
  checksum: 4a84318f3095d2ad0598a650943c654b337bd36fccaafebc10172e773e79d2d19aa691aaab897829f0ad384db28440fd0b67e3e97661323f673917a819e24899
  languageName: node
  linkType: hard

"@jupyterlab/statusbar@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/statusbar@npm:4.4.4"
  dependencies:
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/algorithm": ^2.0.3
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/messaging": ^2.0.3
    "@lumino/signaling": ^2.1.4
    "@lumino/widgets": ^2.7.1
    react: ^18.2.0
  checksum: 44c9e72154bfd0c035c5dc14638967ac37bb0859e7285bbec394ee8be04d0ed9595968d4fc31806c683b4c7a32eaeccca2a9ee57f2bc30a81d79634a158b99a9
  languageName: node
  linkType: hard

"@jupyterlab/terminal-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/terminal-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/launcher": ^4.4.4
    "@jupyterlab/mainmenu": ^4.4.4
    "@jupyterlab/running": ^4.4.4
    "@jupyterlab/services": ^7.4.4
    "@jupyterlab/settingregistry": ^4.4.4
    "@jupyterlab/terminal": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/widgets": ^2.7.1
  checksum: c582a1d38f9358161581baeb6855f284d77b72bff3f6f5f96c9337c0500feca652545a3b01ca9bae775503dcf5a3b45bdf370a64f398b149b1bc69fede95908b
  languageName: node
  linkType: hard

"@jupyterlab/terminal@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/terminal@npm:4.4.4"
  dependencies:
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/services": ^7.4.4
    "@jupyterlab/translation": ^4.4.4
    "@lumino/coreutils": ^2.2.1
    "@lumino/domutils": ^2.0.3
    "@lumino/messaging": ^2.0.3
    "@lumino/widgets": ^2.7.1
    "@xterm/addon-canvas": ~0.7.0
    "@xterm/addon-fit": ~0.10.0
    "@xterm/addon-web-links": ~0.11.0
    "@xterm/addon-webgl": ~0.18.0
    "@xterm/xterm": ~5.5.0
  checksum: 6c15db03400bc32c179167c5730cefabdedb0296bc4d1138e16ccf036337abae8aced35cfb9eaae755847116dcf9cc0c3af601f96501bb1300755b4364e95b29
  languageName: node
  linkType: hard

"@jupyterlab/theme-dark-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/theme-dark-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/translation": ^4.4.4
  checksum: d19dddd3e0e1ad1230cbc2ddf90dd16e61a4f550607b38bc6b05a7708954bd1175946cec24cc175261ec8e551b12657abd6f1a0790d9d986145c86fc40e66cdd
  languageName: node
  linkType: hard

"@jupyterlab/theme-dark-high-contrast-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/theme-dark-high-contrast-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/translation": ^4.4.4
  checksum: 235ba3d6fd818fb6b3fe474a102e672c576f2bd553c6118f412f0d033f14c28bf7d9b4e18bcb9ad248551ede8b0b96c0b5ceb2a3ca630b85f96ca835c0e9a6a1
  languageName: node
  linkType: hard

"@jupyterlab/theme-light-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/theme-light-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/translation": ^4.4.4
  checksum: 552060cc4a50a36f4657952f77d956fcf4cb44253efcb80b0a312ba87e209ccba8e70b0f979052d455291c3200356bf7dd39bb2ade987dfe26f1ef431984a911
  languageName: node
  linkType: hard

"@jupyterlab/toc-extension@npm:~6.4.4":
  version: 6.4.4
  resolution: "@jupyterlab/toc-extension@npm:6.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/settingregistry": ^4.4.4
    "@jupyterlab/toc": ^6.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
  checksum: 60e296f7a4d93eb87b2e6daa3b30fc3101249ddbf7ef31f9cdf50faf12a856f8e0d3e8c9d2e1dbcf717cd7bbcdffba8414b8ad4f1395724590d13828dce8fbc1
  languageName: node
  linkType: hard

"@jupyterlab/toc@npm:~6.4.4":
  version: 6.4.4
  resolution: "@jupyterlab/toc@npm:6.4.4"
  dependencies:
    "@jupyter/react-components": ^0.16.6
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/docregistry": ^4.4.4
    "@jupyterlab/observables": ^5.4.4
    "@jupyterlab/rendermime": ^4.4.4
    "@jupyterlab/rendermime-interfaces": ^3.12.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/messaging": ^2.0.3
    "@lumino/signaling": ^2.1.4
    "@lumino/widgets": ^2.7.1
    react: ^18.2.0
  checksum: 42754a7b84ace894fc19688e554f8b634077e8eeb1c1b3630201708a03b3be668c913b1dd72922133a15af2f4464fa82513427c1578cdd35be736c08674c6e19
  languageName: node
  linkType: hard

"@jupyterlab/tooltip-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/tooltip-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/codeeditor": ^4.4.4
    "@jupyterlab/console": ^4.4.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/fileeditor": ^4.4.4
    "@jupyterlab/notebook": ^4.4.4
    "@jupyterlab/rendermime": ^4.4.4
    "@jupyterlab/services": ^7.4.4
    "@jupyterlab/tooltip": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@lumino/algorithm": ^2.0.3
    "@lumino/coreutils": ^2.2.1
    "@lumino/widgets": ^2.7.1
  checksum: 2275c95e6260bb97ca56d398e0785a426541ed6a5c44a70e38797faa5f95ab3acba592164660f402acd5590ac6bbc2ea4ad3c5645e620533f2b08e3c4424bf76
  languageName: node
  linkType: hard

"@jupyterlab/tooltip@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/tooltip@npm:4.4.4"
  dependencies:
    "@jupyterlab/codeeditor": ^4.4.4
    "@jupyterlab/rendermime": ^4.4.4
    "@jupyterlab/services": ^7.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@lumino/coreutils": ^2.2.1
    "@lumino/messaging": ^2.0.3
    "@lumino/widgets": ^2.7.1
  checksum: 375fe7148555e817433d135d5d7ce39216ef0fda0364f3dd6e90adb3fa1b5e543dee0524bd61a02acb82bdd72ace269f43b0b4dcf6aa14f122832b0f1f9041da
  languageName: node
  linkType: hard

"@jupyterlab/translation-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/translation-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/mainmenu": ^4.4.4
    "@jupyterlab/settingregistry": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
  checksum: 0e5f8cef51c5c8d82c9b7688957832a2cd049d0e1e63c2a64a73b9354e99a040797e3f2a3fb762ff948c2e917f94bb43f2fde3e29626cc4aad9ed2d2fbcf81ba
  languageName: node
  linkType: hard

"@jupyterlab/translation@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/translation@npm:4.4.4"
  dependencies:
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/rendermime-interfaces": ^3.12.4
    "@jupyterlab/services": ^7.4.4
    "@jupyterlab/statedb": ^4.4.4
    "@lumino/coreutils": ^2.2.1
  checksum: 330c6d5bbfb6b9afc33a38172d644bd5eae23ef328032d23782bc256f117ffeb22fa42f58260d471878fbdfe4a8880f264d71de54290594495644534ee4c300f
  languageName: node
  linkType: hard

"@jupyterlab/ui-components-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/ui-components-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
  checksum: d1cd0fa0f985a1ff470ff69b9c3e1f85b6bfec0dc6ebb62fe0d5c7d96208d52079c3bc8fc05ea1e780111c0c27eaa877edd6365a086cf24fb5c01e444b78faf3
  languageName: node
  linkType: hard

"@jupyterlab/ui-components@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/ui-components@npm:4.4.4"
  dependencies:
    "@jupyter/react-components": ^0.16.6
    "@jupyter/web-components": ^0.16.6
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/observables": ^5.4.4
    "@jupyterlab/rendermime-interfaces": ^3.12.4
    "@jupyterlab/translation": ^4.4.4
    "@lumino/algorithm": ^2.0.3
    "@lumino/commands": ^2.3.2
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/messaging": ^2.0.3
    "@lumino/polling": ^2.1.4
    "@lumino/properties": ^2.0.3
    "@lumino/signaling": ^2.1.4
    "@lumino/virtualdom": ^2.0.3
    "@lumino/widgets": ^2.7.1
    "@rjsf/core": ^5.13.4
    "@rjsf/utils": ^5.13.4
    react: ^18.2.0
    react-dom: ^18.2.0
    typestyle: ^2.0.4
  peerDependencies:
    react: ^18.2.0
  checksum: 31961c53ea3d866c69a2a9bbb8d5ec1cc4a818301ae7e0abd086b42b5af08ae38e86691e7df5fedf4eb482e7ddfe098d4b03c3129d0e68c8f1aa581c8f98953f
  languageName: node
  linkType: hard

"@jupyterlab/vega5-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/vega5-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/rendermime-interfaces": ^3.12.4
    "@lumino/coreutils": ^2.2.1
    "@lumino/widgets": ^2.7.1
    vega: ^5.20.0
    vega-embed: ^6.2.1
    vega-lite: ^5.6.1-next.1
  checksum: bc1f6c1d05fcc73cf602e68b4bcc307022ded023523f0fb392d72d9e5d8076c2881383bb2ee7e51aff8a2ac35eeca15e1dac764ecc78a6c18872b3ddb8fd2f0c
  languageName: node
  linkType: hard

"@jupyterlab/workspaces-extension@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/workspaces-extension@npm:4.4.4"
  dependencies:
    "@jupyterlab/application": ^4.4.4
    "@jupyterlab/apputils": ^4.5.4
    "@jupyterlab/coreutils": ^6.4.4
    "@jupyterlab/filebrowser": ^4.4.4
    "@jupyterlab/running": ^4.4.4
    "@jupyterlab/services": ^7.4.4
    "@jupyterlab/settingregistry": ^4.4.4
    "@jupyterlab/statedb": ^4.4.4
    "@jupyterlab/translation": ^4.4.4
    "@jupyterlab/ui-components": ^4.4.4
    "@jupyterlab/workspaces": ^4.4.4
    react: ^18.2.0
  checksum: c9076b97d818c68a58cca33c7db8f280bdfac478c3cb1534c6f3e644c5deba80f69b8d3b96d42cd0298fd976f6510f5c618b46ec2362c43d035296ab766b1fcc
  languageName: node
  linkType: hard

"@jupyterlab/workspaces@npm:~4.4.4":
  version: 4.4.4
  resolution: "@jupyterlab/workspaces@npm:4.4.4"
  dependencies:
    "@jupyterlab/services": ^7.4.4
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/polling": ^2.1.4
    "@lumino/signaling": ^2.1.4
  checksum: d82944374a66f2e018e3b6a658486d31cc54e41f6efbffd7014b014d1383f1266629cffb158396a2eb8f77c6e209459ecdf713bb1a22096055068282d4d6ade9
  languageName: node
  linkType: hard

"@lezer/common@npm:^1.0.0":
  version: 1.2.1
  resolution: "@lezer/common@npm:1.2.1"
  checksum: 0bd092e293a509ce334f4aaf9a4d4a25528f743cd9d7e7948c697e34ac703b805b288b62ad01563488fb206fc34ff05084f7fc5d864be775924b3d0d53ea5dd2
  languageName: node
  linkType: hard

"@lezer/cpp@npm:^1.0.0":
  version: 1.1.0
  resolution: "@lezer/cpp@npm:1.1.0"
  dependencies:
    "@lezer/highlight": ^1.0.0
    "@lezer/lr": ^1.0.0
  checksum: 9b25c881fc9b64fd2b019a077a85b0ba7cfda0bbdd92dbb0ff43300c9ba1ec4360128fe912bfe0f06a1c1bb5a564c5ace375c8aad254d07a717768a8f268695d
  languageName: node
  linkType: hard

"@lezer/css@npm:^1.1.0, @lezer/css@npm:^1.1.7":
  version: 1.1.9
  resolution: "@lezer/css@npm:1.1.9"
  dependencies:
    "@lezer/common": ^1.2.0
    "@lezer/highlight": ^1.0.0
    "@lezer/lr": ^1.0.0
  checksum: 25c63475061a3c9f87961a7f85c5f547f14fb7e81b0864675d2206999a874a0559d676145c74c6ccde39519dbc8aa33e216265f5366d08060507b6c9e875fe0f
  languageName: node
  linkType: hard

"@lezer/generator@npm:^1.7.0":
  version: 1.7.0
  resolution: "@lezer/generator@npm:1.7.0"
  dependencies:
    "@lezer/common": ^1.1.0
    "@lezer/lr": ^1.3.0
  bin:
    lezer-generator: src/lezer-generator.cjs
  checksum: 69f4c6625446cb65adaa509480ec67502f27651707a8e45e99373e682d7f66f8842205669f174bcb138eade72c64ded0b54d6de6aa5af995ac1f1e805ef021fd
  languageName: node
  linkType: hard

"@lezer/highlight@npm:^1.0.0":
  version: 1.2.0
  resolution: "@lezer/highlight@npm:1.2.1"
  dependencies:
    "@lezer/common": ^1.0.0
  checksum: a8822d7e37f79ff64669eb2df4a9f9d16580e88f2b276a646092e19a9bdccac304e92510e200e35869a8b1f6c27eba5972c508d347a277e9b722d582ab7a23d5
  languageName: node
  linkType: hard

"@lezer/html@npm:^1.3.0":
  version: 1.3.3
  resolution: "@lezer/html@npm:1.3.3"
  dependencies:
    "@lezer/common": ^1.0.0
    "@lezer/highlight": ^1.0.0
    "@lezer/lr": ^1.0.0
  checksum: ad74a5a751daead9d5979a4e1dc55faf94e623672d6b835e4d84d7a1174f326fa6b511a354f6dd5ec4c0b375242614966607ecf07cc92e5c13afe882178fe01d
  languageName: node
  linkType: hard

"@lezer/java@npm:^1.0.0":
  version: 1.0.3
  resolution: "@lezer/java@npm:1.0.3"
  dependencies:
    "@lezer/highlight": ^1.0.0
    "@lezer/lr": ^1.0.0
  checksum: 2fffea6627d130413ffad4e61040267974cca3167d98881b9e5b5e2455530de74a82c234d93603e92a4972fad314671453c49c0a76b0f4547c4617d671fd7b99
  languageName: node
  linkType: hard

"@lezer/javascript@npm:^1.0.0":
  version: 1.4.1
  resolution: "@lezer/javascript@npm:1.4.1"
  dependencies:
    "@lezer/highlight": ^1.1.3
    "@lezer/lr": ^1.3.0
  checksum: 634270116d5f1c278e2949d397845f41cac388dec7f0db593a3dc23e0fd4a1b73b9bf08f96fcf109fcd3d38c4b374d48676dce3261ff8ff83a85f5d6a37989f7
  languageName: node
  linkType: hard

"@lezer/json@npm:^1.0.0":
  version: 1.0.0
  resolution: "@lezer/json@npm:1.0.0"
  dependencies:
    "@lezer/highlight": ^1.0.0
    "@lezer/lr": ^1.0.0
  checksum: c1ca0cdf681415b58a383a669944bed66da3aa830870d32d1e471d545cff0fe43d9ac8a0d2a318a96daa99cd5a645b1d58ba8fbdd2e8d7ca4d33a62c7582cbab
  languageName: node
  linkType: hard

"@lezer/lr@npm:^1.0.0, @lezer/lr@npm:^1.1.0, @lezer/lr@npm:^1.3.0":
  version: 1.4.0
  resolution: "@lezer/lr@npm:1.4.0"
  dependencies:
    "@lezer/common": ^1.0.0
  checksum: 4c8517017e9803415c6c5cb8230d8764107eafd7d0b847676cd1023abb863a4b268d0d01c7ce3cf1702c4749527c68f0a26b07c329cb7b68c36ed88362d7b193
  languageName: node
  linkType: hard

"@lezer/markdown@npm:^1.0.0, @lezer/markdown@npm:^1.3.0":
  version: 1.3.0
  resolution: "@lezer/markdown@npm:1.3.0"
  dependencies:
    "@lezer/common": ^1.0.0
    "@lezer/highlight": ^1.0.0
  checksum: 13eb2720e4cb84278349bad8af116f748813094f99fad02680010c3a8c5985e0358c344487990f87a31ef0d6c1a2be582301f914c0e4a6e9cfa22647b6cd6545
  languageName: node
  linkType: hard

"@lezer/php@npm:^1.0.0":
  version: 1.0.1
  resolution: "@lezer/php@npm:1.0.1"
  dependencies:
    "@lezer/highlight": ^1.0.0
    "@lezer/lr": ^1.1.0
  checksum: a847c255c030b4d38913ddf1d5bd7324d83be7ef8d1d244542870be03b9bf7dc71283afeb2415c40dfd188cb99f0cc44bad760b5f3b7c35c3b8e5e00253848fc
  languageName: node
  linkType: hard

"@lezer/python@npm:^1.1.4":
  version: 1.1.7
  resolution: "@lezer/python@npm:1.1.14"
  dependencies:
    "@lezer/highlight": ^1.0.0
    "@lezer/lr": ^1.0.0
  checksum: 1608187f698e972d11b340dfdfd79e15b1359641e386e386befd37d5e5839620b45a5a39c5616792a24da29ef1d99d11ea0dad52b9617f1767e7ea6a11c2fed3
  languageName: node
  linkType: hard

"@lezer/rust@npm:^1.0.0":
  version: 1.0.0
  resolution: "@lezer/rust@npm:1.0.0"
  dependencies:
    "@lezer/highlight": ^1.0.0
    "@lezer/lr": ^1.0.0
  checksum: 0c42f415674f60ca2ef4274b446577621cdeec8f31168b1c3b90888a4377c513f02a89ee346421c264ec3a77fe2fa3e134996be6463ed506dbbc79b4b4505375
  languageName: node
  linkType: hard

"@lezer/xml@npm:^1.0.0":
  version: 1.0.1
  resolution: "@lezer/xml@npm:1.0.1"
  dependencies:
    "@lezer/highlight": ^1.0.0
    "@lezer/lr": ^1.0.0
  checksum: 271319aa7802c123845b70ffa63d7065c0f92fc6a1ddb1f8ec9f3aa965bca3df3c9fad4d4de53187ddf230e833cd3ab3a84cb2aded76ab5f6831e9a2fc310923
  languageName: node
  linkType: hard

"@lumino/algorithm@npm:^2.0.0":
  version: 2.0.3
  resolution: "@lumino/algorithm@npm:2.0.3"
  checksum: 03932cdc39d612a00579ee40bafb0b1d8bf5f8a12449f777a1ae7201843ddefb557bc3f9260aa6b9441d87bfc43e53cced854e71c4737de59e32cd00d4ac1394
  languageName: node
  linkType: hard

"@lumino/application@npm:^2.3.0-alpha.0":
  version: 2.4.4
  resolution: "@lumino/application@npm:2.4.4"
  dependencies:
    "@lumino/commands": ^2.3.2
    "@lumino/coreutils": ^2.2.1
    "@lumino/widgets": ^2.7.1
  checksum: 3223d145172d2d7a793e038631463fdb8c70d46f8343512d452a90f54ac70c6004462ded66edba3313038888f8271ad186feb63918620b27bde500eaa9f33d95
  languageName: node
  linkType: hard

"@lumino/collections@npm:^2.0.3":
  version: 2.0.3
  resolution: "@lumino/collections@npm:2.0.3"
  dependencies:
    "@lumino/algorithm": ^2.0.3
  checksum: 1c7aca239731e6c7379ce593318fd3f646b38c1903e81e884e36ed01f61017498f6699ba58848c43191f4825a9968b7f9c94e9355f1614c9baee84ce9ea6221f
  languageName: node
  linkType: hard

"@lumino/commands@npm:^2.0.1":
  version: 2.3.2
  resolution: "@lumino/commands@npm:2.3.2"
  dependencies:
    "@lumino/algorithm": ^2.0.3
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/domutils": ^2.0.3
    "@lumino/keyboard": ^2.0.3
    "@lumino/signaling": ^2.1.4
    "@lumino/virtualdom": ^2.0.3
  checksum: 090454bcc07aeb71f0791d6ca86ca4857b16bb6286a47ab6e59c3046e7f99cd3ef27c36d2dd35de7cf2bdeeaf5fc00ae8f29246a39e276eac2d186ae3cd7023e
  languageName: node
  linkType: hard

"@lumino/coreutils@npm:^2.0.0":
  version: 2.2.1
  resolution: "@lumino/coreutils@npm:2.2.1"
  dependencies:
    "@lumino/algorithm": ^2.0.3
  checksum: d08570d1ebcf6bca973ba3af0836fb19a5a7a5b24979e90aab0fb4acb245e9619a0db356a78d67f618ae565435bb2aaf7c158c5bc0ae1ef9e9f1638ebfa05484
  languageName: node
  linkType: hard

"@lumino/datagrid@npm:^2.3.0-alpha.0":
  version: 2.5.2
  resolution: "@lumino/datagrid@npm:2.5.2"
  dependencies:
    "@lumino/algorithm": ^2.0.3
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/domutils": ^2.0.3
    "@lumino/dragdrop": ^2.1.6
    "@lumino/keyboard": ^2.0.3
    "@lumino/messaging": ^2.0.3
    "@lumino/signaling": ^2.1.4
    "@lumino/widgets": ^2.7.1
  checksum: b11320a7635c974b451650b78a772bfccee95360f77dc4e790b0139a8a0dab82cf4bb0a4238ea90581fb2d6e2ac1e425cc4a3cc17e2ab4b8aab94e3fff5735a0
  languageName: node
  linkType: hard

"@lumino/disposable@npm:^2.0.0":
  version: 2.1.4
  resolution: "@lumino/disposable@npm:2.1.4"
  dependencies:
    "@lumino/signaling": ^2.1.4
  checksum: 0274c1cd81683f0d37c79795ed683fe49929452e6f075b9027b62dee376b5c6aa5f27b279236c4e1621bcbdcb844d5be0bbde3a065ab39159deb995244d1d2a7
  languageName: node
  linkType: hard

"@lumino/domutils@npm:^2.0.0":
  version: 2.0.3
  resolution: "@lumino/domutils@npm:2.0.3"
  checksum: 46cbcbd38f6abb53eab1b6de0a2ea8a9fa5e28b0f5aa4b058c35f2380cb8ec881fe7616c7468ba200b785f95357ac8cbac6b64512f9945f5973d1d425864b163
  languageName: node
  linkType: hard

"@lumino/dragdrop@npm:^2.0.0":
  version: 2.1.6
  resolution: "@lumino/dragdrop@npm:2.1.6"
  dependencies:
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
  checksum: 5a746ee0644e2fa02cba47d6ef45f3fb09ebc3391ac0f478f6f3073864a9637e13fcee666038c751ab8f17bc69c55299c85a88f526ea645cc3240a367490c8ca
  languageName: node
  linkType: hard

"@lumino/keyboard@npm:^2.0.0":
  version: 2.0.3
  resolution: "@lumino/keyboard@npm:2.0.3"
  checksum: ca648cf978ddcf15fe3af2b8c8beb8aff153dfe616099df5a8bc7f43124420f77c358dbd33a988911b82f68debe07268d630c1777618b182ef7b520962d653e7
  languageName: node
  linkType: hard

"@lumino/messaging@npm:^2.0.0":
  version: 2.0.3
  resolution: "@lumino/messaging@npm:2.0.3"
  dependencies:
    "@lumino/algorithm": ^2.0.3
    "@lumino/collections": ^2.0.3
  checksum: 9c2bea2a31d3922a29276df751b651e6bd41d1ed3a5f61ba94d3e90d454c53f07fc4dac7d435867fb8480415222a3d45d74188dd73e9c89c43110ebbee0ff301
  languageName: node
  linkType: hard

"@lumino/polling@npm:^2.0.0":
  version: 2.1.4
  resolution: "@lumino/polling@npm:2.1.4"
  dependencies:
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/signaling": ^2.1.4
  checksum: e08d07d11eb030fed83bea232dba91af4ea40ef8f6ec7b8fe61722ebbd29faba10c67d269596c19c515c920f607c73bb64cdc9319af9ecef4619cddfd92ea764
  languageName: node
  linkType: hard

"@lumino/properties@npm:^2.0.0":
  version: 2.0.3
  resolution: "@lumino/properties@npm:2.0.3"
  checksum: a575d821f994090907abb567d3af21a828f528ae5f329ada92719eba9818bbb2b0955e675b91bd392043a5d835c345d7b500994a77157c5ea317f36442ce570e
  languageName: node
  linkType: hard

"@lumino/signaling@npm:^2.0.0":
  version: 2.1.4
  resolution: "@lumino/signaling@npm:2.1.4"
  dependencies:
    "@lumino/algorithm": ^2.0.3
    "@lumino/coreutils": ^2.2.1
  checksum: 554a5135c8742ed3f61a4923b1f26cb29b55447ca5939df70033449cfb654a37048d7a3e2fd0932497099cd24501a3819b85cd1fdf4e76023ba0af747c171d53
  languageName: node
  linkType: hard

"@lumino/virtualdom@npm:^2.0.0":
  version: 2.0.3
  resolution: "@lumino/virtualdom@npm:2.0.3"
  dependencies:
    "@lumino/algorithm": ^2.0.3
  checksum: 66c18494fdfc1b87e76286140cd256b3616aede262641912646a18395226e200048ddeaa6d1644dff3f597b1cde8e583968cb973d64a9e9d4f45e2b24c1e2c7c
  languageName: node
  linkType: hard

"@lumino/widgets@npm:^2.3.1-alpha.0":
  version: 2.7.1
  resolution: "@lumino/widgets@npm:2.7.1"
  dependencies:
    "@lumino/algorithm": ^2.0.3
    "@lumino/commands": ^2.3.2
    "@lumino/coreutils": ^2.2.1
    "@lumino/disposable": ^2.1.4
    "@lumino/domutils": ^2.0.3
    "@lumino/dragdrop": ^2.1.6
    "@lumino/keyboard": ^2.0.3
    "@lumino/messaging": ^2.0.3
    "@lumino/properties": ^2.0.3
    "@lumino/signaling": ^2.1.4
    "@lumino/virtualdom": ^2.0.3
  checksum: c57f7e6cfbaddbd830e14db55242dcbdf531524cdf8641214ce737f43a6684004219eb58a572838f99f78af433bb8f9f19fd2ac6f0ffab4a635bd20164b75cec
  languageName: node
  linkType: hard

"@marijn/find-cluster-break@npm:^1.0.0":
  version: 1.0.2
  resolution: "@marijn/find-cluster-break@npm:1.0.2"
  checksum: 0d836de25e04d58325813401ef3c2d34caf040da985a5935fcbc9d84e7b47a21bdb15f57d70c2bf0960bd29ed3dbbb1afd00cdd0fc4fafbee7fd0ffe7d508ae1
  languageName: node
  linkType: hard

"@mermaid-js/layout-elk@npm:^0.1.7":
  version: 0.1.7
  resolution: "@mermaid-js/layout-elk@npm:0.1.7"
  dependencies:
    d3: ^7.9.0
    elkjs: ^0.9.3
  peerDependencies:
    mermaid: ^11.0.0
  checksum: 787d8e0bbaff4c2b329d92c5ac4daf8e1450c0f6a5678da5599a0efba7694a168b3179423909b1a47999ee134af4797b56502f4b438a18d06d17467c1c612956
  languageName: node
  linkType: hard

"@mermaid-js/parser@npm:^0.4.0":
  version: 0.4.0
  resolution: "@mermaid-js/parser@npm:0.4.0"
  dependencies:
    langium: 3.3.1
  checksum: bb2412416807fea35a4c6dce8908cf54e1192293e1e16696b4b7a793b871cc03519738247a59ff8d8e2aac39665883f2c1e75567354a6498e874e18d973d3de7
  languageName: node
  linkType: hard

"@microsoft/fast-colors@npm:^5.3.1":
  version: 5.3.1
  resolution: "@microsoft/fast-colors@npm:5.3.1"
  checksum: ff87f402faadb4b5aeee3d27762566c11807f927cd4012b8bbc7f073ca68de0e2197f95330ff5dfd7038f4b4f0e2f51b11feb64c5d570f5c598d37850a5daf60
  languageName: node
  linkType: hard

"@microsoft/fast-element@npm:^1.12.0":
  version: 1.12.0
  resolution: "@microsoft/fast-element@npm:1.12.0"
  checksum: bbff4e9c83106d1d74f3eeedc87bf84832429e78fee59c6a4ae8164ee4f42667503f586896bea72341b4d2c76c244a3cb0d4fd0d5d3732755f00357714dd609e
  languageName: node
  linkType: hard

"@microsoft/fast-foundation@npm:^2.49.2":
  version: 2.49.4
  resolution: "@microsoft/fast-foundation@npm:2.49.4"
  dependencies:
    "@microsoft/fast-element": ^1.12.0
    "@microsoft/fast-web-utilities": ^5.4.1
    tabbable: ^5.2.0
    tslib: ^1.13.0
  checksum: e979cd500aaba28090e8d9cdc6192933db01803c13288c11aded89aa54da6f0a70256ff2f249754b1c95d9abad369a18401e1df98d672e2823b83cf4cd88ad55
  languageName: node
  linkType: hard

"@microsoft/fast-web-utilities@npm:^5.4.1":
  version: 5.4.1
  resolution: "@microsoft/fast-web-utilities@npm:5.4.1"
  dependencies:
    exenv-es6: ^1.1.1
  checksum: 303e87847f962944f474e3716c3eb305668243916ca9e0719e26bb9a32346144bc958d915c103776b3e552cea0f0f6233f839fad66adfdf96a8436b947288ca7
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": 2.0.5
    run-parallel: ^1.1.9
  checksum: a970d595bd23c66c880e0ef1817791432dbb7acbb8d44b7e7d0e7a22f4521260d4a83f7f9fd61d44fda4610105577f8f58a60718105fb38352baed612fd79e59
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 012480b5ca9d97bff9261571dbbec7bbc6033f69cc92908bc1ecfad0792361a5a1994bc48674b9ef76419d056a03efadfce5a6cf6dbc0a36559571a7a483f6f0
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": 2.1.5
    fastq: ^1.6.0
  checksum: 190c643f156d8f8f277bf2a6078af1ffde1fd43f498f187c2db24d35b4b4b5785c02c7dc52e356497b9a1b65b13edc996de08de0b961c32844364da02986dc53
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^2.1.0":
  version: 2.1.2
  resolution: "@npmcli/fs@npm:2.1.2"
  dependencies:
    "@gar/promisify": ^1.1.3
    semver: ^7.3.5
  checksum: 405074965e72d4c9d728931b64d2d38e6ea12066d4fad651ac253d175e413c06fe4350970c783db0d749181da8fe49c42d3880bd1cbc12cd68e3a7964d820225
  languageName: node
  linkType: hard

"@npmcli/move-file@npm:^2.0.0":
  version: 2.0.1
  resolution: "@npmcli/move-file@npm:2.0.1"
  dependencies:
    mkdirp: ^1.0.4
    rimraf: ^3.0.2
  checksum: 52dc02259d98da517fae4cb3a0a3850227bdae4939dda1980b788a7670636ca2b4a01b58df03dd5f65c1e3cb70c50fa8ce5762b582b3f499ec30ee5ce1fd9380
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 6ad6a00fc4f2f2cfc6bff76fb1d88b8ee20bc0601e18ebb01b6d4be583733a860239a521a7fbca73b612e66705078809483549d2b18f370eb346c5155c8e4a0f
  languageName: node
  linkType: hard

"@polka/url@npm:^1.0.0-next.20":
  version: 1.0.0-next.21
  resolution: "@polka/url@npm:1.0.0-next.21"
  checksum: c7654046d38984257dd639eab3dc770d1b0340916097b2fac03ce5d23506ada684e05574a69b255c32ea6a144a957c8cd84264159b545fca031c772289d88788
  languageName: node
  linkType: hard

"@rjsf/core@npm:^5.13.4":
  version: 5.14.3
  resolution: "@rjsf/core@npm:5.14.3"
  dependencies:
    lodash: ^4.17.21
    lodash-es: ^4.17.21
    markdown-to-jsx: ^7.3.2
    nanoid: ^3.3.6
    prop-types: ^15.8.1
  peerDependencies:
    "@rjsf/utils": ^5.12.x
    react: ^16.14.0 || >=17
  checksum: 9929ae7b99f1c79dc108095c3338a30fe373cd0fc6c613f65d163f7729cda2509070bb7da581ce3ad068a41ea5050f4e8417508c12e53feb41c56119082d3aac
  languageName: node
  linkType: hard

"@rjsf/utils@npm:^5.13.4":
  version: 5.14.3
  resolution: "@rjsf/utils@npm:5.14.3"
  dependencies:
    json-schema-merge-allof: ^0.8.1
    jsonpointer: ^5.0.1
    lodash: ^4.17.21
    lodash-es: ^4.17.21
    react-is: ^18.2.0
  peerDependencies:
    react: ^16.14.0 || >=17
  checksum: 759f85e1d205d360024e569ab04c57543cbec4af8293636ba0af0ec7797bdcb361cd0b33c176ede464f101b33eb4240b60e241537ba19edefc04b91b80e6051e
  languageName: node
  linkType: hard

"@rjsf/validator-ajv8@npm:^5.13.4":
  version: 5.14.3
  resolution: "@rjsf/validator-ajv8@npm:5.14.3"
  dependencies:
    ajv: ^8.12.0
    ajv-formats: ^2.1.1
    lodash: ^4.17.21
    lodash-es: ^4.17.21
  peerDependencies:
    "@rjsf/utils": ^5.12.x
  checksum: 8b7caa0d5a81ecffaee1a538625c93ff8fdf860d67b80e75901df62112d00479bed6987c25427b84957b4e483c80c80d91713922bdb21ae8dd440242f0e8f5e3
  languageName: node
  linkType: hard

"@sindresorhus/is@npm:^4.0.0":
  version: 4.6.0
  resolution: "@sindresorhus/is@npm:4.6.0"
  checksum: 83839f13da2c29d55c97abc3bc2c55b250d33a0447554997a85c539e058e57b8da092da396e252b11ec24a0279a0bed1f537fa26302209327060643e327f81d2
  languageName: node
  linkType: hard

"@szmarczak/http-timer@npm:^4.0.5":
  version: 4.0.6
  resolution: "@szmarczak/http-timer@npm:4.0.6"
  dependencies:
    defer-to-connect: ^2.0.0
  checksum: c29df3bcec6fc3bdec2b17981d89d9c9fc9bd7d0c9bcfe92821dc533f4440bc890ccde79971838b4ceed1921d456973c4180d7175ee1d0023ad0562240a58d95
  languageName: node
  linkType: hard

"@tootallnate/once@npm:2":
  version: 2.0.0
  resolution: "@tootallnate/once@npm:2.0.0"
  checksum: ad87447820dd3f24825d2d947ebc03072b20a42bfc96cbafec16bff8bbda6c1a81fcb0be56d5b21968560c5359a0af4038a68ba150c3e1694fe4c109a063bed8
  languageName: node
  linkType: hard

"@types/base16@npm:^1.0.2":
  version: 1.0.2
  resolution: "@types/base16@npm:1.0.2"
  checksum: 5bc587d45066dae6fb14a4a7f72e57044fa1a8057eed86d4005f9e38a0c38b83009828ac3072e6e9e4be1cf5d5032849690adf8cd51efd545fa281bee5cf0fa5
  languageName: node
  linkType: hard

"@types/cacheable-request@npm:^6.0.1":
  version: 6.0.3
  resolution: "@types/cacheable-request@npm:6.0.3"
  dependencies:
    "@types/http-cache-semantics": "*"
    "@types/keyv": ^3.1.4
    "@types/node": "*"
    "@types/responselike": ^1.0.0
  checksum: d9b26403fe65ce6b0cb3720b7030104c352bcb37e4fac2a7089a25a97de59c355fa08940658751f2f347a8512aa9d18fdb66ab3ade835975b2f454f2d5befbd9
  languageName: node
  linkType: hard

"@types/clone@npm:~2.1.1":
  version: 2.1.1
  resolution: "@types/clone@npm:2.1.1"
  checksum: bda9668b9d6e0875d64bbe00763676f566e8647bc224333a03ac7fd66655dfed56a98a9f8304d0145c4411b964649c84c4d1a03adbdb6547eafb9ab8f303d254
  languageName: node
  linkType: hard

"@types/d3-array@npm:*":
  version: 3.0.5
  resolution: "@types/d3-array@npm:3.0.5"
  checksum: c0014042f75dc77ff4749f6a7a78074aa73e63e5b6a985ac79a0abf3e3d943b19ddd3fbfcfcd11124f25b7f4b97cd0948eec84b2e0783cd617b49c593de695f0
  languageName: node
  linkType: hard

"@types/d3-axis@npm:*":
  version: 3.0.2
  resolution: "@types/d3-axis@npm:3.0.2"
  dependencies:
    "@types/d3-selection": "*"
  checksum: 3067a949572da14050c1ee1dc6a4e9ceb32e9a1bdd99e4029cdf1f541b86a843294d12f911fb9faa32a75107d36d925efcc66116f8341573cba4bb780f42ca00
  languageName: node
  linkType: hard

"@types/d3-brush@npm:*":
  version: 3.0.2
  resolution: "@types/d3-brush@npm:3.0.2"
  dependencies:
    "@types/d3-selection": "*"
  checksum: 5a539f94ff8f397a1ca874b1cf4e641a9b26cb965904f13b1b566a6505685124c37fecf45bd88b0527727b3ffcfadf53613e90aceb5cd774fa3b62f5960db019
  languageName: node
  linkType: hard

"@types/d3-chord@npm:*":
  version: 3.0.2
  resolution: "@types/d3-chord@npm:3.0.2"
  checksum: 7ea3398d826a0a6affe4bafb96340f74baf6126c11547af37962f486e31d4dd48d85ade8a357585bbc7616e46e43f82d2d2435e8bfe4c2d57977fd75dd53d1e5
  languageName: node
  linkType: hard

"@types/d3-color@npm:*":
  version: 3.1.0
  resolution: "@types/d3-color@npm:3.1.0"
  checksum: b1856f17d6366559a68eaba0164f30727e9dc5eaf1b3a6c8844354da228860240423d19fa4de65bff9da26b4ead8843eab14b1566962665412e8fd82c3810554
  languageName: node
  linkType: hard

"@types/d3-contour@npm:*":
  version: 3.0.2
  resolution: "@types/d3-contour@npm:3.0.2"
  dependencies:
    "@types/d3-array": "*"
    "@types/geojson": "*"
  checksum: 7b0f7ccf33dbad8124bd96736adf64b3630087fa0bda355685bcde43e13d51109a30de738785dd33d627bd2672d78d0210b7997403358974ac87b57fcf5e2752
  languageName: node
  linkType: hard

"@types/d3-delaunay@npm:*":
  version: 6.0.1
  resolution: "@types/d3-delaunay@npm:6.0.1"
  checksum: c46fd6f399ed604e9f40841851c432c936c4408af9e61b235a7f6030e93faa9b5c4f6c33a62be221e1d33f48a9162e9c4bbfa173746c0e4787483310da9a03b2
  languageName: node
  linkType: hard

"@types/d3-dispatch@npm:*":
  version: 3.0.2
  resolution: "@types/d3-dispatch@npm:3.0.2"
  checksum: 716f21bdc4e0057ecc2989f8c3b69ba18244b40ba42e6029aad30cbd254a42ce113ec775f40ca300e02fb23823a5ebf378dae3008614d7e591b7759607fde68a
  languageName: node
  linkType: hard

"@types/d3-drag@npm:*":
  version: 3.0.2
  resolution: "@types/d3-drag@npm:3.0.2"
  dependencies:
    "@types/d3-selection": "*"
  checksum: cd2fd6a628c097c0c4fbd1ebe647f846d7bcc7819879882e48fd64fd743b5328efe715d002e9cbf47faa2ce3fabaec9795659cb0849c326576e98cd2bf95cbf1
  languageName: node
  linkType: hard

"@types/d3-dsv@npm:*":
  version: 3.0.1
  resolution: "@types/d3-dsv@npm:3.0.1"
  checksum: f3dbb3c994b1564b5cbeb2991fa9a0e0cee82e93e2d304f2e643b1808818493c6bb11da5503562e21ba6f6cced0faccc8d9cd5005e9065af8e4b6b4477cc8982
  languageName: node
  linkType: hard

"@types/d3-ease@npm:*":
  version: 3.0.0
  resolution: "@types/d3-ease@npm:3.0.0"
  checksum: 1be7c993643b5a08332e0ee146375a3845545d8deb423db5d152e0b061524385d2345ceccf968f75f605247b940dd3f9a144335fee2e7d935cddaf187afb7095
  languageName: node
  linkType: hard

"@types/d3-fetch@npm:*":
  version: 3.0.2
  resolution: "@types/d3-fetch@npm:3.0.2"
  dependencies:
    "@types/d3-dsv": "*"
  checksum: 10fad5c1d4d8c225f2381772fe85e92cfab6575d85069e7a6361eb4d8c0030e1cde7e9db484be7db2b2f075e8e0043dae827b72396a20b94b97e58cedc50f7a5
  languageName: node
  linkType: hard

"@types/d3-force@npm:*":
  version: 3.0.4
  resolution: "@types/d3-force@npm:3.0.4"
  checksum: 779fb597fb41e7bc6a5e1b8969d500deb95c4a73428c7c268bf0ca6f3ed668dd2ed6aa652de7af14d2f9c192dad4f6e7badf2c5bc330624bd8405ac88440b278
  languageName: node
  linkType: hard

"@types/d3-format@npm:*":
  version: 3.0.1
  resolution: "@types/d3-format@npm:3.0.1"
  checksum: 6819fae7e7c3fce1e44cd56e9b6d8ea5508f708cb7a42793edf82ff914b120bf6cddc35208b1b33ec82615ab82b843c284709f9cea5c3fe1ea5f012106b3d32c
  languageName: node
  linkType: hard

"@types/d3-geo@npm:*":
  version: 3.0.3
  resolution: "@types/d3-geo@npm:3.0.3"
  dependencies:
    "@types/geojson": "*"
  checksum: d2f0d386024efb97a0829488cf31d574669ff37f452bb4bb58ba62b03e705e583e166ba30844beffb51119909bf1a168a1efc91885c55ab72da9a52a46c114e2
  languageName: node
  linkType: hard

"@types/d3-hierarchy@npm:*":
  version: 3.1.2
  resolution: "@types/d3-hierarchy@npm:3.1.2"
  checksum: fc423b25843fb54a411e4e119eaa5a092c6da65cf17855c56fcd4807dcc897145aed78578d9af7dd9b924204c34588b1d8c9b973c27474512048e9486439b2d4
  languageName: node
  linkType: hard

"@types/d3-interpolate@npm:*":
  version: 3.0.1
  resolution: "@types/d3-interpolate@npm:3.0.1"
  dependencies:
    "@types/d3-color": "*"
  checksum: 29ce472968b9e6611bdf0eeedaf89e8d6066190b52ced011d16d8183b8b9f8e6dd6516ca2b85242594942896299b42f37504d44e635f8fba3090c2c58594e21b
  languageName: node
  linkType: hard

"@types/d3-path@npm:*":
  version: 3.0.0
  resolution: "@types/d3-path@npm:3.0.0"
  checksum: af7f45ea912cddd794c03384baba856f11e1f9b57a49d05a66a61968dafaeb86e0e42394883118b9b8ccadce21a5f25b1f9a88ad05235e1dc6d24c3e34a379ff
  languageName: node
  linkType: hard

"@types/d3-polygon@npm:*":
  version: 3.0.0
  resolution: "@types/d3-polygon@npm:3.0.0"
  checksum: 2b4fbd864e6e40c8f63c56c46ed27f63c18d4b9b8c6f03c48bda048bc29e0d77c01763122a3cf85cce89acb12c4a65f3cd7b1d87b53ced8bf6ce341831b30190
  languageName: node
  linkType: hard

"@types/d3-quadtree@npm:*":
  version: 3.0.2
  resolution: "@types/d3-quadtree@npm:3.0.2"
  checksum: 2a831a80590df125b07bc573c081449823e85159bb8fafbfee9f211dbe402840aeb01f4b04b4b47b0f28394daee29c8a10185f9acf17b6a8be649a377aff006b
  languageName: node
  linkType: hard

"@types/d3-random@npm:*":
  version: 3.0.1
  resolution: "@types/d3-random@npm:3.0.1"
  checksum: 7ed9f83ee3c0b02510767bf15551984c229491d694c3c9a45a8eb80b27f7eb5f9096a48011c8cae6787b3e36321a6aaf2fe0efed8685a669481927a3d08e6b8a
  languageName: node
  linkType: hard

"@types/d3-scale-chromatic@npm:*":
  version: 3.0.0
  resolution: "@types/d3-scale-chromatic@npm:3.0.0"
  checksum: e06afffd2725570aa90cb3050eb96a94727264948d9256e56807ab582aba379168d84d1d98bcaa275bf38375148b35dfe13697e06fc7565dd17ac7e2acb11980
  languageName: node
  linkType: hard

"@types/d3-scale@npm:*":
  version: 4.0.3
  resolution: "@types/d3-scale@npm:4.0.3"
  dependencies:
    "@types/d3-time": "*"
  checksum: 76684da8519ab5f2210e647f74f96ece9c6816dea4ad5d76131121703a5268cc65687a8bc9ebbf4a44039482247336d98811ecc3fbfeb7f0122fdce4bb295547
  languageName: node
  linkType: hard

"@types/d3-selection@npm:*":
  version: 3.0.5
  resolution: "@types/d3-selection@npm:3.0.5"
  checksum: 77ea35c92273cd2d736355ab5a4cf6477086b064a1f608489a6fef4198ee6bc1d5440e5dfd880099265ac96c4f4b4fda659d89fbaaef37ba03708663196ba8ad
  languageName: node
  linkType: hard

"@types/d3-shape@npm:*":
  version: 3.1.1
  resolution: "@types/d3-shape@npm:3.1.1"
  dependencies:
    "@types/d3-path": "*"
  checksum: 8f1762ecdeb4833a3802be1c65363cbc7cca753d0b836a3855fde4ba12f8e6fc142dba3c5f6d669a9e89374cc6dc414464e4f2d04e72fafd4bc64819ce30bb63
  languageName: node
  linkType: hard

"@types/d3-time-format@npm:*":
  version: 4.0.0
  resolution: "@types/d3-time-format@npm:4.0.0"
  checksum: ac3a841b0cd6e7f4d4c6c2cd09a2662facea6993c16b10f40fdf84f55adf7be59a6d08fd6ac1c42c27c7340f3b5eeafef968b45b052a5476a580c78a991668db
  languageName: node
  linkType: hard

"@types/d3-time@npm:*":
  version: 3.0.0
  resolution: "@types/d3-time@npm:3.0.0"
  checksum: e76adb056daccf80107f4db190ac6deb77e8774f00362bb6c76f178e67f2f217422fe502b654edbc9ac6451f6619045b9f6f5fe0db1ec5520e2ada377af7c72e
  languageName: node
  linkType: hard

"@types/d3-timer@npm:*":
  version: 3.0.0
  resolution: "@types/d3-timer@npm:3.0.0"
  checksum: 1ec86b3808de6ecfa93cfdf34254761069658af0cc1d9540e8353dbcba161cdf1296a0724187bd17433b2ff16563115fd20b85fc89d5e809ff28f9b1ab134b42
  languageName: node
  linkType: hard

"@types/d3-transition@npm:*":
  version: 3.0.3
  resolution: "@types/d3-transition@npm:3.0.3"
  dependencies:
    "@types/d3-selection": "*"
  checksum: ab9ce125108a5a5b67b972cfe23a73a0efbe958a38f51e7a2ef1003759d79f72e4563baac12400a6d6663885372bef25a50ea4433243f1a040e7fc9096b44d9d
  languageName: node
  linkType: hard

"@types/d3-zoom@npm:*":
  version: 3.0.3
  resolution: "@types/d3-zoom@npm:3.0.3"
  dependencies:
    "@types/d3-interpolate": "*"
    "@types/d3-selection": "*"
  checksum: 1c828538e2b04c47f659ead552824bed1c545dd1934f38abfec95f4a3c1cb4eaf7e107959ebd05a3dfec0166fa98677037ede6f633383c6502b1a05eaeb1d418
  languageName: node
  linkType: hard

"@types/d3@npm:^7.4.3":
  version: 7.4.3
  resolution: "@types/d3@npm:7.4.3"
  dependencies:
    "@types/d3-array": "*"
    "@types/d3-axis": "*"
    "@types/d3-brush": "*"
    "@types/d3-chord": "*"
    "@types/d3-color": "*"
    "@types/d3-contour": "*"
    "@types/d3-delaunay": "*"
    "@types/d3-dispatch": "*"
    "@types/d3-drag": "*"
    "@types/d3-dsv": "*"
    "@types/d3-ease": "*"
    "@types/d3-fetch": "*"
    "@types/d3-force": "*"
    "@types/d3-format": "*"
    "@types/d3-geo": "*"
    "@types/d3-hierarchy": "*"
    "@types/d3-interpolate": "*"
    "@types/d3-path": "*"
    "@types/d3-polygon": "*"
    "@types/d3-quadtree": "*"
    "@types/d3-random": "*"
    "@types/d3-scale": "*"
    "@types/d3-scale-chromatic": "*"
    "@types/d3-selection": "*"
    "@types/d3-shape": "*"
    "@types/d3-time": "*"
    "@types/d3-time-format": "*"
    "@types/d3-timer": "*"
    "@types/d3-transition": "*"
    "@types/d3-zoom": "*"
  checksum: 12234aa093c8661546168becdd8956e892b276f525d96f65a7b32fed886fc6a569fe5a1171bff26fef2a5663960635f460c9504a6f2d242ba281a2b6c8c6465c
  languageName: node
  linkType: hard

"@types/emscripten@npm:^1.39.6":
  version: 1.39.7
  resolution: "@types/emscripten@npm:1.39.7"
  checksum: 9871e4495358cc06cc45b2798022cd097d8ac2eb5b2fae7c276c6c5cadea05507150fad053c73ed346d4cbd844c50a3438604e5d7c3c2a7446b703cacb1ce172
  languageName: node
  linkType: hard

"@types/estree@npm:^1.0.0, @types/estree@npm:^1.0.5":
  version: 1.0.5
  resolution: "@types/estree@npm:1.0.5"
  checksum: dd8b5bed28e6213b7acd0fb665a84e693554d850b0df423ac8076cc3ad5823a6bc26b0251d080bdc545af83179ede51dd3f6fa78cad2c46ed1f29624ddf3e41a
  languageName: node
  linkType: hard

"@types/geojson@npm:*, @types/geojson@npm:7946.0.4":
  version: 7946.0.4
  resolution: "@types/geojson@npm:7946.0.4"
  checksum: 541aea46540c918b9fe21ab73f497fe17b1eaf4d0d3baeb5f5614029b7f488c37f63843b644c024a8178dc2fb66d3d6623c25d9cf61d7b553aa19c8dc7f99047
  languageName: node
  linkType: hard

"@types/glob@npm:^7.1.1":
  version: 7.2.0
  resolution: "@types/glob@npm:7.2.0"
  dependencies:
    "@types/minimatch": "*"
    "@types/node": "*"
  checksum: 6ae717fedfdfdad25f3d5a568323926c64f52ef35897bcac8aca8e19bc50c0bd84630bbd063e5d52078b2137d8e7d3c26eabebd1a2f03ff350fff8a91e79fc19
  languageName: node
  linkType: hard

"@types/html-minifier-terser@npm:^6.0.0":
  version: 6.1.0
  resolution: "@types/html-minifier-terser@npm:6.1.0"
  checksum: eb843f6a8d662d44fb18ec61041117734c6aae77aa38df1be3b4712e8e50ffaa35f1e1c92fdd0fde14a5675fecf457abcd0d15a01fae7506c91926176967f452
  languageName: node
  linkType: hard

"@types/http-cache-semantics@npm:*":
  version: 4.0.1
  resolution: "@types/http-cache-semantics@npm:4.0.1"
  checksum: 1048aacf627829f0d5f00184e16548205cd9f964bf0841c29b36bc504509230c40bc57c39778703a1c965a6f5b416ae2cbf4c1d4589c889d2838dd9dbfccf6e9
  languageName: node
  linkType: hard

"@types/json-schema@npm:^7.0.5, @types/json-schema@npm:^7.0.8, @types/json-schema@npm:^7.0.9":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: 97ed0cb44d4070aecea772b7b2e2ed971e10c81ec87dd4ecc160322ffa55ff330dace1793489540e3e318d90942064bb697cc0f8989391797792d919737b3b98
  languageName: node
  linkType: hard

"@types/keyv@npm:^3.1.4":
  version: 3.1.4
  resolution: "@types/keyv@npm:3.1.4"
  dependencies:
    "@types/node": "*"
  checksum: e009a2bfb50e90ca9b7c6e8f648f8464067271fd99116f881073fa6fa76dc8d0133181dd65e6614d5fb1220d671d67b0124aef7d97dc02d7e342ab143a47779d
  languageName: node
  linkType: hard

"@types/lodash@npm:^4.14.178, @types/lodash@npm:^4.14.191":
  version: 4.14.191
  resolution: "@types/lodash@npm:4.14.191"
  checksum: ba0d5434e10690869f32d5ea49095250157cae502f10d57de0a723fd72229ce6c6a4979576f0f13e0aa9fbe3ce2457bfb9fa7d4ec3d6daba56730a51906d1491
  languageName: node
  linkType: hard

"@types/minimatch@npm:*":
  version: 3.0.5
  resolution: "@types/minimatch@npm:3.0.5"
  checksum: c41d136f67231c3131cf1d4ca0b06687f4a322918a3a5adddc87ce90ed9dbd175a3610adee36b106ae68c0b92c637c35e02b58c8a56c424f71d30993ea220b92
  languageName: node
  linkType: hard

"@types/node@npm:*":
  version: 20.11.27
  resolution: "@types/node@npm:20.11.27"
  dependencies:
    undici-types: ~5.26.4
  checksum: 24a134a3c18e7261b6d6385c2b7714b6cf93663f30d5df8ca95c649f9cef917a422d77bfd4a18b2c7d190d3a39f445b345ecd9ef6f994e61c7e92f400ecbe80f
  languageName: node
  linkType: hard

"@types/prop-types@npm:*":
  version: 15.7.5
  resolution: "@types/prop-types@npm:15.7.5"
  checksum: 5b43b8b15415e1f298243165f1d44390403bb2bd42e662bca3b5b5633fdd39c938e91b7fce3a9483699db0f7a715d08cef220c121f723a634972fdf596aec980
  languageName: node
  linkType: hard

"@types/react@npm:^18.0.26":
  version: 18.0.28
  resolution: "@types/react@npm:18.0.28"
  dependencies:
    "@types/prop-types": "*"
    "@types/scheduler": "*"
    csstype: ^3.0.2
  checksum: e752df961105e5127652460504785897ca6e77259e0da8f233f694f9e8f451cde7fa0709d4456ade0ff600c8ce909cfe29f9b08b9c247fa9b734e126ec53edd7
  languageName: node
  linkType: hard

"@types/responselike@npm:^1.0.0":
  version: 1.0.0
  resolution: "@types/responselike@npm:1.0.0"
  dependencies:
    "@types/node": "*"
  checksum: e99fc7cc6265407987b30deda54c1c24bb1478803faf6037557a774b2f034c5b097ffd65847daa87e82a61a250d919f35c3588654b0fdaa816906650f596d1b0
  languageName: node
  linkType: hard

"@types/scheduler@npm:*":
  version: 0.16.2
  resolution: "@types/scheduler@npm:0.16.2"
  checksum: b6b4dcfeae6deba2e06a70941860fb1435730576d3689225a421280b7742318d1548b3d22c1f66ab68e414f346a9542f29240bc955b6332c5b11e561077583bc
  languageName: node
  linkType: hard

"@types/semver@npm:^7.1.0":
  version: 7.5.8
  resolution: "@types/semver@npm:7.5.8"
  checksum: ea6f5276f5b84c55921785a3a27a3cd37afee0111dfe2bcb3e03c31819c197c782598f17f0b150a69d453c9584cd14c4c4d7b9a55d2c5e6cacd4d66fdb3b3663
  languageName: node
  linkType: hard

"@types/source-list-map@npm:*":
  version: 0.1.2
  resolution: "@types/source-list-map@npm:0.1.2"
  checksum: fda8f37537aca9d3ed860d559289ab1dddb6897e642e6f53e909bbd18a7ac3129a8faa2a7d093847c91346cf09c86ef36e350c715406fba1f2271759b449adf6
  languageName: node
  linkType: hard

"@types/treeify@npm:^1.0.0":
  version: 1.0.0
  resolution: "@types/treeify@npm:1.0.0"
  checksum: 1b2397030d13beee7f82b878ca80feeddb0d550a6b00d8be30082a370c0ac5985ecf7b9378cf93ea278ff00c3e900b416ae8d9379f2c7e8caecdece1dfc77380
  languageName: node
  linkType: hard

"@types/trusted-types@npm:^2.0.7":
  version: 2.0.7
  resolution: "@types/trusted-types@npm:2.0.7"
  checksum: 8e4202766a65877efcf5d5a41b7dd458480b36195e580a3b1085ad21e948bc417d55d6f8af1fd2a7ad008015d4117d5fdfe432731157da3c68678487174e4ba3
  languageName: node
  linkType: hard

"@types/webpack-sources@npm:^0.1.5":
  version: 0.1.9
  resolution: "@types/webpack-sources@npm:0.1.9"
  dependencies:
    "@types/node": "*"
    "@types/source-list-map": "*"
    source-map: ^0.6.1
  checksum: bc09c584c7047e8aed29801a3981787dee3898e9e7a99891a362df114fcac3879eea5a00932314866a01b25220391839be09fe1487b16d4970ff4a7afd5b9725
  languageName: node
  linkType: hard

"@verdaccio/auth@npm:8.0.0-next-8.1":
  version: 8.0.0-next-8.1
  resolution: "@verdaccio/auth@npm:8.0.0-next-8.1"
  dependencies:
    "@verdaccio/config": 8.0.0-next-8.1
    "@verdaccio/core": 8.0.0-next-8.1
    "@verdaccio/loaders": 8.0.0-next-8.1
    "@verdaccio/logger": 8.0.0-next-8.1
    "@verdaccio/signature": 8.0.0-next-8.0
    "@verdaccio/utils": 7.0.1-next-8.1
    debug: 4.3.7
    lodash: 4.17.21
    verdaccio-htpasswd: 13.0.0-next-8.1
  checksum: 3bfc293a81032df993556d0c66850703d85355a85d2ea17f342863b0de021005aa52970f6fab97892eec1c38b7c9455885ad5b7477156ee6b77920c1d9112fc7
  languageName: node
  linkType: hard

"@verdaccio/commons-api@npm:10.2.0":
  version: 10.2.0
  resolution: "@verdaccio/commons-api@npm:10.2.0"
  dependencies:
    http-errors: 2.0.0
    http-status-codes: 2.2.0
  checksum: b3c946f7e15140b4e15274fa9988a8759681e9ad4316ec882096551588f554c093fb1ffbbb88ed05db162e1b0e40e9859759e1339f0ae4a074706afb7e732be2
  languageName: node
  linkType: hard

"@verdaccio/config@npm:8.0.0-next-8.1":
  version: 8.0.0-next-8.1
  resolution: "@verdaccio/config@npm:8.0.0-next-8.1"
  dependencies:
    "@verdaccio/core": 8.0.0-next-8.1
    "@verdaccio/utils": 7.0.1-next-8.1
    debug: 4.3.7
    js-yaml: 4.1.0
    lodash: 4.17.21
    minimatch: 7.4.6
  checksum: cb4c2bd4dfd7100a01a0dbbf7414434e50f0221a546ac3ffdf86e6ccbc63cfefc4bc9142a0964a8c2685ab3f44737e4c35c41f2a29c1d3d1da28ede3168f7e4b
  languageName: node
  linkType: hard

"@verdaccio/core@npm:8.0.0-next-8.1":
  version: 8.0.0-next-8.1
  resolution: "@verdaccio/core@npm:8.0.0-next-8.1"
  dependencies:
    ajv: 8.17.1
    core-js: 3.37.1
    http-errors: 2.0.0
    http-status-codes: 2.3.0
    process-warning: 1.0.0
    semver: 7.6.3
  checksum: 40cea00ababa401ef021ad2a919af01099925c4986ab4f8363639a1bde8eb618a7672aabc5e7bdc8fb0d1327df83eecdbec0c8c2776a4a4d4ce46de8c4fd9e5b
  languageName: node
  linkType: hard

"@verdaccio/file-locking@npm:10.3.1":
  version: 10.3.1
  resolution: "@verdaccio/file-locking@npm:10.3.1"
  dependencies:
    lockfile: 1.0.4
  checksum: 114948ed4ce9c0f98008eaf32355f902c4dfc91c0ce2e539a3d0c9397781e9ef8d1a2f6ce900d39dac0e054a9d7e616edbc472eedf758096fcd3cc8294d6add5
  languageName: node
  linkType: hard

"@verdaccio/file-locking@npm:13.0.0-next-8.0":
  version: 13.0.0-next-8.0
  resolution: "@verdaccio/file-locking@npm:13.0.0-next-8.0"
  dependencies:
    lockfile: 1.0.4
  checksum: 5ba07475e441d2113aa17a74dc96e682f9d15644d12282fa7954b1ed4c7e1bafaea1acb5b3790048d6fceeb6a787bb2f4ed933d9860a9f432d7d2cd3be93cec9
  languageName: node
  linkType: hard

"@verdaccio/loaders@npm:8.0.0-next-8.1":
  version: 8.0.0-next-8.1
  resolution: "@verdaccio/loaders@npm:8.0.0-next-8.1"
  dependencies:
    "@verdaccio/logger": 8.0.0-next-8.1
    debug: 4.3.7
    lodash: 4.17.21
  checksum: a03762fe73ebded25fd82da10314ad73dabc804e6b8cb0c06938effe9d2c70578208881438abb3fb4f393939993fb78f23a27b1bf472133cb63545fdeeab41f3
  languageName: node
  linkType: hard

"@verdaccio/local-storage-legacy@npm:11.0.2":
  version: 11.0.2
  resolution: "@verdaccio/local-storage-legacy@npm:11.0.2"
  dependencies:
    "@verdaccio/commons-api": 10.2.0
    "@verdaccio/file-locking": 10.3.1
    "@verdaccio/streams": 10.2.1
    async: 3.2.4
    debug: 4.3.4
    lodash: 4.17.21
    lowdb: 1.0.0
    mkdirp: 1.0.4
  checksum: e5c09028a9d67459297e6760acb1d5301a87bb3fe67a9ae7d8fb3e2deb6d907ebd113d7e883e89174eefb08afdaa9043257a5e04b8cb6702d3718ad0d8b5f731
  languageName: node
  linkType: hard

"@verdaccio/logger-7@npm:8.0.0-next-8.1":
  version: 8.0.0-next-8.1
  resolution: "@verdaccio/logger-7@npm:8.0.0-next-8.1"
  dependencies:
    "@verdaccio/logger-commons": 8.0.0-next-8.1
    pino: 7.11.0
  checksum: b10ec02a57d5cbde5adcb6cc13f2b15d5385f75f0260aea29d9d12fcacde13324d89c5b2865246ed18b3e7be1d6536ae14883c1343c7fb8464d5f2b56e02e987
  languageName: node
  linkType: hard

"@verdaccio/logger-commons@npm:8.0.0-next-8.1":
  version: 8.0.0-next-8.1
  resolution: "@verdaccio/logger-commons@npm:8.0.0-next-8.1"
  dependencies:
    "@verdaccio/core": 8.0.0-next-8.1
    "@verdaccio/logger-prettify": 8.0.0-next-8.0
    colorette: 2.0.20
    debug: 4.3.7
  checksum: 50003c0868bc8838aae129240aa9bc8c49429c4fe3aef017a1411ee339919a9e347f085f9cbe74166899cbc7ef8312c4df7a3174d5a3f931f4eab0c3156af2a8
  languageName: node
  linkType: hard

"@verdaccio/logger-prettify@npm:8.0.0-next-8.0":
  version: 8.0.0-next-8.0
  resolution: "@verdaccio/logger-prettify@npm:8.0.0-next-8.0"
  dependencies:
    colorette: 2.0.20
    dayjs: 1.11.13
    lodash: 4.17.21
    pino-abstract-transport: 1.1.0
    sonic-boom: 3.8.0
  checksum: 54e64feef2e09254677109c8eae75d3cefeac814476c5a4406c0edf18c2b814d60db50b3d078d03b9031df8d0571d8f112edf1f195ab006ff3da9080eea233e7
  languageName: node
  linkType: hard

"@verdaccio/logger@npm:8.0.0-next-8.1":
  version: 8.0.0-next-8.1
  resolution: "@verdaccio/logger@npm:8.0.0-next-8.1"
  dependencies:
    "@verdaccio/logger-commons": 8.0.0-next-8.1
    pino: 8.17.2
  checksum: 41cea3e4cb6cbcf8e3126cf66b89a2d0f60673a533af6acda3397e10a60269f0445faac4e304b92d4820a7199d3e8ea514ab14dfde23170ab01ffedae52abca4
  languageName: node
  linkType: hard

"@verdaccio/middleware@npm:8.0.0-next-8.1":
  version: 8.0.0-next-8.1
  resolution: "@verdaccio/middleware@npm:8.0.0-next-8.1"
  dependencies:
    "@verdaccio/config": 8.0.0-next-8.1
    "@verdaccio/core": 8.0.0-next-8.1
    "@verdaccio/url": 13.0.0-next-8.1
    "@verdaccio/utils": 7.0.1-next-8.1
    debug: 4.3.7
    express: 4.21.0
    express-rate-limit: 5.5.1
    lodash: 4.17.21
    lru-cache: 7.18.3
    mime: 2.6.0
  checksum: ab7d4cf690b668eafae62c8c658782c2a0d07daaf8b9ba1a60bbcca7102b9268882cbe8d019ac61a779803b0f07a178be14b1b059d48887c410e699691b1d464
  languageName: node
  linkType: hard

"@verdaccio/search-indexer@npm:8.0.0-next-8.0":
  version: 8.0.0-next-8.0
  resolution: "@verdaccio/search-indexer@npm:8.0.0-next-8.0"
  checksum: 682d82ed9870c23b1d31d1bebdd31abe819e05bcafcbb64695f2f0e2aa078b016cb646ce6d4dffeb4c281fef32ef105753865da28f7802f8cfa820b804c21ec6
  languageName: node
  linkType: hard

"@verdaccio/signature@npm:8.0.0-next-8.0":
  version: 8.0.0-next-8.0
  resolution: "@verdaccio/signature@npm:8.0.0-next-8.0"
  dependencies:
    debug: 4.3.7
    jsonwebtoken: 9.0.2
  checksum: 0720688e58a44737a8646300203e21465ed4a547a67efe224801caae0e59826d378122e412d5b6b0642c9c2f04de7d7dafc86005b87c388d7b4401a933edc6b1
  languageName: node
  linkType: hard

"@verdaccio/streams@npm:10.2.1":
  version: 10.2.1
  resolution: "@verdaccio/streams@npm:10.2.1"
  checksum: eadc671c2b40ea06da8c56bd2e5b394edbfdc2895f4e622491d4edd4312468d8172534ddaf364e107d118c7c7585f2f9114336ee93a16f0ec747c4ea15c6737b
  languageName: node
  linkType: hard

"@verdaccio/tarball@npm:13.0.0-next-8.1":
  version: 13.0.0-next-8.1
  resolution: "@verdaccio/tarball@npm:13.0.0-next-8.1"
  dependencies:
    "@verdaccio/core": 8.0.0-next-8.1
    "@verdaccio/url": 13.0.0-next-8.1
    "@verdaccio/utils": 7.0.1-next-8.1
    debug: 4.3.7
    gunzip-maybe: ^1.4.2
    lodash: 4.17.21
    tar-stream: ^3.1.7
  checksum: 23677afee3574200c33aea39476cc22a5350bae7d14a589b2816271405b4c1068c3c7ac629ae55c1d1d5b34ab0f65cc768256967c23466b51131f03c10cd63a2
  languageName: node
  linkType: hard

"@verdaccio/ui-theme@npm:8.0.0-next-8.1":
  version: 8.0.0-next-8.1
  resolution: "@verdaccio/ui-theme@npm:8.0.0-next-8.1"
  checksum: c613da907e5a3d41ff7cf221ff2feb32093146970a264308681da552ea749e68f9274f4535798b4845fe5cf0aaf2b46854a93b89ff50dc1b45d12f5e32c1d88c
  languageName: node
  linkType: hard

"@verdaccio/url@npm:13.0.0-next-8.1":
  version: 13.0.0-next-8.1
  resolution: "@verdaccio/url@npm:13.0.0-next-8.1"
  dependencies:
    "@verdaccio/core": 8.0.0-next-8.1
    debug: 4.3.7
    lodash: 4.17.21
    validator: 13.12.0
  checksum: edd32bee12f54f82016cc4d5e93dfddbcf7b1da150e789e63829d11836845cc41957864a4d15646496e9f49a28ebab11cdb3e67dbbcda02439387d5ae58834de
  languageName: node
  linkType: hard

"@verdaccio/utils@npm:7.0.1-next-8.1":
  version: 7.0.1-next-8.1
  resolution: "@verdaccio/utils@npm:7.0.1-next-8.1"
  dependencies:
    "@verdaccio/core": 8.0.0-next-8.1
    lodash: 4.17.21
    minimatch: 7.4.6
    semver: 7.6.3
  checksum: cf8a4a38cd80f6569d506f51533279b12a66bc2a24b6ee835528c210ba8988c6be804edc9b07bc5670885f65f8bf2b434ea60fba0ed890eee15460846215258b
  languageName: node
  linkType: hard

"@vscode/debugprotocol@npm:^1.51.0":
  version: 1.59.0
  resolution: "@vscode/debugprotocol@npm:1.59.0"
  checksum: 7a7de9e51f791b217da2e6f0d7a08e4732f933e973eaa24b9e7078958c8d6828401f2591334968bb067d42e4dd09ea4ad209f2e32caed8c31b4329320976af5e
  languageName: node
  linkType: hard

"@webassemblyjs/ast@npm:1.12.1, @webassemblyjs/ast@npm:^1.12.1":
  version: 1.12.1
  resolution: "@webassemblyjs/ast@npm:1.12.1"
  dependencies:
    "@webassemblyjs/helper-numbers": 1.11.6
    "@webassemblyjs/helper-wasm-bytecode": 1.11.6
  checksum: 31bcc64147236bd7b1b6d29d1f419c1f5845c785e1e42dc9e3f8ca2e05a029e9393a271b84f3a5bff2a32d35f51ff59e2181a6e5f953fe88576acd6750506202
  languageName: node
  linkType: hard

"@webassemblyjs/floating-point-hex-parser@npm:1.11.6":
  version: 1.11.6
  resolution: "@webassemblyjs/floating-point-hex-parser@npm:1.11.6"
  checksum: 29b08758841fd8b299c7152eda36b9eb4921e9c584eb4594437b5cd90ed6b920523606eae7316175f89c20628da14326801090167cc7fbffc77af448ac84b7e2
  languageName: node
  linkType: hard

"@webassemblyjs/helper-api-error@npm:1.11.6":
  version: 1.11.6
  resolution: "@webassemblyjs/helper-api-error@npm:1.11.6"
  checksum: e8563df85161096343008f9161adb138a6e8f3c2cc338d6a36011aa55eabb32f2fd138ffe63bc278d009ada001cc41d263dadd1c0be01be6c2ed99076103689f
  languageName: node
  linkType: hard

"@webassemblyjs/helper-buffer@npm:1.12.1":
  version: 1.12.1
  resolution: "@webassemblyjs/helper-buffer@npm:1.12.1"
  checksum: c3ffb723024130308db608e86e2bdccd4868bbb62dffb0a9a1530606496f79c87f8565bd8e02805ce64912b71f1a70ee5fb00307258b0c082c3abf961d097eca
  languageName: node
  linkType: hard

"@webassemblyjs/helper-numbers@npm:1.11.6":
  version: 1.11.6
  resolution: "@webassemblyjs/helper-numbers@npm:1.11.6"
  dependencies:
    "@webassemblyjs/floating-point-hex-parser": 1.11.6
    "@webassemblyjs/helper-api-error": 1.11.6
    "@xtuc/long": 4.2.2
  checksum: f4b562fa219f84368528339e0f8d273ad44e047a07641ffcaaec6f93e5b76fd86490a009aa91a294584e1436d74b0a01fa9fde45e333a4c657b58168b04da424
  languageName: node
  linkType: hard

"@webassemblyjs/helper-wasm-bytecode@npm:1.11.6":
  version: 1.11.6
  resolution: "@webassemblyjs/helper-wasm-bytecode@npm:1.11.6"
  checksum: 3535ef4f1fba38de3475e383b3980f4bbf3de72bbb631c2b6584c7df45be4eccd62c6ff48b5edd3f1bcff275cfd605a37679ec199fc91fd0a7705d7f1e3972dc
  languageName: node
  linkType: hard

"@webassemblyjs/helper-wasm-section@npm:1.12.1":
  version: 1.12.1
  resolution: "@webassemblyjs/helper-wasm-section@npm:1.12.1"
  dependencies:
    "@webassemblyjs/ast": 1.12.1
    "@webassemblyjs/helper-buffer": 1.12.1
    "@webassemblyjs/helper-wasm-bytecode": 1.11.6
    "@webassemblyjs/wasm-gen": 1.12.1
  checksum: c19810cdd2c90ff574139b6d8c0dda254d42d168a9e5b3d353d1bc085f1d7164ccd1b3c05592a45a939c47f7e403dc8d03572bb686642f06a3d02932f6f0bc8f
  languageName: node
  linkType: hard

"@webassemblyjs/ieee754@npm:1.11.6":
  version: 1.11.6
  resolution: "@webassemblyjs/ieee754@npm:1.11.6"
  dependencies:
    "@xtuc/ieee754": ^1.2.0
  checksum: 13574b8e41f6ca39b700e292d7edf102577db5650fe8add7066a320aa4b7a7c09a5056feccac7a74eb68c10dea9546d4461412af351f13f6b24b5f32379b49de
  languageName: node
  linkType: hard

"@webassemblyjs/leb128@npm:1.11.6":
  version: 1.11.6
  resolution: "@webassemblyjs/leb128@npm:1.11.6"
  dependencies:
    "@xtuc/long": 4.2.2
  checksum: 7ea942dc9777d4b18a5ebfa3a937b30ae9e1d2ce1fee637583ed7f376334dd1d4274f813d2e250056cca803e0952def4b954913f1a3c9068bcd4ab4ee5143bf0
  languageName: node
  linkType: hard

"@webassemblyjs/utf8@npm:1.11.6":
  version: 1.11.6
  resolution: "@webassemblyjs/utf8@npm:1.11.6"
  checksum: 807fe5b5ce10c390cfdd93e0fb92abda8aebabb5199980681e7c3743ee3306a75729bcd1e56a3903980e96c885ee53ef901fcbaac8efdfa480f9c0dae1d08713
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-edit@npm:^1.12.1":
  version: 1.12.1
  resolution: "@webassemblyjs/wasm-edit@npm:1.12.1"
  dependencies:
    "@webassemblyjs/ast": 1.12.1
    "@webassemblyjs/helper-buffer": 1.12.1
    "@webassemblyjs/helper-wasm-bytecode": 1.11.6
    "@webassemblyjs/helper-wasm-section": 1.12.1
    "@webassemblyjs/wasm-gen": 1.12.1
    "@webassemblyjs/wasm-opt": 1.12.1
    "@webassemblyjs/wasm-parser": 1.12.1
    "@webassemblyjs/wast-printer": 1.12.1
  checksum: ae23642303f030af888d30c4ef37b08dfec7eab6851a9575a616e65d1219f880d9223913a39056dd654e49049d76e97555b285d1f7e56935047abf578cce0692
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-gen@npm:1.12.1":
  version: 1.12.1
  resolution: "@webassemblyjs/wasm-gen@npm:1.12.1"
  dependencies:
    "@webassemblyjs/ast": 1.12.1
    "@webassemblyjs/helper-wasm-bytecode": 1.11.6
    "@webassemblyjs/ieee754": 1.11.6
    "@webassemblyjs/leb128": 1.11.6
    "@webassemblyjs/utf8": 1.11.6
  checksum: 5787626bb7f0b033044471ddd00ce0c9fe1ee4584e8b73e232051e3a4c99ba1a102700d75337151c8b6055bae77eefa4548960c610a5e4a504e356bd872138ff
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-opt@npm:1.12.1":
  version: 1.12.1
  resolution: "@webassemblyjs/wasm-opt@npm:1.12.1"
  dependencies:
    "@webassemblyjs/ast": 1.12.1
    "@webassemblyjs/helper-buffer": 1.12.1
    "@webassemblyjs/wasm-gen": 1.12.1
    "@webassemblyjs/wasm-parser": 1.12.1
  checksum: 0e8fa8a0645304a1e18ff40d3db5a2e9233ebaa169b19fcc651d6fc9fe2cac0ce092ddee927318015ae735d9cd9c5d97c0cafb6a51dcd2932ac73587b62df991
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-parser@npm:1.12.1, @webassemblyjs/wasm-parser@npm:^1.12.1":
  version: 1.12.1
  resolution: "@webassemblyjs/wasm-parser@npm:1.12.1"
  dependencies:
    "@webassemblyjs/ast": 1.12.1
    "@webassemblyjs/helper-api-error": 1.11.6
    "@webassemblyjs/helper-wasm-bytecode": 1.11.6
    "@webassemblyjs/ieee754": 1.11.6
    "@webassemblyjs/leb128": 1.11.6
    "@webassemblyjs/utf8": 1.11.6
  checksum: 176015de3551ac068cd4505d837414f258d9ade7442bd71efb1232fa26c9f6d7d4e11a5c816caeed389943f409af7ebff6899289a992d7a70343cb47009d21a8
  languageName: node
  linkType: hard

"@webassemblyjs/wast-printer@npm:1.12.1":
  version: 1.12.1
  resolution: "@webassemblyjs/wast-printer@npm:1.12.1"
  dependencies:
    "@webassemblyjs/ast": 1.12.1
    "@xtuc/long": 4.2.2
  checksum: 2974b5dda8d769145ba0efd886ea94a601e61fb37114c14f9a9a7606afc23456799af652ac3052f284909bd42edc3665a76bc9b50f95f0794c053a8a1757b713
  languageName: node
  linkType: hard

"@webpack-cli/configtest@npm:^2.0.1":
  version: 2.0.1
  resolution: "@webpack-cli/configtest@npm:2.0.1"
  peerDependencies:
    webpack: 5.x.x
    webpack-cli: 5.x.x
  checksum: 15d0ca835f2e16ec99e9f295f07b676435b9e706d7700df0ad088692fea065e34772fc44b96a4f6a86178b9ca8cf1ff941fbce15269587cf0925d70b18928cea
  languageName: node
  linkType: hard

"@webpack-cli/info@npm:^2.0.1":
  version: 2.0.1
  resolution: "@webpack-cli/info@npm:2.0.1"
  peerDependencies:
    webpack: 5.x.x
    webpack-cli: 5.x.x
  checksum: b8fba49fee10d297c2affb0b064c9a81e9038d75517c6728fb85f9fb254cae634e5d33e696dac5171e6944ae329d85fddac72f781c7d833f7e9dfe43151ce60d
  languageName: node
  linkType: hard

"@webpack-cli/serve@npm:^2.0.1":
  version: 2.0.1
  resolution: "@webpack-cli/serve@npm:2.0.1"
  peerDependencies:
    webpack: 5.x.x
    webpack-cli: 5.x.x
  peerDependenciesMeta:
    webpack-dev-server:
      optional: true
  checksum: 75c55f8398dd60e4821f81bec6e96287cebb3ab1837ef016779bc2f0c76a1d29c45b99e53daa99ba1fa156b5e2b61c19abf58098de20c2b58391b1f496ecc145
  languageName: node
  linkType: hard

"@xterm/addon-canvas@npm:~0.7.0":
  version: 0.7.0
  resolution: "@xterm/addon-canvas@npm:0.7.0"
  peerDependencies:
    "@xterm/xterm": ^5.0.0
  checksum: 28b9965f34e01a9bbcc31651dee2d4a9380aad528039484cce372b856be820705b293c4b5091cee65208c3dfbf6f944f27ecc6c3b794df5a96036cbf6dba0d74
  languageName: node
  linkType: hard

"@xterm/addon-fit@npm:~0.10.0":
  version: 0.10.0
  resolution: "@xterm/addon-fit@npm:0.10.0"
  peerDependencies:
    "@xterm/xterm": ^5.0.0
  checksum: 8edfad561c0d0316c5883cbe2ce56109f105a2b2bf53b71d5f8c788e656a3205c1093a659dddcf4025a459e4b7ff8e07b6c6a19815c8711deeded560de5f1893
  languageName: node
  linkType: hard

"@xterm/addon-web-links@npm:~0.11.0":
  version: 0.11.0
  resolution: "@xterm/addon-web-links@npm:0.11.0"
  peerDependencies:
    "@xterm/xterm": ^5.0.0
  checksum: c1b36b649b8cddc613213eb9e835daf3106a8be359a19ccacca1f1d9ff883b59d478f166109ee017fa3994d224dcdd82976ad5d5a1221271ab8d3d5684eb141d
  languageName: node
  linkType: hard

"@xterm/addon-webgl@npm:~0.18.0":
  version: 0.18.0
  resolution: "@xterm/addon-webgl@npm:0.18.0"
  peerDependencies:
    "@xterm/xterm": ^5.0.0
  checksum: 942ecaa4e08423563a795a1a63fa79637e60d4e3aba1b5fa2f426e1d7b19c553e8c8f1cfad1258461f22e05362e791f642dc6aab7e8766e26d4d3876617d8320
  languageName: node
  linkType: hard

"@xterm/xterm@npm:~5.5.0":
  version: 5.5.0
  resolution: "@xterm/xterm@npm:5.5.0"
  checksum: 393c1891b95fdd50d05e7a063abdc95a6643d2c45a4231637c23db90511426a95b1b56a5c4c91831121d2710aee9de97cf5e426016c589ca87dea8fff9a41b33
  languageName: node
  linkType: hard

"@xtuc/ieee754@npm:^1.2.0":
  version: 1.2.0
  resolution: "@xtuc/ieee754@npm:1.2.0"
  checksum: ac56d4ca6e17790f1b1677f978c0c6808b1900a5b138885d3da21732f62e30e8f0d9120fcf8f6edfff5100ca902b46f8dd7c1e3f903728634523981e80e2885a
  languageName: node
  linkType: hard

"@xtuc/long@npm:4.2.2":
  version: 4.2.2
  resolution: "@xtuc/long@npm:4.2.2"
  checksum: 8ed0d477ce3bc9c6fe2bf6a6a2cc316bb9c4127c5a7827bae947fa8ec34c7092395c5a283cc300c05b5fa01cbbfa1f938f410a7bf75db7c7846fea41949989ec
  languageName: node
  linkType: hard

"@yarnpkg/core@npm:^4.0.3":
  version: 4.0.3
  resolution: "@yarnpkg/core@npm:4.0.3"
  dependencies:
    "@arcanis/slice-ansi": ^1.1.1
    "@types/semver": ^7.1.0
    "@types/treeify": ^1.0.0
    "@yarnpkg/fslib": ^3.0.2
    "@yarnpkg/libzip": ^3.0.1
    "@yarnpkg/parsers": ^3.0.0
    "@yarnpkg/shell": ^4.0.0
    camelcase: ^5.3.1
    chalk: ^3.0.0
    ci-info: ^3.2.0
    clipanion: ^4.0.0-rc.2
    cross-spawn: 7.0.3
    diff: ^5.1.0
    dotenv: ^16.3.1
    fast-glob: ^3.2.2
    got: ^11.7.0
    lodash: ^4.17.15
    micromatch: ^4.0.2
    p-limit: ^2.2.0
    semver: ^7.1.2
    strip-ansi: ^6.0.0
    tar: ^6.0.5
    tinylogic: ^2.0.0
    treeify: ^1.1.0
    tslib: ^2.4.0
    tunnel: ^0.0.6
  checksum: 811956ab94e9f4b3601f304312baf816322df124cf85754ef3985110c90935b1b761c8266a06c2c5633f6edc39c8a4ceffeff50f08f036d7adf19612ce3761a3
  languageName: node
  linkType: hard

"@yarnpkg/fslib@npm:^3.0.0, @yarnpkg/fslib@npm:^3.0.2":
  version: 3.0.2
  resolution: "@yarnpkg/fslib@npm:3.0.2"
  dependencies:
    tslib: ^2.4.0
  checksum: b0795df777e43eb1c2bd8aa1d6c45f88b8a1d3922ee8bf264cdca916cc6f9455551a9499761a9c1f585aa576350d84adcc315e497fc031cdbbbc1a5c71942e1b
  languageName: node
  linkType: hard

"@yarnpkg/libzip@npm:^3.0.1":
  version: 3.0.1
  resolution: "@yarnpkg/libzip@npm:3.0.1"
  dependencies:
    "@types/emscripten": ^1.39.6
    "@yarnpkg/fslib": ^3.0.2
    tslib: ^2.4.0
  peerDependencies:
    "@yarnpkg/fslib": ^3.0.2
  checksum: 932b1fc35bb60ca57d033aab7a940f7b1d7bf52a68bfff6c755fe26827c0f1b373fda33e373afabbae98de3ed31ae4f0e9ee2ef24588cd074b75fb814117827d
  languageName: node
  linkType: hard

"@yarnpkg/parsers@npm:^3.0.0":
  version: 3.0.0
  resolution: "@yarnpkg/parsers@npm:3.0.0"
  dependencies:
    js-yaml: ^3.10.0
    tslib: ^2.4.0
  checksum: fefe5ecafb5bfa2b678ac9ba9259810fdda40142afd9d0b7e0e5cc1cce1fd824dffc52217c5e429807481d8fd18ead074bd317e64fd626335d3c9f1a320bade2
  languageName: node
  linkType: hard

"@yarnpkg/shell@npm:^4.0.0":
  version: 4.0.0
  resolution: "@yarnpkg/shell@npm:4.0.0"
  dependencies:
    "@yarnpkg/fslib": ^3.0.0
    "@yarnpkg/parsers": ^3.0.0
    chalk: ^3.0.0
    clipanion: ^4.0.0-rc.2
    cross-spawn: 7.0.3
    fast-glob: ^3.2.2
    micromatch: ^4.0.2
    tslib: ^2.4.0
  bin:
    shell: ./lib/cli.js
  checksum: 8497e278b1d3d0ffe324a3b9c878ca7165bbbe4d182f5ecb02f1bfaaf4dd18c8aaa54c33ee17bb37eb09173816dc4617b70c3fe0925f5fb99749687e2650b7a2
  languageName: node
  linkType: hard

"JSONStream@npm:1.3.5":
  version: 1.3.5
  resolution: "JSONStream@npm:1.3.5"
  dependencies:
    jsonparse: ^1.2.0
    through: ">=2.2.7 <3"
  bin:
    JSONStream: ./bin.js
  checksum: 2605fa124260c61bad38bb65eba30d2f72216a78e94d0ab19b11b4e0327d572b8d530c0c9cc3b0764f727ad26d39e00bf7ebad57781ca6368394d73169c59e46
  languageName: node
  linkType: hard

"abab@npm:^2.0.3":
  version: 2.0.6
  resolution: "abab@npm:2.0.6"
  checksum: 6ffc1af4ff315066c62600123990d87551ceb0aafa01e6539da77b0f5987ac7019466780bf480f1787576d4385e3690c81ccc37cfda12819bf510b8ab47e5a3e
  languageName: node
  linkType: hard

"abbrev@npm:^1.0.0":
  version: 1.1.1
  resolution: "abbrev@npm:1.1.1"
  checksum: a4a97ec07d7ea112c517036882b2ac22f3109b7b19077dc656316d07d308438aac28e4d9746dc4d84bf6b1e75b4a7b0a5f3cb30592419f128ca9a8cee3bcfa17
  languageName: node
  linkType: hard

"abort-controller@npm:^3.0.0":
  version: 3.0.0
  resolution: "abort-controller@npm:3.0.0"
  dependencies:
    event-target-shim: ^5.0.0
  checksum: 170bdba9b47b7e65906a28c8ce4f38a7a369d78e2271706f020849c1bfe0ee2067d4261df8bbb66eb84f79208fd5b710df759d64191db58cfba7ce8ef9c54b75
  languageName: node
  linkType: hard

"accepts@npm:~1.3.8":
  version: 1.3.8
  resolution: "accepts@npm:1.3.8"
  dependencies:
    mime-types: ~2.1.34
    negotiator: 0.6.3
  checksum: 50c43d32e7b50285ebe84b613ee4a3aa426715a7d131b65b786e2ead0fd76b6b60091b9916d3478a75f11f162628a2139991b6c03ab3f1d9ab7c86075dc8eab4
  languageName: node
  linkType: hard

"acorn-import-attributes@npm:^1.9.5":
  version: 1.9.5
  resolution: "acorn-import-attributes@npm:1.9.5"
  peerDependencies:
    acorn: ^8
  checksum: 1c0c49b6a244503964ae46ae850baccf306e84caf99bc2010ed6103c69a423987b07b520a6c619f075d215388bd4923eccac995886a54309eda049ab78a4be95
  languageName: node
  linkType: hard

"acorn-walk@npm:^8.0.0":
  version: 8.2.0
  resolution: "acorn-walk@npm:8.2.0"
  checksum: 1715e76c01dd7b2d4ca472f9c58968516a4899378a63ad5b6c2d668bba8da21a71976c14ec5f5b75f887b6317c4ae0b897ab141c831d741dc76024d8745f1ad1
  languageName: node
  linkType: hard

"acorn@npm:^8.0.4, acorn@npm:^8.14.0, acorn@npm:^8.5.0, acorn@npm:^8.7.1, acorn@npm:^8.8.2":
  version: 8.14.0
  resolution: "acorn@npm:8.14.0"
  bin:
    acorn: bin/acorn
  checksum: 8755074ba55fff94e84e81c72f1013c2d9c78e973c31231c8ae505a5f966859baf654bddd75046bffd73ce816b149298977fff5077a3033dedba0ae2aad152d4
  languageName: node
  linkType: hard

"agent-base@npm:6, agent-base@npm:^6.0.2":
  version: 6.0.2
  resolution: "agent-base@npm:6.0.2"
  dependencies:
    debug: 4
  checksum: f52b6872cc96fd5f622071b71ef200e01c7c4c454ee68bc9accca90c98cfb39f2810e3e9aa330435835eedc8c23f4f8a15267f67c6e245d2b33757575bdac49d
  languageName: node
  linkType: hard

"agentkeepalive@npm:^4.2.1":
  version: 4.3.0
  resolution: "agentkeepalive@npm:4.3.0"
  dependencies:
    debug: ^4.1.0
    depd: ^2.0.0
    humanize-ms: ^1.2.1
  checksum: 982453aa44c11a06826c836025e5162c846e1200adb56f2d075400da7d32d87021b3b0a58768d949d824811f5654223d5a8a3dad120921a2439625eb847c6260
  languageName: node
  linkType: hard

"aggregate-error@npm:^3.0.0":
  version: 3.1.0
  resolution: "aggregate-error@npm:3.1.0"
  dependencies:
    clean-stack: ^2.0.0
    indent-string: ^4.0.0
  checksum: 1101a33f21baa27a2fa8e04b698271e64616b886795fd43c31068c07533c7b3facfcaf4e9e0cab3624bd88f729a592f1c901a1a229c9e490eafce411a8644b79
  languageName: node
  linkType: hard

"ajv-formats@npm:^2.1.1":
  version: 2.1.1
  resolution: "ajv-formats@npm:2.1.1"
  dependencies:
    ajv: ^8.0.0
  peerDependencies:
    ajv: ^8.0.0
  peerDependenciesMeta:
    ajv:
      optional: true
  checksum: 4a287d937f1ebaad4683249a4c40c0fa3beed30d9ddc0adba04859026a622da0d317851316ea64b3680dc60f5c3c708105ddd5d5db8fe595d9d0207fd19f90b7
  languageName: node
  linkType: hard

"ajv-keywords@npm:^3.5.2":
  version: 3.5.2
  resolution: "ajv-keywords@npm:3.5.2"
  peerDependencies:
    ajv: ^6.9.1
  checksum: 7dc5e5931677a680589050f79dcbe1fefbb8fea38a955af03724229139175b433c63c68f7ae5f86cf8f65d55eb7c25f75a046723e2e58296707617ca690feae9
  languageName: node
  linkType: hard

"ajv-keywords@npm:^5.0.0":
  version: 5.1.0
  resolution: "ajv-keywords@npm:5.1.0"
  dependencies:
    fast-deep-equal: ^3.1.3
  peerDependencies:
    ajv: ^8.8.2
  checksum: c35193940b853119242c6757787f09ecf89a2c19bcd36d03ed1a615e710d19d450cb448bfda407b939aba54b002368c8bff30529cc50a0536a8e10bcce300421
  languageName: node
  linkType: hard

"ajv@npm:8.17.1, ajv@npm:^8.0.0, ajv@npm:^8.12.0, ajv@npm:^8.8.0":
  version: 8.17.1
  resolution: "ajv@npm:8.17.1"
  dependencies:
    fast-deep-equal: ^3.1.3
    fast-uri: ^3.0.1
    json-schema-traverse: ^1.0.0
    require-from-string: ^2.0.2
  checksum: 1797bf242cfffbaf3b870d13565bd1716b73f214bb7ada9a497063aada210200da36e3ed40237285f3255acc4feeae91b1fb183625331bad27da95973f7253d9
  languageName: node
  linkType: hard

"ajv@npm:^6.12.4, ajv@npm:^6.12.5":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: ^3.1.1
    fast-json-stable-stringify: ^2.0.0
    json-schema-traverse: ^0.4.1
    uri-js: ^4.2.2
  checksum: 874972efe5c4202ab0a68379481fbd3d1b5d0a7bd6d3cc21d40d3536ebff3352a2a1fabb632d4fd2cc7fe4cbdcd5ed6782084c9bbf7f32a1536d18f9da5007d4
  languageName: node
  linkType: hard

"ansi-escapes@npm:^6.0.0":
  version: 6.0.0
  resolution: "ansi-escapes@npm:6.0.0"
  dependencies:
    type-fest: ^3.0.0
  checksum: 1ddc0b27b1d040c3c703c9cd80ee0a103817e2f9fa8f1adf0c66e970b57543ec60effdb0bd1a396ed7182bca3b1a0d8fda60ec61fee862d353db81b1c3650a78
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 2aa4bb54caf2d622f1afdad09441695af2a83aa3fe8b8afa581d205e57ed4261c183c4d3877cee25794443fde5876417d859c108078ab788d6af7e4fe52eb66b
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.0.1
  resolution: "ansi-regex@npm:6.0.1"
  checksum: 1ff8b7667cded1de4fa2c9ae283e979fc87036864317da86a2e546725f96406746411d0d85e87a2d12fa5abd715d90006de7fa4fa0477c92321ad3b4c7d4e169
  languageName: node
  linkType: hard

"ansi-styles@npm:^3.2.1":
  version: 3.2.1
  resolution: "ansi-styles@npm:3.2.1"
  dependencies:
    color-convert: ^1.9.0
  checksum: d85ade01c10e5dd77b6c89f34ed7531da5830d2cb5882c645f330079975b716438cd7ebb81d0d6e6b4f9c577f19ae41ab55f07f19786b02f9dfd9e0377395665
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: ^2.0.1
  checksum: 513b44c3b2105dd14cc42a19271e80f386466c4be574bccf60b627432f9198571ebf4ab1e4c3ba17347658f4ee1711c163d574248c0c1cdc2d5917a0ad582ec4
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: ef940f2f0ced1a6347398da88a91da7930c33ecac3c77b72c5905f8b8fe402c52e6fde304ff5347f616e27a742da3f1dc76de98f6866c69251ad0b07a66776d9
  languageName: node
  linkType: hard

"anymatch@npm:~3.1.2":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: ^3.0.0
    picomatch: ^2.0.4
  checksum: 3e044fd6d1d26545f235a9fe4d7a534e2029d8e59fa7fd9f2a6eb21230f6b5380ea1eaf55136e60cbf8e613544b3b766e7a6fa2102e2a3a117505466e3025dc2
  languageName: node
  linkType: hard

"apache-md5@npm:1.1.8":
  version: 1.1.8
  resolution: "apache-md5@npm:1.1.8"
  checksum: 5f93fe00a4c75c947a8ba88054cfa9c141ea13d1581515a59637d580747581345f8cee41204af354f7280439ab19120f4bec4a1ee5cf1ac7033a7a89dbb05ada
  languageName: node
  linkType: hard

"aproba@npm:^1.0.3 || ^2.0.0":
  version: 2.0.0
  resolution: "aproba@npm:2.0.0"
  checksum: 5615cadcfb45289eea63f8afd064ab656006361020e1735112e346593856f87435e02d8dcc7ff0d11928bc7d425f27bc7c2a84f6c0b35ab0ff659c814c138a24
  languageName: node
  linkType: hard

"are-we-there-yet@npm:^3.0.0":
  version: 3.0.1
  resolution: "are-we-there-yet@npm:3.0.1"
  dependencies:
    delegates: ^1.0.0
    readable-stream: ^3.6.0
  checksum: 52590c24860fa7173bedeb69a4c05fb573473e860197f618b9a28432ee4379049336727ae3a1f9c4cb083114601c1140cee578376164d0e651217a9843f9fe83
  languageName: node
  linkType: hard

"argparse@npm:^1.0.7":
  version: 1.0.10
  resolution: "argparse@npm:1.0.10"
  dependencies:
    sprintf-js: ~1.0.2
  checksum: 7ca6e45583a28de7258e39e13d81e925cfa25d7d4aacbf806a382d3c02fcb13403a07fb8aeef949f10a7cfe4a62da0e2e807b348a5980554cc28ee573ef95945
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 83644b56493e89a254bae05702abf3a1101b4fa4d0ca31df1c9985275a5a5bd47b3c27b7fa0b71098d41114d8ca000e6ed90cad764b306f8a503665e4d517ced
  languageName: node
  linkType: hard

"array-flatten@npm:1.1.1":
  version: 1.1.1
  resolution: "array-flatten@npm:1.1.1"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"array-union@npm:^2.1.0":
  version: 2.1.0
  resolution: "array-union@npm:2.1.0"
  checksum: 5bee12395cba82da674931df6d0fea23c4aa4660cb3b338ced9f828782a65caa232573e6bf3968f23e0c5eb301764a382cef2f128b170a9dc59de0e36c39f98d
  languageName: node
  linkType: hard

"asn1@npm:~0.2.3":
  version: 0.2.6
  resolution: "asn1@npm:0.2.6"
  dependencies:
    safer-buffer: ~2.1.0
  checksum: 39f2ae343b03c15ad4f238ba561e626602a3de8d94ae536c46a4a93e69578826305366dc09fbb9b56aec39b4982a463682f259c38e59f6fa380cd72cd61e493d
  languageName: node
  linkType: hard

"assert-plus@npm:1.0.0, assert-plus@npm:^1.0.0":
  version: 1.0.0
  resolution: "assert-plus@npm:1.0.0"
  checksum: 19b4340cb8f0e6a981c07225eacac0e9d52c2644c080198765d63398f0075f83bbc0c8e95474d54224e297555ad0d631c1dcd058adb1ddc2437b41a6b424ac64
  languageName: node
  linkType: hard

"async@npm:3.2.4":
  version: 3.2.4
  resolution: "async@npm:3.2.4"
  checksum: 43d07459a4e1d09b84a20772414aa684ff4de085cbcaec6eea3c7a8f8150e8c62aa6cd4e699fe8ee93c3a5b324e777d34642531875a0817a35697522c1b02e89
  languageName: node
  linkType: hard

"async@npm:3.2.6":
  version: 3.2.6
  resolution: "async@npm:3.2.6"
  checksum: ee6eb8cd8a0ab1b58bd2a3ed6c415e93e773573a91d31df9d5ef559baafa9dab37d3b096fa7993e84585cac3697b2af6ddb9086f45d3ac8cae821bb2aab65682
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 7b78c451df768adba04e2d02e63e2d0bf3b07adcd6e42b4cf665cb7ce899bedd344c69a1dcbce355b5f972d597b25aaa1c1742b52cffd9caccb22f348114f6be
  languageName: node
  linkType: hard

"atomic-sleep@npm:^1.0.0":
  version: 1.0.0
  resolution: "atomic-sleep@npm:1.0.0"
  checksum: b95275afb2f80732f22f43a60178430c468906a415a7ff18bcd0feeebc8eec3930b51250aeda91a476062a90e07132b43a1794e8d8ffcf9b650e8139be75fa36
  languageName: node
  linkType: hard

"aws-sign2@npm:~0.7.0":
  version: 0.7.0
  resolution: "aws-sign2@npm:0.7.0"
  checksum: b148b0bb0778098ad8cf7e5fc619768bcb51236707ca1d3e5b49e41b171166d8be9fdc2ea2ae43d7decf02989d0aaa3a9c4caa6f320af95d684de9b548a71525
  languageName: node
  linkType: hard

"aws4@npm:^1.8.0":
  version: 1.12.0
  resolution: "aws4@npm:1.12.0"
  checksum: 68f79708ac7c335992730bf638286a3ee0a645cf12575d557860100767c500c08b30e24726b9f03265d74116417f628af78509e1333575e9f8d52a80edfe8cbc
  languageName: node
  linkType: hard

"b4a@npm:^1.6.4":
  version: 1.6.7
  resolution: "b4a@npm:1.6.7"
  checksum: afe4e239b49c0ef62236fe0d788ac9bd9d7eac7e9855b0d1835593cd0efcc7be394f9cc28a747a2ed2cdcb0a48c3528a551a196f472eb625457c711169c9efa2
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 9706c088a283058a8a99e0bf91b0a2f75497f185980d9ffa8b304de1d9e58ebda7c72c07ebf01dadedaac5b2907b2c6f566f660d62bd336c3468e960403b9d65
  languageName: node
  linkType: hard

"bare-events@npm:^2.2.0":
  version: 2.5.4
  resolution: "bare-events@npm:2.5.4"
  checksum: 522a5401caaede9d8c857c2fd346c993bf43995e958e8ebfa79d32b1e086032800e0639f3559d7ad85788fae54f6d9605685de507eec54298ea2aa2c8c9cb2c3
  languageName: node
  linkType: hard

"base16@npm:^1.0.0":
  version: 1.0.0
  resolution: "base16@npm:1.0.0"
  checksum: 0cd449a2db0f0f957e4b6b57e33bc43c9e20d4f1dd744065db94b5da35e8e71fa4dc4bc7a901e59a84d5f8b6936e3c520e2471787f667fc155fb0f50d8540f5d
  languageName: node
  linkType: hard

"base64-js@npm:^1.3.1":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: 669632eb3745404c2f822a18fc3a0122d2f9a7a13f7fb8b5823ee19d1d2ff9ee5b52c53367176ea4ad093c332fd5ab4bd0ebae5a8e27917a4105a4cfc86b1005
  languageName: node
  linkType: hard

"bcrypt-pbkdf@npm:^1.0.0":
  version: 1.0.2
  resolution: "bcrypt-pbkdf@npm:1.0.2"
  dependencies:
    tweetnacl: ^0.14.3
  checksum: 4edfc9fe7d07019609ccf797a2af28351736e9d012c8402a07120c4453a3b789a15f2ee1530dc49eee8f7eb9379331a8dd4b3766042b9e502f74a68e7f662291
  languageName: node
  linkType: hard

"bcryptjs@npm:2.4.3":
  version: 2.4.3
  resolution: "bcryptjs@npm:2.4.3"
  checksum: 0e80ed852a41f5dfb1853f53ee14a7390b0ef263ce05dba6e2ef3cd919dfad025a7c21ebcfe5bc7fa04b100990edf90c7a877ff7fe623d3e479753253131b629
  languageName: node
  linkType: hard

"big.js@npm:^5.2.2":
  version: 5.2.2
  resolution: "big.js@npm:5.2.2"
  checksum: b89b6e8419b097a8fb4ed2399a1931a68c612bce3cfd5ca8c214b2d017531191070f990598de2fc6f3f993d91c0f08aa82697717f6b3b8732c9731866d233c9e
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.0.0":
  version: 2.2.0
  resolution: "binary-extensions@npm:2.2.0"
  checksum: ccd267956c58d2315f5d3ea6757cf09863c5fc703e50fbeb13a7dc849b812ef76e3cf9ca8f35a0c48498776a7478d7b4a0418e1e2b8cb9cb9731f2922aaad7f8
  languageName: node
  linkType: hard

"bl@npm:^5.0.0":
  version: 5.1.0
  resolution: "bl@npm:5.1.0"
  dependencies:
    buffer: ^6.0.3
    inherits: ^2.0.4
    readable-stream: ^3.4.0
  checksum: a7a438ee0bc540e80b8eb68cc1ad759a9c87df06874a99411d701d01cc0b36f30cd20050512ac3e77090138890960e07bfee724f3ee6619bb39a569f5cc3b1bc
  languageName: node
  linkType: hard

"body-parser@npm:1.20.3":
  version: 1.20.3
  resolution: "body-parser@npm:1.20.3"
  dependencies:
    bytes: 3.1.2
    content-type: ~1.0.5
    debug: 2.6.9
    depd: 2.0.0
    destroy: 1.2.0
    http-errors: 2.0.0
    iconv-lite: 0.4.24
    on-finished: 2.4.1
    qs: 6.13.0
    raw-body: 2.5.2
    type-is: ~1.6.18
    unpipe: 1.0.0
  checksum: 1a35c59a6be8d852b00946330141c4f142c6af0f970faa87f10ad74f1ee7118078056706a05ae3093c54dabca9cd3770fa62a170a85801da1a4324f04381167d
  languageName: node
  linkType: hard

"boolbase@npm:^1.0.0":
  version: 1.0.0
  resolution: "boolbase@npm:1.0.0"
  checksum: 3e25c80ef626c3a3487c73dbfc70ac322ec830666c9ad915d11b701142fab25ec1e63eff2c450c74347acfd2de854ccde865cd79ef4db1683f7c7b046ea43bb0
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: ^1.0.0
    concat-map: 0.0.1
  checksum: faf34a7bb0c3fcf4b59c7808bc5d2a96a40988addf2e7e09dfbb67a2251800e0d14cd2bfc1aa79174f2f5095c54ff27f46fb1289fe2d77dac755b5eb3434cc07
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.1
  resolution: "brace-expansion@npm:2.0.1"
  dependencies:
    balanced-match: ^1.0.0
  checksum: a61e7cd2e8a8505e9f0036b3b6108ba5e926b4b55089eeb5550cd04a471fe216c96d4fe7e4c7f995c728c554ae20ddfc4244cad10aef255e72b62930afd233d1
  languageName: node
  linkType: hard

"braces@npm:^3.0.2, braces@npm:~3.0.2":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: ^7.1.1
  checksum: b95aa0b3bd909f6cd1720ffcf031aeaf46154dd88b4da01f9a1d3f7ea866a79eba76a6d01cbc3c422b2ee5cdc39a4f02491058d5df0d7bf6e6a162a832df1f69
  languageName: node
  linkType: hard

"browserify-zlib@npm:^0.1.4":
  version: 0.1.4
  resolution: "browserify-zlib@npm:0.1.4"
  dependencies:
    pako: ~0.2.0
  checksum: abee4cb4349e8a21391fd874564f41b113fe691372913980e6fa06a777e4ea2aad4e942af14ab99bce190d5ac8f5328201432f4ef0eae48c6d02208bc212976f
  languageName: node
  linkType: hard

"browserslist@npm:^4.21.10":
  version: 4.23.3
  resolution: "browserslist@npm:4.23.3"
  dependencies:
    caniuse-lite: ^1.0.30001646
    electron-to-chromium: ^1.5.4
    node-releases: ^2.0.18
    update-browserslist-db: ^1.1.0
  bin:
    browserslist: cli.js
  checksum: 7906064f9970aeb941310b2fcb8b4ace4a1b50aa657c986677c6f1553a8cabcc94ee9c5922f715baffbedaa0e6cf0831b6fed7b059dde6873a4bfadcbe069c7e
  languageName: node
  linkType: hard

"buffer-equal-constant-time@npm:1.0.1":
  version: 1.0.1
  resolution: "buffer-equal-constant-time@npm:1.0.1"
  checksum: 80bb945f5d782a56f374b292770901065bad21420e34936ecbe949e57724b4a13874f735850dd1cc61f078773c4fb5493a41391e7bda40d1fa388d6bd80daaab
  languageName: node
  linkType: hard

"buffer-from@npm:^1.0.0":
  version: 1.1.2
  resolution: "buffer-from@npm:1.1.2"
  checksum: 0448524a562b37d4d7ed9efd91685a5b77a50672c556ea254ac9a6d30e3403a517d8981f10e565db24e8339413b43c97ca2951f10e399c6125a0d8911f5679bb
  languageName: node
  linkType: hard

"buffer@npm:^6.0.3":
  version: 6.0.3
  resolution: "buffer@npm:6.0.3"
  dependencies:
    base64-js: ^1.3.1
    ieee754: ^1.2.1
  checksum: 5ad23293d9a731e4318e420025800b42bf0d264004c0286c8cc010af7a270c7a0f6522e84f54b9ad65cbd6db20b8badbfd8d2ebf4f80fa03dab093b89e68c3f9
  languageName: node
  linkType: hard

"bytes@npm:3.1.2":
  version: 3.1.2
  resolution: "bytes@npm:3.1.2"
  checksum: e4bcd3948d289c5127591fbedf10c0b639ccbf00243504e4e127374a15c3bc8eed0d28d4aaab08ff6f1cf2abc0cce6ba3085ed32f4f90e82a5683ce0014e1b6e
  languageName: node
  linkType: hard

"cacache@npm:^16.1.0":
  version: 16.1.3
  resolution: "cacache@npm:16.1.3"
  dependencies:
    "@npmcli/fs": ^2.1.0
    "@npmcli/move-file": ^2.0.0
    chownr: ^2.0.0
    fs-minipass: ^2.1.0
    glob: ^8.0.1
    infer-owner: ^1.0.4
    lru-cache: ^7.7.1
    minipass: ^3.1.6
    minipass-collect: ^1.0.2
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    mkdirp: ^1.0.4
    p-map: ^4.0.0
    promise-inflight: ^1.0.1
    rimraf: ^3.0.2
    ssri: ^9.0.0
    tar: ^6.1.11
    unique-filename: ^2.0.0
  checksum: d91409e6e57d7d9a3a25e5dcc589c84e75b178ae8ea7de05cbf6b783f77a5fae938f6e8fda6f5257ed70000be27a681e1e44829251bfffe4c10216002f8f14e6
  languageName: node
  linkType: hard

"cacheable-lookup@npm:^5.0.3":
  version: 5.0.4
  resolution: "cacheable-lookup@npm:5.0.4"
  checksum: 763e02cf9196bc9afccacd8c418d942fc2677f22261969a4c2c2e760fa44a2351a81557bd908291c3921fe9beb10b976ba8fa50c5ca837c5a0dd945f16468f2d
  languageName: node
  linkType: hard

"cacheable-request@npm:^7.0.2":
  version: 7.0.2
  resolution: "cacheable-request@npm:7.0.2"
  dependencies:
    clone-response: ^1.0.2
    get-stream: ^5.1.0
    http-cache-semantics: ^4.0.0
    keyv: ^4.0.0
    lowercase-keys: ^2.0.0
    normalize-url: ^6.0.1
    responselike: ^2.0.0
  checksum: 6152813982945a5c9989cb457a6c499f12edcc7ade323d2fbfd759abc860bdbd1306e08096916bb413c3c47e812f8e4c0a0cc1e112c8ce94381a960f115bc77f
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.1":
  version: 1.0.2
  resolution: "call-bind-apply-helpers@npm:1.0.2"
  dependencies:
    es-errors: ^1.3.0
    function-bind: ^1.1.2
  checksum: b2863d74fcf2a6948221f65d95b91b4b2d90cfe8927650b506141e669f7d5de65cea191bf788838bc40d13846b7886c5bc5c84ab96c3adbcf88ad69a72fcdc6b
  languageName: node
  linkType: hard

"call-bound@npm:^1.0.2":
  version: 1.0.3
  resolution: "call-bound@npm:1.0.3"
  dependencies:
    call-bind-apply-helpers: ^1.0.1
    get-intrinsic: ^1.2.6
  checksum: a93bbe0f2d0a2d6c144a4349ccd0593d5d0d5d9309b69101710644af8964286420062f2cc3114dca120b9bc8cc07507952d4b1b3ea7672e0d7f6f1675efedb32
  languageName: node
  linkType: hard

"camel-case@npm:^4.1.1, camel-case@npm:^4.1.2":
  version: 4.1.2
  resolution: "camel-case@npm:4.1.2"
  dependencies:
    pascal-case: ^3.1.2
    tslib: ^2.0.3
  checksum: bcbd25cd253b3cbc69be3f535750137dbf2beb70f093bdc575f73f800acc8443d34fd52ab8f0a2413c34f1e8203139ffc88428d8863e4dfe530cfb257a379ad6
  languageName: node
  linkType: hard

"camelcase@npm:^5.3.1":
  version: 5.3.1
  resolution: "camelcase@npm:5.3.1"
  checksum: e6effce26b9404e3c0f301498184f243811c30dfe6d0b9051863bd8e4034d09c8c2923794f280d6827e5aa055f6c434115ff97864a16a963366fb35fd673024b
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001646":
  version: 1.0.30001660
  resolution: "caniuse-lite@npm:1.0.30001660"
  checksum: 8b2c5de2f5facd31980426afbba68238270984acfe8c1ae925b8b6480448eea2fae292f815674617e9170c730c8a238d7cc0db919f184dc0e3cd9bec18f5e5ad
  languageName: node
  linkType: hard

"caseless@npm:~0.12.0":
  version: 0.12.0
  resolution: "caseless@npm:0.12.0"
  checksum: b43bd4c440aa1e8ee6baefee8063b4850fd0d7b378f6aabc796c9ec8cb26d27fb30b46885350777d9bd079c5256c0e1329ad0dc7c2817e0bb466810ebb353751
  languageName: node
  linkType: hard

"chalk@npm:^2.3.0":
  version: 2.4.2
  resolution: "chalk@npm:2.4.2"
  dependencies:
    ansi-styles: ^3.2.1
    escape-string-regexp: ^1.0.5
    supports-color: ^5.3.0
  checksum: ec3661d38fe77f681200f878edbd9448821924e0f93a9cefc0e26a33b145f1027a2084bf19967160d11e1f03bfe4eaffcabf5493b89098b2782c3fe0b03d80c2
  languageName: node
  linkType: hard

"chalk@npm:^3.0.0":
  version: 3.0.0
  resolution: "chalk@npm:3.0.0"
  dependencies:
    ansi-styles: ^4.1.0
    supports-color: ^7.1.0
  checksum: 8e3ddf3981c4da405ddbd7d9c8d91944ddf6e33d6837756979f7840a29272a69a5189ecae0ff84006750d6d1e92368d413335eab4db5476db6e6703a1d1e0505
  languageName: node
  linkType: hard

"chalk@npm:^4.1.0":
  version: 4.1.0
  resolution: "chalk@npm:4.1.0"
  dependencies:
    ansi-styles: ^4.1.0
    supports-color: ^7.1.0
  checksum: 5561c7b4c063badee3e16d04bce50bd033e1be1bf4c6948639275683ffa7a1993c44639b43c22b1c505f0f813a24b1889037eb182546b48946f9fe7cdd0e7d13
  languageName: node
  linkType: hard

"chalk@npm:^5.0.0, chalk@npm:^5.1.2":
  version: 5.2.0
  resolution: "chalk@npm:5.2.0"
  checksum: 03d8060277de6cf2fd567dc25fcf770593eb5bb85f460ce443e49255a30ff1242edd0c90a06a03803b0466ff0687a939b41db1757bec987113e83de89a003caa
  languageName: node
  linkType: hard

"chardet@npm:^0.7.0":
  version: 0.7.0
  resolution: "chardet@npm:0.7.0"
  checksum: 6fd5da1f5d18ff5712c1e0aed41da200d7c51c28f11b36ee3c7b483f3696dabc08927fc6b227735eb8f0e1215c9a8abd8154637f3eff8cada5959df7f58b024d
  languageName: node
  linkType: hard

"chevrotain-allstar@npm:~0.3.0":
  version: 0.3.1
  resolution: "chevrotain-allstar@npm:0.3.1"
  dependencies:
    lodash-es: ^4.17.21
  peerDependencies:
    chevrotain: ^11.0.0
  checksum: 5f5213693886d03ca04ffacc57f7424b5c8015e7a62de3c193c3bc94ae7472f113e9fab7f4e92ce0553c181483950a170576897d7b695aac6196ce32b988475e
  languageName: node
  linkType: hard

"chevrotain@npm:~11.0.3":
  version: 11.0.3
  resolution: "chevrotain@npm:11.0.3"
  dependencies:
    "@chevrotain/cst-dts-gen": 11.0.3
    "@chevrotain/gast": 11.0.3
    "@chevrotain/regexp-to-ast": 11.0.3
    "@chevrotain/types": 11.0.3
    "@chevrotain/utils": 11.0.3
    lodash-es: 4.17.21
  checksum: 43abce4ef2be2ae499027066ad5bfb2dd6b838423108adc69839133655b925a4d86212b97125d8deef9f84dc173b34457eedf59a2d178b6d0b2a0d2e2a7762a4
  languageName: node
  linkType: hard

"chokidar@npm:^3.4.0":
  version: 3.5.3
  resolution: "chokidar@npm:3.5.3"
  dependencies:
    anymatch: ~3.1.2
    braces: ~3.0.2
    fsevents: ~2.3.2
    glob-parent: ~5.1.2
    is-binary-path: ~2.1.0
    is-glob: ~4.0.1
    normalize-path: ~3.0.0
    readdirp: ~3.6.0
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: b49fcde40176ba007ff361b198a2d35df60d9bb2a5aab228279eb810feae9294a6b4649ab15981304447afe1e6ffbf4788ad5db77235dc770ab777c6e771980c
  languageName: node
  linkType: hard

"chownr@npm:^2.0.0":
  version: 2.0.0
  resolution: "chownr@npm:2.0.0"
  checksum: c57cf9dd0791e2f18a5ee9c1a299ae6e801ff58fee96dc8bfd0dcb4738a6ce58dd252a3605b1c93c6418fe4f9d5093b28ffbf4d66648cb2a9c67eaef9679be2f
  languageName: node
  linkType: hard

"chrome-trace-event@npm:^1.0.2":
  version: 1.0.3
  resolution: "chrome-trace-event@npm:1.0.3"
  checksum: cb8b1fc7e881aaef973bd0c4a43cd353c2ad8323fb471a041e64f7c2dd849cde4aad15f8b753331a32dda45c973f032c8a03b8177fc85d60eaa75e91e08bfb97
  languageName: node
  linkType: hard

"ci-info@npm:^3.2.0":
  version: 3.8.0
  resolution: "ci-info@npm:3.8.0"
  checksum: d0a4d3160497cae54294974a7246202244fff031b0a6ea20dd57b10ec510aa17399c41a1b0982142c105f3255aff2173e5c0dd7302ee1b2f28ba3debda375098
  languageName: node
  linkType: hard

"clean-css@npm:^4.2.3":
  version: 4.2.4
  resolution: "clean-css@npm:4.2.4"
  dependencies:
    source-map: ~0.6.0
  checksum: 045ff6fcf4b5c76a084b24e1633e0c78a13b24080338fc8544565a9751559aa32ff4ee5886d9e52c18a644a6ff119bd8e37bc58e574377c05382a1fb7dbe39f8
  languageName: node
  linkType: hard

"clean-css@npm:^5.2.2":
  version: 5.3.2
  resolution: "clean-css@npm:5.3.2"
  dependencies:
    source-map: ~0.6.0
  checksum: 8787b281acc9878f309b5f835d410085deedfd4e126472666773040a6a8a72f472a1d24185947d23b87b1c419bf2c5ed429395d5c5ff8279c98b05d8011e9758
  languageName: node
  linkType: hard

"clean-stack@npm:^2.0.0":
  version: 2.2.0
  resolution: "clean-stack@npm:2.2.0"
  checksum: 2ac8cd2b2f5ec986a3c743935ec85b07bc174d5421a5efc8017e1f146a1cf5f781ae962618f416352103b32c9cd7e203276e8c28241bbe946160cab16149fb68
  languageName: node
  linkType: hard

"cli-cursor@npm:^4.0.0":
  version: 4.0.0
  resolution: "cli-cursor@npm:4.0.0"
  dependencies:
    restore-cursor: ^4.0.0
  checksum: ab3f3ea2076e2176a1da29f9d64f72ec3efad51c0960898b56c8a17671365c26e67b735920530eaf7328d61f8bd41c27f46b9cf6e4e10fe2fa44b5e8c0e392cc
  languageName: node
  linkType: hard

"cli-spinners@npm:^2.6.1":
  version: 2.6.1
  resolution: "cli-spinners@npm:2.6.1"
  checksum: 423409baaa7a58e5104b46ca1745fbfc5888bbd0b0c5a626e052ae1387060839c8efd512fb127e25769b3dc9562db1dc1b5add6e0b93b7ef64f477feb6416a45
  languageName: node
  linkType: hard

"cli-width@npm:^4.0.0":
  version: 4.0.0
  resolution: "cli-width@npm:4.0.0"
  checksum: 1ec12311217cc8b2d018646a58b61424d2348def598fb58ba2c32e28f0bcb59a35cef168110311cefe3340abf00e5171b351de6c3e2c084bd1642e6e2a9e144e
  languageName: node
  linkType: hard

"clipanion@npm:4.0.0-rc.4, clipanion@npm:^4.0.0-rc.2":
  version: 4.0.0-rc.4
  resolution: "clipanion@npm:4.0.0-rc.4"
  dependencies:
    typanion: ^3.8.0
  peerDependencies:
    typanion: "*"
  checksum: a92aa03b24eb89292b7bda570973c164fff16a1c5ba4c4abdd1b0dd6110a57651752114ec9f5cfc29e2040213e514b3220142a2316c4fc4e659ba423caa296c7
  languageName: node
  linkType: hard

"cliui@npm:^8.0.1":
  version: 8.0.1
  resolution: "cliui@npm:8.0.1"
  dependencies:
    string-width: ^4.2.0
    strip-ansi: ^6.0.1
    wrap-ansi: ^7.0.0
  checksum: 79648b3b0045f2e285b76fb2e24e207c6db44323581e421c3acbd0e86454cba1b37aea976ab50195a49e7384b871e6dfb2247ad7dec53c02454ac6497394cb56
  languageName: node
  linkType: hard

"clone-deep@npm:^4.0.1":
  version: 4.0.1
  resolution: "clone-deep@npm:4.0.1"
  dependencies:
    is-plain-object: ^2.0.4
    kind-of: ^6.0.2
    shallow-clone: ^3.0.0
  checksum: 770f912fe4e6f21873c8e8fbb1e99134db3b93da32df271d00589ea4a29dbe83a9808a322c93f3bcaf8584b8b4fa6fc269fc8032efbaa6728e0c9886c74467d2
  languageName: node
  linkType: hard

"clone-response@npm:^1.0.2":
  version: 1.0.3
  resolution: "clone-response@npm:1.0.3"
  dependencies:
    mimic-response: ^1.0.0
  checksum: 4e671cac39b11c60aa8ba0a450657194a5d6504df51bca3fac5b3bd0145c4f8e8464898f87c8406b83232e3bc5cca555f51c1f9c8ac023969ebfbf7f6bdabb2e
  languageName: node
  linkType: hard

"clone@npm:^1.0.2":
  version: 1.0.4
  resolution: "clone@npm:1.0.4"
  checksum: d06418b7335897209e77bdd430d04f882189582e67bd1f75a04565f3f07f5b3f119a9d670c943b6697d0afb100f03b866b3b8a1f91d4d02d72c4ecf2bb64b5dd
  languageName: node
  linkType: hard

"clone@npm:~2.1.2":
  version: 2.1.2
  resolution: "clone@npm:2.1.2"
  checksum: aaf106e9bc025b21333e2f4c12da539b568db4925c0501a1bf4070836c9e848c892fa22c35548ce0d1132b08bbbfa17a00144fe58fccdab6fa900fec4250f67d
  languageName: node
  linkType: hard

"clsx@npm:^1.1.1":
  version: 1.2.1
  resolution: "clsx@npm:1.2.1"
  checksum: 30befca8019b2eb7dbad38cff6266cf543091dae2825c856a62a8ccf2c3ab9c2907c4d12b288b73101196767f66812365400a227581484a05f968b0307cfaf12
  languageName: node
  linkType: hard

"color-convert@npm:^1.9.0, color-convert@npm:^1.9.3":
  version: 1.9.3
  resolution: "color-convert@npm:1.9.3"
  dependencies:
    color-name: 1.1.3
  checksum: fd7a64a17cde98fb923b1dd05c5f2e6f7aefda1b60d67e8d449f9328b4e53b228a428fd38bfeaeb2db2ff6b6503a776a996150b80cdf224062af08a5c8a3a203
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: ~1.1.4
  checksum: 79e6bdb9fd479a205c71d89574fccfb22bd9053bd98c6c4d870d65c132e5e904e6034978e55b43d69fcaa7433af2016ee203ce76eeba9cfa554b373e7f7db336
  languageName: node
  linkType: hard

"color-name@npm:1.1.3":
  version: 1.1.3
  resolution: "color-name@npm:1.1.3"
  checksum: 09c5d3e33d2105850153b14466501f2bfb30324a2f76568a408763a3b7433b0e50e5b4ab1947868e65cb101bb7cb75029553f2c333b6d4b8138a73fcc133d69d
  languageName: node
  linkType: hard

"color-name@npm:^1.0.0, color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: b0445859521eb4021cd0fb0cc1a75cecf67fceecae89b63f62b201cca8d345baf8b952c966862a9d9a2632987d4f6581f0ec8d957dfacece86f0a7919316f610
  languageName: node
  linkType: hard

"color-string@npm:^1.6.0":
  version: 1.9.1
  resolution: "color-string@npm:1.9.1"
  dependencies:
    color-name: ^1.0.0
    simple-swizzle: ^0.2.2
  checksum: c13fe7cff7885f603f49105827d621ce87f4571d78ba28ef4a3f1a104304748f620615e6bf065ecd2145d0d9dad83a3553f52bb25ede7239d18e9f81622f1cc5
  languageName: node
  linkType: hard

"color-support@npm:^1.1.3":
  version: 1.1.3
  resolution: "color-support@npm:1.1.3"
  bin:
    color-support: bin.js
  checksum: 9b7356817670b9a13a26ca5af1c21615463b500783b739b7634a0c2047c16cef4b2865d7576875c31c3cddf9dd621fa19285e628f20198b233a5cfdda6d0793b
  languageName: node
  linkType: hard

"color@npm:^3.2.1":
  version: 3.2.1
  resolution: "color@npm:3.2.1"
  dependencies:
    color-convert: ^1.9.3
    color-string: ^1.6.0
  checksum: f81220e8b774d35865c2561be921f5652117638dcda7ca4029262046e37fc2444ac7bbfdd110cf1fd9c074a4ee5eda8f85944ffbdda26186b602dd9bb05f6400
  languageName: node
  linkType: hard

"colorette@npm:2.0.20, colorette@npm:^2.0.14":
  version: 2.0.20
  resolution: "colorette@npm:2.0.20"
  checksum: 0c016fea2b91b733eb9f4bcdb580018f52c0bc0979443dad930e5037a968237ac53d9beb98e218d2e9235834f8eebce7f8e080422d6194e957454255bde71d3d
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.8, combined-stream@npm:~1.0.6":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: ~1.0.0
  checksum: 49fa4aeb4916567e33ea81d088f6584749fc90c7abec76fd516bf1c5aa5c79f3584b5ba3de6b86d26ddd64bae5329c4c7479343250cfe71c75bb366eae53bb7c
  languageName: node
  linkType: hard

"commander@npm:2, commander@npm:^2.20.0":
  version: 2.20.3
  resolution: "commander@npm:2.20.3"
  checksum: ab8c07884e42c3a8dbc5dd9592c606176c7eb5c1ca5ff274bcf907039b2c41de3626f684ea75ccf4d361ba004bbaff1f577d5384c155f3871e456bdf27becf9e
  languageName: node
  linkType: hard

"commander@npm:7, commander@npm:^7.2.0":
  version: 7.2.0
  resolution: "commander@npm:7.2.0"
  checksum: 53501cbeee61d5157546c0bef0fedb6cdfc763a882136284bed9a07225f09a14b82d2a84e7637edfd1a679fb35ed9502fd58ef1d091e6287f60d790147f68ddc
  languageName: node
  linkType: hard

"commander@npm:9.2.0":
  version: 9.2.0
  resolution: "commander@npm:9.2.0"
  checksum: 7c82e4cd969712aa6d7c055b8351807a7230f9f31ef7ec7881e11a1147511de85adf5d6ccfd200240a118eecf693b220caf6865b8efbcea558a70d35aa9ed711
  languageName: node
  linkType: hard

"commander@npm:^4.1.1":
  version: 4.1.1
  resolution: "commander@npm:4.1.1"
  checksum: d7b9913ff92cae20cb577a4ac6fcc121bd6223319e54a40f51a14740a681ad5c574fd29a57da478a5f234a6fa6c52cbf0b7c641353e03c648b1ae85ba670b977
  languageName: node
  linkType: hard

"commander@npm:^8.3.0":
  version: 8.3.0
  resolution: "commander@npm:8.3.0"
  checksum: 0f82321821fc27b83bd409510bb9deeebcfa799ff0bf5d102128b500b7af22872c0c92cb6a0ebc5a4cf19c6b550fba9cedfa7329d18c6442a625f851377bacf0
  languageName: node
  linkType: hard

"commander@npm:^9.4.1":
  version: 9.5.0
  resolution: "commander@npm:9.5.0"
  checksum: c7a3e27aa59e913b54a1bafd366b88650bc41d6651f0cbe258d4ff09d43d6a7394232a4dadd0bf518b3e696fdf595db1028a0d82c785b88bd61f8a440cecfade
  languageName: node
  linkType: hard

"compressible@npm:~2.0.18":
  version: 2.0.18
  resolution: "compressible@npm:2.0.18"
  dependencies:
    mime-db: ">= 1.43.0 < 2"
  checksum: 58321a85b375d39230405654721353f709d0c1442129e9a17081771b816302a012471a9b8f4864c7dbe02eef7f2aaac3c614795197092262e94b409c9be108f0
  languageName: node
  linkType: hard

"compression@npm:1.7.5":
  version: 1.7.5
  resolution: "compression@npm:1.7.5"
  dependencies:
    bytes: 3.1.2
    compressible: ~2.0.18
    debug: 2.6.9
    negotiator: ~0.6.4
    on-headers: ~1.0.2
    safe-buffer: 5.2.1
    vary: ~1.1.2
  checksum: d624b5562492518eee82c4f1381ea36f69f1f10b4283bfc2dcafd7d4d7eeed17c3f0e8f2951798594b7064db7ac5a6198df34816bde2d56bb7c75ce1570880e9
  languageName: node
  linkType: hard

"compute-gcd@npm:^1.2.1":
  version: 1.2.1
  resolution: "compute-gcd@npm:1.2.1"
  dependencies:
    validate.io-array: ^1.0.3
    validate.io-function: ^1.0.2
    validate.io-integer-array: ^1.0.0
  checksum: 51cf33b75f7c8db5142fcb99a9d84a40260993fed8e02a7ab443834186c3ab99b3fd20b30ad9075a6a9d959d69df6da74dd3be8a59c78d9f2fe780ebda8242e1
  languageName: node
  linkType: hard

"compute-lcm@npm:^1.1.2":
  version: 1.1.2
  resolution: "compute-lcm@npm:1.1.2"
  dependencies:
    compute-gcd: ^1.2.1
    validate.io-array: ^1.0.3
    validate.io-function: ^1.0.2
    validate.io-integer-array: ^1.0.0
  checksum: d499ab57dcb48e8d0fd233b99844a06d1cc56115602c920c586e998ebba60293731f5b6976e8a1e83ae6cbfe86716f62d9432e8d94913fed8bd8352f447dc917
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 902a9f5d8967a3e2faf138d5cb784b9979bad2e6db5357c5b21c568df4ebe62bcb15108af1b2253744844eb964fc023fbd9afbbbb6ddd0bcc204c6fb5b7bf3af
  languageName: node
  linkType: hard

"confbox@npm:^0.1.8":
  version: 0.1.8
  resolution: "confbox@npm:0.1.8"
  checksum: 5c7718ab22cf9e35a31c21ef124156076ae8c9dc65e6463d54961caf5a1d529284485a0fdf83fd23b27329f3b75b0c8c07d2e36c699f5151a2efe903343f976a
  languageName: node
  linkType: hard

"confbox@npm:^0.2.1":
  version: 0.2.1
  resolution: "confbox@npm:0.2.1"
  checksum: a8c511a5aa7c9246ccc8997711bebb69413c6ffb66059860b53d46fe7d948bd6753aede559bdf8f62ab36e1e6be95b57919519fe8b04c53c78a92b63a50f07f1
  languageName: node
  linkType: hard

"console-control-strings@npm:^1.1.0":
  version: 1.1.0
  resolution: "console-control-strings@npm:1.1.0"
  checksum: 8755d76787f94e6cf79ce4666f0c5519906d7f5b02d4b884cf41e11dcd759ed69c57da0670afd9236d229a46e0f9cf519db0cd829c6dca820bb5a5c3def584ed
  languageName: node
  linkType: hard

"content-disposition@npm:0.5.4":
  version: 0.5.4
  resolution: "content-disposition@npm:0.5.4"
  dependencies:
    safe-buffer: 5.2.1
  checksum: afb9d545e296a5171d7574fcad634b2fdf698875f4006a9dd04a3e1333880c5c0c98d47b560d01216fb6505a54a2ba6a843ee3a02ec86d7e911e8315255f56c3
  languageName: node
  linkType: hard

"content-type@npm:~1.0.4, content-type@npm:~1.0.5":
  version: 1.0.5
  resolution: "content-type@npm:1.0.5"
  checksum: 566271e0a251642254cde0f845f9dd4f9856e52d988f4eb0d0dcffbb7a1f8ec98de7a5215fc628f3bce30fe2fb6fd2bc064b562d721658c59b544e2d34ea2766
  languageName: node
  linkType: hard

"cookie-signature@npm:1.0.6":
  version: 1.0.6
  resolution: "cookie-signature@npm:1.0.6"
  checksum: f4e1b0a98a27a0e6e66fd7ea4e4e9d8e038f624058371bf4499cfcd8f3980be9a121486995202ba3fca74fbed93a407d6d54d43a43f96fd28d0bd7a06761591a
  languageName: node
  linkType: hard

"cookie@npm:0.6.0":
  version: 0.6.0
  resolution: "cookie@npm:0.6.0"
  checksum: f56a7d32a07db5458e79c726b77e3c2eff655c36792f2b6c58d351fb5f61531e5b1ab7f46987150136e366c65213cbe31729e02a3eaed630c3bf7334635fb410
  languageName: node
  linkType: hard

"cookie@npm:0.7.1":
  version: 0.7.1
  resolution: "cookie@npm:0.7.1"
  checksum: cec5e425549b3650eb5c3498a9ba3cde0b9cd419e3b36e4b92739d30b4d89e0b678b98c1ddc209ce7cf958cd3215671fd6ac47aec21f10c2a0cc68abd399d8a7
  languageName: node
  linkType: hard

"core-js@npm:3.37.1":
  version: 3.37.1
  resolution: "core-js@npm:3.37.1"
  checksum: 2d58a5c599f05c3e04abc8bc5e64b88eb17d914c0f552f670fb800afa74ec54b4fcc7f231ad6bd45badaf62c0fb0ce30e6fe89cedb6bb6d54e6f19115c3c17ff
  languageName: node
  linkType: hard

"core-util-is@npm:1.0.2, core-util-is@npm:~1.0.0":
  version: 1.0.2
  resolution: "core-util-is@npm:1.0.2"
  checksum: 7a4c925b497a2c91421e25bf76d6d8190f0b2359a9200dbeed136e63b2931d6294d3b1893eda378883ed363cd950f44a12a401384c609839ea616befb7927dab
  languageName: node
  linkType: hard

"cors@npm:2.8.5":
  version: 2.8.5
  resolution: "cors@npm:2.8.5"
  dependencies:
    object-assign: ^4
    vary: ^1
  checksum: ced838404ccd184f61ab4fdc5847035b681c90db7ac17e428f3d81d69e2989d2b680cc254da0e2554f5ed4f8a341820a1ce3d1c16b499f6e2f47a1b9b07b5006
  languageName: node
  linkType: hard

"cose-base@npm:^1.0.0":
  version: 1.0.3
  resolution: "cose-base@npm:1.0.3"
  dependencies:
    layout-base: ^1.0.0
  checksum: 3f3d592316df74adb215ca91e430f1c22b6e890bc0025b32ae1f6464c73fdb9614816cb40a8d38b40c6a3e9e7b8c64eda90d53fb9a4a6948abec17dad496f30b
  languageName: node
  linkType: hard

"cose-base@npm:^2.2.0":
  version: 2.2.0
  resolution: "cose-base@npm:2.2.0"
  dependencies:
    layout-base: ^2.0.0
  checksum: 2e694f340bf216c71fc126d237578a4168e138720011d0b48c88bf9bfc7fd45f912eff2c603ef3d1307d6e3ce6f465ed382285a764a3a6620db590c5457d2557
  languageName: node
  linkType: hard

"crelt@npm:^1.0.5":
  version: 1.0.5
  resolution: "crelt@npm:1.0.5"
  checksum: 04a618c5878e12a14a9a328a49ff6e37bed76abb88b72e661c56b5f161d8a9aca133650da6bcbc5224ad1f7f43a69325627f209e92a21002986d52a8f844b367
  languageName: node
  linkType: hard

"cross-spawn@npm:7.0.3, cross-spawn@npm:^7.0.0, cross-spawn@npm:^7.0.3":
  version: 7.0.3
  resolution: "cross-spawn@npm:7.0.3"
  dependencies:
    path-key: ^3.1.0
    shebang-command: ^2.0.0
    which: ^2.0.1
  checksum: 671cc7c7288c3a8406f3c69a3ae2fc85555c04169e9d611def9a675635472614f1c0ed0ef80955d5b6d4e724f6ced67f0ad1bb006c2ea643488fcfef994d7f52
  languageName: node
  linkType: hard

"crypto@npm:~1.0.1":
  version: 1.0.1
  resolution: "crypto@npm:1.0.1"
  checksum: 087fe3165bd94c333a49e6ed66a0193911f63eac38a24f379b3001a5fe260a59c413646e53a0f67875ba13902b2686d81dc703cb2c147a4ec727dcdc04e5645e
  languageName: node
  linkType: hard

"css-loader@npm:^6.7.1":
  version: 6.7.3
  resolution: "css-loader@npm:6.7.3"
  dependencies:
    icss-utils: ^5.1.0
    postcss: ^8.4.19
    postcss-modules-extract-imports: ^3.0.0
    postcss-modules-local-by-default: ^4.0.0
    postcss-modules-scope: ^3.0.0
    postcss-modules-values: ^4.0.0
    postcss-value-parser: ^4.2.0
    semver: ^7.3.8
  peerDependencies:
    webpack: ^5.0.0
  checksum: 473cc32b6c837c2848e2051ad1ba331c1457449f47442e75a8c480d9891451434ada241f7e3de2347e57de17fcd84610b3bcfc4a9da41102cdaedd1e17902d31
  languageName: node
  linkType: hard

"css-select@npm:^4.1.3":
  version: 4.3.0
  resolution: "css-select@npm:4.3.0"
  dependencies:
    boolbase: ^1.0.0
    css-what: ^6.0.1
    domhandler: ^4.3.1
    domutils: ^2.8.0
    nth-check: ^2.0.1
  checksum: d6202736839194dd7f910320032e7cfc40372f025e4bf21ca5bf6eb0a33264f322f50ba9c0adc35dadd342d3d6fae5ca244779a4873afbfa76561e343f2058e0
  languageName: node
  linkType: hard

"css-what@npm:^6.0.1":
  version: 6.1.0
  resolution: "css-what@npm:6.1.0"
  checksum: b975e547e1e90b79625918f84e67db5d33d896e6de846c9b584094e529f0c63e2ab85ee33b9daffd05bff3a146a1916bec664e18bb76dd5f66cbff9fc13b2bbe
  languageName: node
  linkType: hard

"cssesc@npm:^3.0.0":
  version: 3.0.0
  resolution: "cssesc@npm:3.0.0"
  bin:
    cssesc: bin/cssesc
  checksum: f8c4ababffbc5e2ddf2fa9957dda1ee4af6048e22aeda1869d0d00843223c1b13ad3f5d88b51caa46c994225eacb636b764eb807a8883e2fb6f99b4f4e8c48b2
  languageName: node
  linkType: hard

"csstype@npm:3.0.10, csstype@npm:^3.0.10, csstype@npm:^3.0.2":
  version: 3.0.10
  resolution: "csstype@npm:3.0.10"
  checksum: 20a8fa324f2b33ddf94aa7507d1b6ab3daa6f3cc308888dc50126585d7952f2471de69b2dbe0635d1fdc31223fef8e070842691877e725caf456e2378685a631
  languageName: node
  linkType: hard

"cytoscape-cose-bilkent@npm:^4.1.0":
  version: 4.1.0
  resolution: "cytoscape-cose-bilkent@npm:4.1.0"
  dependencies:
    cose-base: ^1.0.0
  peerDependencies:
    cytoscape: ^3.2.0
  checksum: bea6aa139e21bf4135b01b99f8778eed061e074d1a1689771597e8164a999d66f4075d46be584b0a88a5447f9321f38c90c8821df6a9322faaf5afebf4848d97
  languageName: node
  linkType: hard

"cytoscape-fcose@npm:^2.2.0":
  version: 2.2.0
  resolution: "cytoscape-fcose@npm:2.2.0"
  dependencies:
    cose-base: ^2.2.0
  peerDependencies:
    cytoscape: ^3.2.0
  checksum: 94ffe6f131f9c08c2a0a7a6ce1c6c5e523a395bf8d84eba6d4a5f85e23f33788ea3ff807540861a5f78a6914a27729e06a7e6f66784f4f28ea1c030acf500121
  languageName: node
  linkType: hard

"cytoscape@npm:^3.29.3":
  version: 3.31.1
  resolution: "cytoscape@npm:3.31.1"
  checksum: 88dabf36caa2fdd01ff6f511989a436424f90f95a5a81b7062574f6dcaf5079bbb91f9b70dc0549ba6dadbea3b96b4ad7538948f2ff6ed866db8a10593597ed6
  languageName: node
  linkType: hard

"d3-array@npm:1 - 2":
  version: 2.12.1
  resolution: "d3-array@npm:2.12.1"
  dependencies:
    internmap: ^1.0.0
  checksum: 97853b7b523aded17078f37c67742f45d81e88dda2107ae9994c31b9e36c5fa5556c4c4cf39650436f247813602dfe31bf7ad067ff80f127a16903827f10c6eb
  languageName: node
  linkType: hard

"d3-array@npm:1 - 3, d3-array@npm:2 - 3, d3-array@npm:2.10.0 - 3, d3-array@npm:2.5.0 - 3, d3-array@npm:3, d3-array@npm:3.2.4, d3-array@npm:^3.2.0, d3-array@npm:^3.2.2":
  version: 3.2.4
  resolution: "d3-array@npm:3.2.4"
  dependencies:
    internmap: 1 - 2
  checksum: a5976a6d6205f69208478bb44920dd7ce3e788c9dceb86b304dbe401a4bfb42ecc8b04c20facde486e9adcb488b5d1800d49393a3f81a23902b68158e12cddd0
  languageName: node
  linkType: hard

"d3-axis@npm:3":
  version: 3.0.0
  resolution: "d3-axis@npm:3.0.0"
  checksum: 227ddaa6d4bad083539c1ec245e2228b4620cca941997a8a650cb0af239375dc20271993127eedac66f0543f331027aca09385e1e16eed023f93eac937cddf0b
  languageName: node
  linkType: hard

"d3-brush@npm:3":
  version: 3.0.0
  resolution: "d3-brush@npm:3.0.0"
  dependencies:
    d3-dispatch: 1 - 3
    d3-drag: 2 - 3
    d3-interpolate: 1 - 3
    d3-selection: 3
    d3-transition: 3
  checksum: 1d042167769a02ac76271c71e90376d7184206e489552b7022a8ec2860209fe269db55e0a3430f3dcbe13b6fec2ff65b1adeaccba3218991b38e022390df72e3
  languageName: node
  linkType: hard

"d3-chord@npm:3":
  version: 3.0.1
  resolution: "d3-chord@npm:3.0.1"
  dependencies:
    d3-path: 1 - 3
  checksum: ddf35d41675e0f8738600a8a2f05bf0858def413438c12cba357c5802ecc1014c80a658acbbee63cbad2a8c747912efb2358455d93e59906fe37469f1dc6b78b
  languageName: node
  linkType: hard

"d3-color@npm:1 - 3, d3-color@npm:3, d3-color@npm:^3.1.0":
  version: 3.1.0
  resolution: "d3-color@npm:3.1.0"
  checksum: 4931fbfda5d7c4b5cfa283a13c91a954f86e3b69d75ce588d06cde6c3628cebfc3af2069ccf225e982e8987c612aa7948b3932163ce15eb3c11cd7c003f3ee3b
  languageName: node
  linkType: hard

"d3-contour@npm:4":
  version: 4.0.2
  resolution: "d3-contour@npm:4.0.2"
  dependencies:
    d3-array: ^3.2.0
  checksum: 56aa082c1acf62a45b61c8d29fdd307041785aa17d9a07de7d1d848633769887a33fb6823888afa383f31c460d0f21d24756593e84e334ddb92d774214d32f1b
  languageName: node
  linkType: hard

"d3-delaunay@npm:6, d3-delaunay@npm:^6.0.2":
  version: 6.0.4
  resolution: "d3-delaunay@npm:6.0.4"
  dependencies:
    delaunator: 5
  checksum: ce6d267d5ef21a8aeadfe4606329fc80a22ab6e7748d47bc220bcc396ee8be84b77a5473033954c5ac4aa522d265ddc45d4165d30fe4787dd60a15ea66b9bbb4
  languageName: node
  linkType: hard

"d3-dispatch@npm:1 - 3, d3-dispatch@npm:3":
  version: 3.0.1
  resolution: "d3-dispatch@npm:3.0.1"
  checksum: fdfd4a230f46463e28e5b22a45dd76d03be9345b605e1b5dc7d18bd7ebf504e6c00ae123fd6d03e23d9e2711e01f0e14ea89cd0632545b9f0c00b924ba4be223
  languageName: node
  linkType: hard

"d3-drag@npm:2 - 3, d3-drag@npm:3":
  version: 3.0.0
  resolution: "d3-drag@npm:3.0.0"
  dependencies:
    d3-dispatch: 1 - 3
    d3-selection: 3
  checksum: d297231e60ecd633b0d076a63b4052b436ddeb48b5a3a11ff68c7e41a6774565473a6b064c5e9256e88eca6439a917ab9cea76032c52d944ddbf4fd289e31111
  languageName: node
  linkType: hard

"d3-dsv@npm:1 - 3, d3-dsv@npm:3, d3-dsv@npm:^3.0.1":
  version: 3.0.1
  resolution: "d3-dsv@npm:3.0.1"
  dependencies:
    commander: 7
    iconv-lite: 0.6
    rw: 1
  bin:
    csv2json: bin/dsv2json.js
    csv2tsv: bin/dsv2dsv.js
    dsv2dsv: bin/dsv2dsv.js
    dsv2json: bin/dsv2json.js
    json2csv: bin/json2dsv.js
    json2dsv: bin/json2dsv.js
    json2tsv: bin/json2dsv.js
    tsv2csv: bin/dsv2dsv.js
    tsv2json: bin/dsv2json.js
  checksum: 5fc0723647269d5dccd181d74f2265920ab368a2868b0b4f55ffa2fecdfb7814390ea28622cd61ee5d9594ab262879509059544e9f815c54fe76fbfb4ffa4c8a
  languageName: node
  linkType: hard

"d3-ease@npm:1 - 3, d3-ease@npm:3":
  version: 3.0.1
  resolution: "d3-ease@npm:3.0.1"
  checksum: 06e2ee5326d1e3545eab4e2c0f84046a123dcd3b612e68858219aa034da1160333d9ce3da20a1d3486d98cb5c2a06f7d233eee1bc19ce42d1533458bd85dedcd
  languageName: node
  linkType: hard

"d3-fetch@npm:3":
  version: 3.0.1
  resolution: "d3-fetch@npm:3.0.1"
  dependencies:
    d3-dsv: 1 - 3
  checksum: 382dcea06549ef82c8d0b719e5dc1d96286352579e3b51b20f71437f5800323315b09cf7dcfd4e1f60a41e1204deb01758470cea257d2285a7abd9dcec806984
  languageName: node
  linkType: hard

"d3-force@npm:3, d3-force@npm:^3.0.0":
  version: 3.0.0
  resolution: "d3-force@npm:3.0.0"
  dependencies:
    d3-dispatch: 1 - 3
    d3-quadtree: 1 - 3
    d3-timer: 1 - 3
  checksum: 6c7e96438cab62fa32aeadb0ade3297b62b51f81b1b38b0a60a5ec9fd627d74090c1189654d92df2250775f31b06812342f089f1d5947de9960a635ee3581def
  languageName: node
  linkType: hard

"d3-format@npm:1 - 3, d3-format@npm:3, d3-format@npm:^3.1.0":
  version: 3.1.0
  resolution: "d3-format@npm:3.1.0"
  checksum: f345ec3b8ad3cab19bff5dead395bd9f5590628eb97a389b1dd89f0b204c7c4fc1d9520f13231c2c7cf14b7c9a8cf10f8ef15bde2befbab41454a569bd706ca2
  languageName: node
  linkType: hard

"d3-geo-projection@npm:^4.0.0":
  version: 4.0.0
  resolution: "d3-geo-projection@npm:4.0.0"
  dependencies:
    commander: 7
    d3-array: 1 - 3
    d3-geo: 1.12.0 - 3
  bin:
    geo2svg: bin/geo2svg.js
    geograticule: bin/geograticule.js
    geoproject: bin/geoproject.js
    geoquantize: bin/geoquantize.js
    geostitch: bin/geostitch.js
  checksum: 631422b10dd78d1047ba5a3b073148bea27721060bd7087a5fa6c053ca80445d26432e505e0e3acbd6e0d76cf577c61bf9a5db70dabbc9310c493de1f7ff736d
  languageName: node
  linkType: hard

"d3-geo@npm:1.12.0 - 3, d3-geo@npm:3, d3-geo@npm:^3.1.0":
  version: 3.1.0
  resolution: "d3-geo@npm:3.1.0"
  dependencies:
    d3-array: 2.5.0 - 3
  checksum: adf82b0c105c0c5951ae0a833d4dfc479a563791ad7938579fa14e1cffd623b469d8aa7a37dc413a327fb6ac56880f3da3f6c43d4abe3c923972dd98f34f37d1
  languageName: node
  linkType: hard

"d3-hierarchy@npm:3, d3-hierarchy@npm:^3.1.2":
  version: 3.1.2
  resolution: "d3-hierarchy@npm:3.1.2"
  checksum: 0fd946a8c5fd4686d43d3e11bbfc2037a145fda29d2261ccd0e36f70b66af6d7638e2c0c7112124d63fc3d3127197a00a6aecf676bd5bd392a94d7235a214263
  languageName: node
  linkType: hard

"d3-interpolate@npm:1 - 3, d3-interpolate@npm:1.2.0 - 3, d3-interpolate@npm:3, d3-interpolate@npm:^3.0.1":
  version: 3.0.1
  resolution: "d3-interpolate@npm:3.0.1"
  dependencies:
    d3-color: 1 - 3
  checksum: a42ba314e295e95e5365eff0f604834e67e4a3b3c7102458781c477bd67e9b24b6bb9d8e41ff5521050a3f2c7c0c4bbbb6e187fd586daa3980943095b267e78b
  languageName: node
  linkType: hard

"d3-path@npm:1":
  version: 1.0.9
  resolution: "d3-path@npm:1.0.9"
  checksum: d4382573baf9509a143f40944baeff9fead136926aed6872f7ead5b3555d68925f8a37935841dd51f1d70b65a294fe35c065b0906fb6e42109295f6598fc16d0
  languageName: node
  linkType: hard

"d3-path@npm:1 - 3, d3-path@npm:3, d3-path@npm:^3.1.0":
  version: 3.1.0
  resolution: "d3-path@npm:3.1.0"
  checksum: 2306f1bd9191e1eac895ec13e3064f732a85f243d6e627d242a313f9777756838a2215ea11562f0c7630c7c3b16a19ec1fe0948b1c82f3317fac55882f6ee5d8
  languageName: node
  linkType: hard

"d3-polygon@npm:3":
  version: 3.0.1
  resolution: "d3-polygon@npm:3.0.1"
  checksum: 0b85c532517895544683849768a2c377cee3801ef8ccf3fa9693c8871dd21a0c1a2a0fc75ff54192f0ba2c562b0da2bc27f5bf959dfafc7fa23573b574865d2c
  languageName: node
  linkType: hard

"d3-quadtree@npm:1 - 3, d3-quadtree@npm:3":
  version: 3.0.1
  resolution: "d3-quadtree@npm:3.0.1"
  checksum: 5469d462763811475f34a7294d984f3eb100515b0585ca5b249656f6b1a6e99b20056a2d2e463cc9944b888896d2b1d07859c50f9c0cf23438df9cd2e3146066
  languageName: node
  linkType: hard

"d3-random@npm:3":
  version: 3.0.1
  resolution: "d3-random@npm:3.0.1"
  checksum: a70ad8d1cabe399ebeb2e482703121ac8946a3b336830b518da6848b9fdd48a111990fc041dc716f16885a72176ffa2898f2a250ca3d363ecdba5ef92b18e131
  languageName: node
  linkType: hard

"d3-sankey@npm:^0.12.3":
  version: 0.12.3
  resolution: "d3-sankey@npm:0.12.3"
  dependencies:
    d3-array: 1 - 2
    d3-shape: ^1.2.0
  checksum: df1cb9c9d02dd8fd14040e89f112f0da58c03bd7529fa001572a6925a51496d1d82ff25d9fedb6c429a91645fbd2476c19891e535ac90c8bc28337c33ee21c87
  languageName: node
  linkType: hard

"d3-scale-chromatic@npm:3, d3-scale-chromatic@npm:^3.1.0":
  version: 3.1.0
  resolution: "d3-scale-chromatic@npm:3.1.0"
  dependencies:
    d3-color: 1 - 3
    d3-interpolate: 1 - 3
  checksum: ab6324bd8e1f708e731e02ab44e09741efda2b174cea1d8ca21e4a87546295e99856bc44e2fd3890f228849c96bccfbcf922328f95be6a7df117453eb5cf22c9
  languageName: node
  linkType: hard

"d3-scale@npm:4, d3-scale@npm:^4.0.2":
  version: 4.0.2
  resolution: "d3-scale@npm:4.0.2"
  dependencies:
    d3-array: 2.10.0 - 3
    d3-format: 1 - 3
    d3-interpolate: 1.2.0 - 3
    d3-time: 2.1.1 - 3
    d3-time-format: 2 - 4
  checksum: a9c770d283162c3bd11477c3d9d485d07f8db2071665f1a4ad23eec3e515e2cefbd369059ec677c9ac849877d1a765494e90e92051d4f21111aa56791c98729e
  languageName: node
  linkType: hard

"d3-selection@npm:2 - 3, d3-selection@npm:3":
  version: 3.0.0
  resolution: "d3-selection@npm:3.0.0"
  checksum: f4e60e133309115b99f5b36a79ae0a19d71ee6e2d5e3c7216ef3e75ebd2cb1e778c2ed2fa4c01bef35e0dcbd96c5428f5bd6ca2184fe2957ed582fde6841cbc5
  languageName: node
  linkType: hard

"d3-shape@npm:3, d3-shape@npm:^3.2.0":
  version: 3.2.0
  resolution: "d3-shape@npm:3.2.0"
  dependencies:
    d3-path: ^3.1.0
  checksum: de2af5fc9a93036a7b68581ca0bfc4aca2d5a328aa7ba7064c11aedd44d24f310c20c40157cb654359d4c15c3ef369f95ee53d71221017276e34172c7b719cfa
  languageName: node
  linkType: hard

"d3-shape@npm:^1.2.0":
  version: 1.3.7
  resolution: "d3-shape@npm:1.3.7"
  dependencies:
    d3-path: 1
  checksum: 46566a3ab64a25023653bf59d64e81e9e6c987e95be985d81c5cedabae5838bd55f4a201a6b69069ca862eb63594cd263cac9034afc2b0e5664dfe286c866129
  languageName: node
  linkType: hard

"d3-time-format@npm:2 - 4, d3-time-format@npm:4, d3-time-format@npm:^4.1.0":
  version: 4.1.0
  resolution: "d3-time-format@npm:4.1.0"
  dependencies:
    d3-time: 1 - 3
  checksum: 7342bce28355378152bbd4db4e275405439cabba082d9cd01946d40581140481c8328456d91740b0fe513c51ec4a467f4471ffa390c7e0e30ea30e9ec98fcdf4
  languageName: node
  linkType: hard

"d3-time@npm:1 - 3, d3-time@npm:2.1.1 - 3, d3-time@npm:3, d3-time@npm:^3.1.0":
  version: 3.1.0
  resolution: "d3-time@npm:3.1.0"
  dependencies:
    d3-array: 2 - 3
  checksum: 613b435352a78d9f31b7f68540788186d8c331b63feca60ad21c88e9db1989fe888f97f242322ebd6365e45ec3fb206a4324cd4ca0dfffa1d9b5feb856ba00a7
  languageName: node
  linkType: hard

"d3-timer@npm:1 - 3, d3-timer@npm:3, d3-timer@npm:^3.0.1":
  version: 3.0.1
  resolution: "d3-timer@npm:3.0.1"
  checksum: 1cfddf86d7bca22f73f2c427f52dfa35c49f50d64e187eb788dcad6e927625c636aa18ae4edd44d084eb9d1f81d8ca4ec305dae7f733c15846a824575b789d73
  languageName: node
  linkType: hard

"d3-transition@npm:2 - 3, d3-transition@npm:3":
  version: 3.0.1
  resolution: "d3-transition@npm:3.0.1"
  dependencies:
    d3-color: 1 - 3
    d3-dispatch: 1 - 3
    d3-ease: 1 - 3
    d3-interpolate: 1 - 3
    d3-timer: 1 - 3
  peerDependencies:
    d3-selection: 2 - 3
  checksum: cb1e6e018c3abf0502fe9ff7b631ad058efb197b5e14b973a410d3935aead6e3c07c67d726cfab258e4936ef2667c2c3d1cd2037feb0765f0b4e1d3b8788c0ea
  languageName: node
  linkType: hard

"d3-zoom@npm:3":
  version: 3.0.0
  resolution: "d3-zoom@npm:3.0.0"
  dependencies:
    d3-dispatch: 1 - 3
    d3-drag: 2 - 3
    d3-interpolate: 1 - 3
    d3-selection: 2 - 3
    d3-transition: 2 - 3
  checksum: 8056e3527281cfd1ccbcbc458408f86973b0583e9dac00e51204026d1d36803ca437f970b5736f02fafed9f2b78f145f72a5dbc66397e02d4d95d4c594b8ff54
  languageName: node
  linkType: hard

"d3@npm:^7.9.0":
  version: 7.9.0
  resolution: "d3@npm:7.9.0"
  dependencies:
    d3-array: 3
    d3-axis: 3
    d3-brush: 3
    d3-chord: 3
    d3-color: 3
    d3-contour: 4
    d3-delaunay: 6
    d3-dispatch: 3
    d3-drag: 3
    d3-dsv: 3
    d3-ease: 3
    d3-fetch: 3
    d3-force: 3
    d3-format: 3
    d3-geo: 3
    d3-hierarchy: 3
    d3-interpolate: 3
    d3-path: 3
    d3-polygon: 3
    d3-quadtree: 3
    d3-random: 3
    d3-scale: 4
    d3-scale-chromatic: 3
    d3-selection: 3
    d3-shape: 3
    d3-time: 3
    d3-time-format: 4
    d3-timer: 3
    d3-transition: 3
    d3-zoom: 3
  checksum: 1c0e9135f1fb78aa32b187fafc8b56ae6346102bd0e4e5e5a5339611a51e6038adbaa293fae373994228100eddd87320e930b1be922baeadc07c9fd43d26d99b
  languageName: node
  linkType: hard

"dagre-d3-es@npm:7.0.11":
  version: 7.0.11
  resolution: "dagre-d3-es@npm:7.0.11"
  dependencies:
    d3: ^7.9.0
    lodash-es: ^4.17.21
  checksum: 933b0a54d3d5f64d440dba8c6433385e6879bf433d03032b2884f1af6826e0f437e2e3da61f7441e74a445d68d9710020cc12242ce79f169289a5dd7054bab21
  languageName: node
  linkType: hard

"dashdash@npm:^1.12.0":
  version: 1.14.1
  resolution: "dashdash@npm:1.14.1"
  dependencies:
    assert-plus: ^1.0.0
  checksum: 3634c249570f7f34e3d34f866c93f866c5b417f0dd616275decae08147dcdf8fccfaa5947380ccfb0473998ea3a8057c0b4cd90c875740ee685d0624b2983598
  languageName: node
  linkType: hard

"data-urls@npm:^2.0.0":
  version: 2.0.0
  resolution: "data-urls@npm:2.0.0"
  dependencies:
    abab: ^2.0.3
    whatwg-mimetype: ^2.3.0
    whatwg-url: ^8.0.0
  checksum: 97caf828aac25e25e04ba6869db0f99c75e6859bb5b424ada28d3e7841941ebf08ddff3c1b1bb4585986bd507a5d54c2a716853ea6cb98af877400e637393e71
  languageName: node
  linkType: hard

"dayjs@npm:1.11.13, dayjs@npm:^1.11.13":
  version: 1.11.13
  resolution: "dayjs@npm:1.11.13"
  checksum: f388db88a6aa93956c1f6121644e783391c7b738b73dbc54485578736565c8931bdfba4bb94e9b1535c6e509c97d5deb918bbe1ae6b34358d994de735055cca9
  languageName: node
  linkType: hard

"debug@npm:2.6.9":
  version: 2.6.9
  resolution: "debug@npm:2.6.9"
  dependencies:
    ms: 2.0.0
  checksum: d2f51589ca66df60bf36e1fa6e4386b318c3f1e06772280eea5b1ae9fd3d05e9c2b7fd8a7d862457d00853c75b00451aa2d7459b924629ee385287a650f58fe6
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.1.0, debug@npm:^4.3.3, debug@npm:^4.3.7, debug@npm:^4.4.0":
  version: 4.4.0
  resolution: "debug@npm:4.4.0"
  dependencies:
    ms: ^2.1.3
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: fb42df878dd0e22816fc56e1fdca9da73caa85212fbe40c868b1295a6878f9101ae684f4eeef516c13acfc700f5ea07f1136954f43d4cd2d477a811144136479
  languageName: node
  linkType: hard

"debug@npm:4.3.4":
  version: 4.3.4
  resolution: "debug@npm:4.3.4"
  dependencies:
    ms: 2.1.2
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 3dbad3f94ea64f34431a9cbf0bafb61853eda57bff2880036153438f50fb5a84f27683ba0d8e5426bf41a8c6ff03879488120cf5b3a761e77953169c0600a708
  languageName: node
  linkType: hard

"debug@npm:4.3.7":
  version: 4.3.7
  resolution: "debug@npm:4.3.7"
  dependencies:
    ms: ^2.1.3
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 822d74e209cd910ef0802d261b150314bbcf36c582ccdbb3e70f0894823c17e49a50d3e66d96b633524263975ca16b6a833f3e3b7e030c157169a5fabac63160
  languageName: node
  linkType: hard

"decompress-response@npm:^6.0.0":
  version: 6.0.0
  resolution: "decompress-response@npm:6.0.0"
  dependencies:
    mimic-response: ^3.1.0
  checksum: d377cf47e02d805e283866c3f50d3d21578b779731e8c5072d6ce8c13cc31493db1c2f6784da9d1d5250822120cefa44f1deab112d5981015f2e17444b763812
  languageName: node
  linkType: hard

"deep-extend@npm:^0.6.0":
  version: 0.6.0
  resolution: "deep-extend@npm:0.6.0"
  checksum: 7be7e5a8d468d6b10e6a67c3de828f55001b6eb515d014f7aeb9066ce36bd5717161eb47d6a0f7bed8a9083935b465bc163ee2581c8b128d29bf61092fdf57a7
  languageName: node
  linkType: hard

"deepmerge@npm:^4.2.2":
  version: 4.3.0
  resolution: "deepmerge@npm:4.3.0"
  checksum: c7980eb5c5be040b371f1df0d566473875cfabed9f672ccc177b81ba8eee5686ce2478de2f1d0076391621cbe729e5eacda397179a59ef0f68901849647db126
  languageName: node
  linkType: hard

"defaults@npm:^1.0.3":
  version: 1.0.4
  resolution: "defaults@npm:1.0.4"
  dependencies:
    clone: ^1.0.2
  checksum: 3a88b7a587fc076b84e60affad8b85245c01f60f38fc1d259e7ac1d89eb9ce6abb19e27215de46b98568dd5bc48471730b327637e6f20b0f1bc85cf00440c80a
  languageName: node
  linkType: hard

"defer-to-connect@npm:^2.0.0":
  version: 2.0.1
  resolution: "defer-to-connect@npm:2.0.1"
  checksum: 8a9b50d2f25446c0bfefb55a48e90afd58f85b21bcf78e9207cd7b804354f6409032a1705c2491686e202e64fc05f147aa5aa45f9aa82627563f045937f5791b
  languageName: node
  linkType: hard

"delaunator@npm:5":
  version: 5.0.0
  resolution: "delaunator@npm:5.0.0"
  dependencies:
    robust-predicates: ^3.0.0
  checksum: d6764188442b7f7c6bcacebd96edc00e35f542a96f1af3ef600e586bfb9849a3682c489c0ab423440c90bc4c7cac77f28761babff76fa29e193e1cf50a95b860
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 46fe6e83e2cb1d85ba50bd52803c68be9bd953282fa7096f51fc29edd5d67ff84ff753c51966061e5ba7cb5e47ef6d36a91924eddb7f3f3483b1c560f77a0020
  languageName: node
  linkType: hard

"delegates@npm:^1.0.0":
  version: 1.0.0
  resolution: "delegates@npm:1.0.0"
  checksum: a51744d9b53c164ba9c0492471a1a2ffa0b6727451bdc89e31627fdf4adda9d51277cfcbfb20f0a6f08ccb3c436f341df3e92631a3440226d93a8971724771fd
  languageName: node
  linkType: hard

"depd@npm:2.0.0, depd@npm:^2.0.0":
  version: 2.0.0
  resolution: "depd@npm:2.0.0"
  checksum: abbe19c768c97ee2eed6282d8ce3031126662252c58d711f646921c9623f9052e3e1906443066beec1095832f534e57c523b7333f8e7e0d93051ab6baef5ab3a
  languageName: node
  linkType: hard

"dependency-graph@npm:^0.11.0":
  version: 0.11.0
  resolution: "dependency-graph@npm:0.11.0"
  checksum: 477204beaa9be69e642bc31ffe7a8c383d0cf48fa27acbc91c5df01431ab913e65c154213d2ef83d034c98d77280743ec85e5da018a97a18dd43d3c0b78b28cd
  languageName: node
  linkType: hard

"destroy@npm:1.2.0":
  version: 1.2.0
  resolution: "destroy@npm:1.2.0"
  checksum: 0acb300b7478a08b92d810ab229d5afe0d2f4399272045ab22affa0d99dbaf12637659411530a6fcd597a9bdac718fc94373a61a95b4651bbc7b83684a565e38
  languageName: node
  linkType: hard

"detect-indent@npm:^6.0.0":
  version: 6.1.0
  resolution: "detect-indent@npm:6.1.0"
  checksum: ab953a73c72dbd4e8fc68e4ed4bfd92c97eb6c43734af3900add963fd3a9316f3bc0578b018b24198d4c31a358571eff5f0656e81a1f3b9ad5c547d58b2d093d
  languageName: node
  linkType: hard

"detect-newline@npm:3.1.0":
  version: 3.1.0
  resolution: "detect-newline@npm:3.1.0"
  checksum: ae6cd429c41ad01b164c59ea36f264a2c479598e61cba7c99da24175a7ab80ddf066420f2bec9a1c57a6bead411b4655ff15ad7d281c000a89791f48cbe939e7
  languageName: node
  linkType: hard

"diff@npm:^5.1.0":
  version: 5.1.0
  resolution: "diff@npm:5.1.0"
  checksum: c7bf0df7c9bfbe1cf8a678fd1b2137c4fb11be117a67bc18a0e03ae75105e8533dbfb1cda6b46beb3586ef5aed22143ef9d70713977d5fb1f9114e21455fba90
  languageName: node
  linkType: hard

"dir-glob@npm:^3.0.1":
  version: 3.0.1
  resolution: "dir-glob@npm:3.0.1"
  dependencies:
    path-type: ^4.0.0
  checksum: fa05e18324510d7283f55862f3161c6759a3f2f8dbce491a2fc14c8324c498286c54282c1f0e933cb930da8419b30679389499b919122952a4f8592362ef4615
  languageName: node
  linkType: hard

"dom-converter@npm:^0.2.0":
  version: 0.2.0
  resolution: "dom-converter@npm:0.2.0"
  dependencies:
    utila: ~0.4
  checksum: ea52fe303f5392e48dea563abef0e6fb3a478b8dbe3c599e99bb5d53981c6c38fc4944e56bb92a8ead6bb989d10b7914722ae11febbd2fd0910e33b9fc4aaa77
  languageName: node
  linkType: hard

"dom-serializer@npm:^1.0.1":
  version: 1.4.1
  resolution: "dom-serializer@npm:1.4.1"
  dependencies:
    domelementtype: ^2.0.1
    domhandler: ^4.2.0
    entities: ^2.0.0
  checksum: fbb0b01f87a8a2d18e6e5a388ad0f7ec4a5c05c06d219377da1abc7bb0f674d804f4a8a94e3f71ff15f6cb7dcfc75704a54b261db672b9b3ab03da6b758b0b22
  languageName: node
  linkType: hard

"dom-serializer@npm:^2.0.0":
  version: 2.0.0
  resolution: "dom-serializer@npm:2.0.0"
  dependencies:
    domelementtype: ^2.3.0
    domhandler: ^5.0.2
    entities: ^4.2.0
  checksum: cd1810544fd8cdfbd51fa2c0c1128ec3a13ba92f14e61b7650b5de421b88205fd2e3f0cc6ace82f13334114addb90ed1c2f23074a51770a8e9c1273acbc7f3e6
  languageName: node
  linkType: hard

"domelementtype@npm:^2.0.1, domelementtype@npm:^2.2.0, domelementtype@npm:^2.3.0":
  version: 2.3.0
  resolution: "domelementtype@npm:2.3.0"
  checksum: ee837a318ff702622f383409d1f5b25dd1024b692ef64d3096ff702e26339f8e345820f29a68bcdcea8cfee3531776b3382651232fbeae95612d6f0a75efb4f6
  languageName: node
  linkType: hard

"domhandler@npm:^3.0.0":
  version: 3.3.0
  resolution: "domhandler@npm:3.3.0"
  dependencies:
    domelementtype: ^2.0.1
  checksum: 850e5e9fee7834ab4314811e18bc1f4294d7eafbf6a79ad03cbe50cf964108935c97257ac248944d72a9312b4a18dfa8323e857d23278964dc83b1f124467fa3
  languageName: node
  linkType: hard

"domhandler@npm:^4.0.0, domhandler@npm:^4.2.0, domhandler@npm:^4.3.1":
  version: 4.3.1
  resolution: "domhandler@npm:4.3.1"
  dependencies:
    domelementtype: ^2.2.0
  checksum: 4c665ceed016e1911bf7d1dadc09dc888090b64dee7851cccd2fcf5442747ec39c647bb1cb8c8919f8bbdd0f0c625a6bafeeed4b2d656bbecdbae893f43ffaaa
  languageName: node
  linkType: hard

"domhandler@npm:^5.0.1, domhandler@npm:^5.0.2":
  version: 5.0.3
  resolution: "domhandler@npm:5.0.3"
  dependencies:
    domelementtype: ^2.3.0
  checksum: 0f58f4a6af63e6f3a4320aa446d28b5790a009018707bce2859dcb1d21144c7876482b5188395a188dfa974238c019e0a1e610d2fc269a12b2c192ea2b0b131c
  languageName: node
  linkType: hard

"dompurify@npm:^3.2.4":
  version: 3.2.4
  resolution: "dompurify@npm:3.2.4"
  dependencies:
    "@types/trusted-types": ^2.0.7
  dependenciesMeta:
    "@types/trusted-types":
      optional: true
  checksum: 7a299cbbfe3b3d189e5fc77ab94ad312807e37fda1e24a927548b76a58a9c98137e612ce8d94a2f6cd3d3db59844f14fca477676b5eae6103568a82142771df6
  languageName: node
  linkType: hard

"domutils@npm:^2.0.0, domutils@npm:^2.5.2, domutils@npm:^2.8.0":
  version: 2.8.0
  resolution: "domutils@npm:2.8.0"
  dependencies:
    dom-serializer: ^1.0.1
    domelementtype: ^2.2.0
    domhandler: ^4.2.0
  checksum: abf7434315283e9aadc2a24bac0e00eab07ae4313b40cc239f89d84d7315ebdfd2fb1b5bf750a96bc1b4403d7237c7b2ebf60459be394d625ead4ca89b934391
  languageName: node
  linkType: hard

"domutils@npm:^3.0.1":
  version: 3.0.1
  resolution: "domutils@npm:3.0.1"
  dependencies:
    dom-serializer: ^2.0.0
    domelementtype: ^2.3.0
    domhandler: ^5.0.1
  checksum: 23aa7a840572d395220e173cb6263b0d028596e3950100520870a125af33ff819e6f609e1606d6f7d73bd9e7feb03bb404286e57a39063b5384c62b724d987b3
  languageName: node
  linkType: hard

"dot-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "dot-case@npm:3.0.4"
  dependencies:
    no-case: ^3.0.4
    tslib: ^2.0.3
  checksum: a65e3519414856df0228b9f645332f974f2bf5433370f544a681122eab59e66038fc3349b4be1cdc47152779dac71a5864f1ccda2f745e767c46e9c6543b1169
  languageName: node
  linkType: hard

"dotenv@npm:^16.3.1":
  version: 16.4.5
  resolution: "dotenv@npm:16.4.5"
  checksum: 301a12c3d44fd49888b74eb9ccf9f07a1f5df43f489e7fcb89647a2edcd84c42d6bc349dc8df099cd18f07c35c7b04685c1a4f3e6a6a9e6b30f8d48c15b7f49c
  languageName: node
  linkType: hard

"dunder-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "dunder-proto@npm:1.0.1"
  dependencies:
    call-bind-apply-helpers: ^1.0.1
    es-errors: ^1.3.0
    gopd: ^1.2.0
  checksum: 149207e36f07bd4941921b0ca929e3a28f1da7bd6b6ff8ff7f4e2f2e460675af4576eeba359c635723dc189b64cdd4787e0255897d5b135ccc5d15cb8685fc90
  languageName: node
  linkType: hard

"duplexer@npm:^0.1.2":
  version: 0.1.2
  resolution: "duplexer@npm:0.1.2"
  checksum: 62ba61a830c56801db28ff6305c7d289b6dc9f859054e8c982abd8ee0b0a14d2e9a8e7d086ffee12e868d43e2bbe8a964be55ddbd8c8957714c87373c7a4f9b0
  languageName: node
  linkType: hard

"duplexify@npm:^3.5.0, duplexify@npm:^3.6.0":
  version: 3.7.1
  resolution: "duplexify@npm:3.7.1"
  dependencies:
    end-of-stream: ^1.0.0
    inherits: ^2.0.1
    readable-stream: ^2.0.0
    stream-shift: ^1.0.0
  checksum: 3c2ed2223d956a5da713dae12ba8295acb61d9acd966ccbba938090d04f4574ca4dca75cca089b5077c2d7e66101f32e6ea9b36a78ca213eff574e7a8b8accf2
  languageName: node
  linkType: hard

"duplexify@npm:^4.1.2":
  version: 4.1.2
  resolution: "duplexify@npm:4.1.2"
  dependencies:
    end-of-stream: ^1.4.1
    inherits: ^2.0.3
    readable-stream: ^3.1.1
    stream-shift: ^1.0.0
  checksum: 964376c61c0e92f6ed0694b3ba97c84f199413dc40ab8dfdaef80b7a7f4982fcabf796214e28ed614a5bc1ec45488a29b81e7d46fa3f5ddf65bcb118c20145ad
  languageName: node
  linkType: hard

"duplicate-package-checker-webpack-plugin@npm:^3.0.0":
  version: 3.0.0
  resolution: "duplicate-package-checker-webpack-plugin@npm:3.0.0"
  dependencies:
    chalk: ^2.3.0
    find-root: ^1.0.0
    lodash: ^4.17.4
    semver: ^5.4.1
  checksum: d77be45cb72d79a429c64d8f8f7603fea681d182fb795459a3d4afa608faad9a923378a7e80c6855f465263e1983140b6fc3682bd0213228b8cd7906ab4b934d
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 7d00d7cd8e49b9afa762a813faac332dee781932d6f2c848dc348939c4253f1d4564341b7af1d041853bc3f32c2ef141b58e0a4d9862c17a7f08f68df1e0f1ed
  languageName: node
  linkType: hard

"ecc-jsbn@npm:~0.1.1":
  version: 0.1.2
  resolution: "ecc-jsbn@npm:0.1.2"
  dependencies:
    jsbn: ~0.1.0
    safer-buffer: ^2.1.0
  checksum: 22fef4b6203e5f31d425f5b711eb389e4c6c2723402e389af394f8411b76a488fa414d309d866e2b577ce3e8462d344205545c88a8143cc21752a5172818888a
  languageName: node
  linkType: hard

"ecdsa-sig-formatter@npm:1.0.11":
  version: 1.0.11
  resolution: "ecdsa-sig-formatter@npm:1.0.11"
  dependencies:
    safe-buffer: ^5.0.1
  checksum: 207f9ab1c2669b8e65540bce29506134613dd5f122cccf1e6a560f4d63f2732d427d938f8481df175505aad94583bcb32c688737bb39a6df0625f903d6d93c03
  languageName: node
  linkType: hard

"ee-first@npm:1.1.1":
  version: 1.1.1
  resolution: "ee-first@npm:1.1.1"
  checksum: 1b4cac778d64ce3b582a7e26b218afe07e207a0f9bfe13cc7395a6d307849cfe361e65033c3251e00c27dd060cab43014c2d6b2647676135e18b77d2d05b3f4f
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.5.4":
  version: 1.5.20
  resolution: "electron-to-chromium@npm:1.5.20"
  checksum: 18c44337b32ed2ee1513f67835d1d19fcc87da13da228af56b0eef41787e2999ec9b4477a0aee615e0693a1c9bd4e60b4066fd0c9fe8dcbbeb3cc0dbd308ca09
  languageName: node
  linkType: hard

"elkjs@npm:^0.9.3":
  version: 0.9.3
  resolution: "elkjs@npm:0.9.3"
  checksum: 1293e42e0ea034b39d3719f3816b7b3cbaceb52a3114f2c1bd5ddd969bb1e36ae0afef58e77864fff7a1018dc5e96c177e9b0a40c16e4aaac26eb87f5785be4b
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: d4c5c39d5a9868b5fa152f00cada8a936868fd3367f33f71be515ecee4c803132d11b31a6222b2571b1e5f7e13890156a94880345594d0ce7e3c9895f560f192
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 8487182da74aabd810ac6d6f1994111dfc0e331b01271ae01ec1eb0ad7b5ecc2bbbbd2f053c05cb55a1ac30449527d819bbfbf0e3de1023db308cbcb47f86601
  languageName: node
  linkType: hard

"emojis-list@npm:^3.0.0":
  version: 3.0.0
  resolution: "emojis-list@npm:3.0.0"
  checksum: ddaaa02542e1e9436c03970eeed445f4ed29a5337dfba0fe0c38dfdd2af5da2429c2a0821304e8a8d1cadf27fdd5b22ff793571fa803ae16852a6975c65e8e70
  languageName: node
  linkType: hard

"encodeurl@npm:~1.0.2":
  version: 1.0.2
  resolution: "encodeurl@npm:1.0.2"
  checksum: e50e3d508cdd9c4565ba72d2012e65038e5d71bdc9198cb125beb6237b5b1ade6c0d343998da9e170fb2eae52c1bed37d4d6d98a46ea423a0cddbed5ac3f780c
  languageName: node
  linkType: hard

"encodeurl@npm:~2.0.0":
  version: 2.0.0
  resolution: "encodeurl@npm:2.0.0"
  checksum: abf5cd51b78082cf8af7be6785813c33b6df2068ce5191a40ca8b1afe6a86f9230af9a9ce694a5ce4665955e5c1120871826df9c128a642e09c58d592e2807fe
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: ^0.6.2
  checksum: bb98632f8ffa823996e508ce6a58ffcf5856330fde839ae42c9e1f436cc3b5cc651d4aeae72222916545428e54fd0f6aa8862fd8d25bdbcc4589f1e3f3715e7f
  languageName: node
  linkType: hard

"end-of-stream@npm:^1.0.0, end-of-stream@npm:^1.1.0, end-of-stream@npm:^1.4.1":
  version: 1.4.4
  resolution: "end-of-stream@npm:1.4.4"
  dependencies:
    once: ^1.4.0
  checksum: 530a5a5a1e517e962854a31693dbb5c0b2fc40b46dad2a56a2deec656ca040631124f4795823acc68238147805f8b021abbe221f4afed5ef3c8e8efc2024908b
  languageName: node
  linkType: hard

"enhanced-resolve@npm:^5.17.1":
  version: 5.17.1
  resolution: "enhanced-resolve@npm:5.17.1"
  dependencies:
    graceful-fs: ^4.2.4
    tapable: ^2.2.0
  checksum: 4bc38cf1cea96456f97503db7280394177d1bc46f8f87c267297d04f795ac5efa81e48115a2f5b6273c781027b5b6bfc5f62b54df629e4d25fa7001a86624f59
  languageName: node
  linkType: hard

"entities@npm:^2.0.0":
  version: 2.2.0
  resolution: "entities@npm:2.2.0"
  checksum: 19010dacaf0912c895ea262b4f6128574f9ccf8d4b3b65c7e8334ad0079b3706376360e28d8843ff50a78aabcb8f08f0a32dbfacdc77e47ed77ca08b713669b3
  languageName: node
  linkType: hard

"entities@npm:^4.2.0, entities@npm:^4.3.0":
  version: 4.4.0
  resolution: "entities@npm:4.4.0"
  checksum: 84d250329f4b56b40fa93ed067b194db21e8815e4eb9b59f43a086f0ecd342814f6bc483de8a77da5d64e0f626033192b1b4f1792232a7ea6b970ebe0f3187c2
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 65b5df55a8bab92229ab2b40dad3b387fad24613263d103a97f91c9fe43ceb21965cd3392b1ccb5d77088021e525c4e0481adb309625d0cb94ade1d1fb8dc17e
  languageName: node
  linkType: hard

"envinfo@npm:7.14.0, envinfo@npm:^7.7.3":
  version: 7.14.0
  resolution: "envinfo@npm:7.14.0"
  bin:
    envinfo: dist/cli.js
  checksum: 137c1dd9a4d5781c4a6cdc6b695454ba3c4ba1829f73927198aa4122f11b35b59d7b2cb7e1ceea1364925a30278897548511d22f860c14253a33797d0bebd551
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 8b7b1be20d2de12d2255c0bc2ca638b7af5171142693299416e6a9339bd7d88fc8d7707d913d78e0993176005405a236b066b45666b27b797252c771156ace54
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 0512f4e5d564021c9e3a644437b0155af2679d10d80f21adaf868e64d30efdfbd321631956f20f42d655fedb2e3a027da479fad3fa6048f768eb453a80a5f80a
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: ec1414527a0ccacd7f15f4a3bc66e215f04f595ba23ca75cdae0927af099b5ec865f9f4d33e9d7e86f512f252876ac77d4281a7871531a50678132429b1271b5
  languageName: node
  linkType: hard

"es-module-lexer@npm:^1.2.1":
  version: 1.5.4
  resolution: "es-module-lexer@npm:1.5.4"
  checksum: a0cf04fb92d052647ac7d818d1913b98d3d3d0f5b9d88f0eafb993436e4c3e2c958599db68839d57f2dfa281fdf0f60e18d448eb78fc292c33c0f25635b6854f
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0":
  version: 1.1.1
  resolution: "es-object-atoms@npm:1.1.1"
  dependencies:
    es-errors: ^1.3.0
  checksum: 214d3767287b12f36d3d7267ef342bbbe1e89f899cfd67040309fc65032372a8e60201410a99a1645f2f90c1912c8c49c8668066f6bdd954bcd614dda2e3da97
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.1.0":
  version: 2.1.0
  resolution: "es-set-tostringtag@npm:2.1.0"
  dependencies:
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.6
    has-tostringtag: ^1.0.2
    hasown: ^2.0.2
  checksum: 789f35de4be3dc8d11fdcb91bc26af4ae3e6d602caa93299a8c45cf05d36cc5081454ae2a6d3afa09cceca214b76c046e4f8151e092e6fc7feeb5efb9e794fc6
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1, escalade@npm:^3.1.2":
  version: 3.2.0
  resolution: "escalade@npm:3.2.0"
  checksum: 47b029c83de01b0d17ad99ed766347b974b0d628e848de404018f3abee728e987da0d2d370ad4574aa3d5b5bfc368754fd085d69a30f8e75903486ec4b5b709e
  languageName: node
  linkType: hard

"escape-html@npm:~1.0.3":
  version: 1.0.3
  resolution: "escape-html@npm:1.0.3"
  checksum: 6213ca9ae00d0ab8bccb6d8d4e0a98e76237b2410302cf7df70aaa6591d509a2a37ce8998008cbecae8fc8ffaadf3fb0229535e6a145f3ce0b211d060decbb24
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^1.0.5":
  version: 1.0.5
  resolution: "escape-string-regexp@npm:1.0.5"
  checksum: 6092fda75c63b110c706b6a9bfde8a612ad595b628f0bd2147eea1d3406723020810e591effc7db1da91d80a71a737a313567c5abb3813e8d9c71f4aa595b410
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 98b48897d93060f2322108bf29db0feba7dd774be96cd069458d1453347b25ce8682ecc39859d4bca2203cc0ab19c237bcc71755eff49a0f8d90beadeeba5cc5
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^5.0.0":
  version: 5.0.0
  resolution: "escape-string-regexp@npm:5.0.0"
  checksum: 20daabe197f3cb198ec28546deebcf24b3dbb1a5a269184381b3116d12f0532e06007f4bc8da25669d6a7f8efb68db0758df4cd981f57bc5b57f521a3e12c59e
  languageName: node
  linkType: hard

"eslint-scope@npm:5.1.1":
  version: 5.1.1
  resolution: "eslint-scope@npm:5.1.1"
  dependencies:
    esrecurse: ^4.3.0
    estraverse: ^4.1.1
  checksum: 47e4b6a3f0cc29c7feedee6c67b225a2da7e155802c6ea13bbef4ac6b9e10c66cd2dcb987867ef176292bf4e64eccc680a49e35e9e9c669f4a02bac17e86abdb
  languageName: node
  linkType: hard

"esm@npm:^3.2.25":
  version: 3.2.25
  resolution: "esm@npm:3.2.25"
  checksum: 978aabe2de83541c105605a6d60a26ed8e627ef6bb0a7605fe15a95bbdea6b8348bd045255cb22219c054dd09a81a94823df00843d9e97f42419c92015ce3a64
  languageName: node
  linkType: hard

"esprima@npm:^4.0.0":
  version: 4.0.1
  resolution: "esprima@npm:4.0.1"
  bin:
    esparse: ./bin/esparse.js
    esvalidate: ./bin/esvalidate.js
  checksum: b45bc805a613dbea2835278c306b91aff6173c8d034223fa81498c77dcbce3b2931bf6006db816f62eacd9fd4ea975dfd85a5b7f3c6402cfd050d4ca3c13a628
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: ^5.2.0
  checksum: ebc17b1a33c51cef46fdc28b958994b1dc43cd2e86237515cbc3b4e5d2be6a811b2315d0a1a4d9d340b6d2308b15322f5c8291059521cc5f4802f65e7ec32837
  languageName: node
  linkType: hard

"estraverse@npm:^4.1.1":
  version: 4.3.0
  resolution: "estraverse@npm:4.3.0"
  checksum: a6299491f9940bb246124a8d44b7b7a413a8336f5436f9837aaa9330209bd9ee8af7e91a654a3545aee9c54b3308e78ee360cef1d777d37cfef77d2fa33b5827
  languageName: node
  linkType: hard

"estraverse@npm:^5.2.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 072780882dc8416ad144f8fe199628d2b3e7bbc9989d9ed43795d2c90309a2047e6bc5979d7e2322a341163d22cfad9e21f4110597fe487519697389497e4e2b
  languageName: node
  linkType: hard

"etag@npm:~1.8.1":
  version: 1.8.1
  resolution: "etag@npm:1.8.1"
  checksum: 571aeb3dbe0f2bbd4e4fadbdb44f325fc75335cd5f6f6b6a091e6a06a9f25ed5392f0863c5442acb0646787446e816f13cbfc6edce5b07658541dff573cab1ff
  languageName: node
  linkType: hard

"event-target-shim@npm:^5.0.0":
  version: 5.0.1
  resolution: "event-target-shim@npm:5.0.1"
  checksum: 1ffe3bb22a6d51bdeb6bf6f7cf97d2ff4a74b017ad12284cc9e6a279e727dc30a5de6bb613e5596ff4dc3e517841339ad09a7eec44266eccb1aa201a30448166
  languageName: node
  linkType: hard

"events@npm:^3.2.0, events@npm:^3.3.0":
  version: 3.3.0
  resolution: "events@npm:3.3.0"
  checksum: f6f487ad2198aa41d878fa31452f1a3c00958f46e9019286ff4787c84aac329332ab45c9cdc8c445928fc6d7ded294b9e005a7fce9426488518017831b272780
  languageName: node
  linkType: hard

"exenv-es6@npm:^1.1.1":
  version: 1.1.1
  resolution: "exenv-es6@npm:1.1.1"
  checksum: 7f2aa12025e6f06c48dc286f380cf3183bb19c6017b36d91695034a3e5124a7235c4f8ff24ca2eb88ae801322f0f99605cedfcfd996a5fcbba7669320e2a448e
  languageName: node
  linkType: hard

"express-rate-limit@npm:5.5.1":
  version: 5.5.1
  resolution: "express-rate-limit@npm:5.5.1"
  checksum: 264820bd5fe350794f90497c5bdc7b323eec4394873cd4b9f9d3654b2c47b285e87270a5a11721fb7fb895d56218e9657ea7bb9a544dd43770c6e7beaad217e8
  languageName: node
  linkType: hard

"express@npm:4.21.0":
  version: 4.21.0
  resolution: "express@npm:4.21.0"
  dependencies:
    accepts: ~1.3.8
    array-flatten: 1.1.1
    body-parser: 1.20.3
    content-disposition: 0.5.4
    content-type: ~1.0.4
    cookie: 0.6.0
    cookie-signature: 1.0.6
    debug: 2.6.9
    depd: 2.0.0
    encodeurl: ~2.0.0
    escape-html: ~1.0.3
    etag: ~1.8.1
    finalhandler: 1.3.1
    fresh: 0.5.2
    http-errors: 2.0.0
    merge-descriptors: 1.0.3
    methods: ~1.1.2
    on-finished: 2.4.1
    parseurl: ~1.3.3
    path-to-regexp: 0.1.10
    proxy-addr: ~2.0.7
    qs: 6.13.0
    range-parser: ~1.2.1
    safe-buffer: 5.2.1
    send: 0.19.0
    serve-static: 1.16.2
    setprototypeof: 1.2.0
    statuses: 2.0.1
    type-is: ~1.6.18
    utils-merge: 1.0.1
    vary: ~1.1.2
  checksum: 1c5212993f665809c249bf00ab550b989d1365a5b9171cdfaa26d93ee2ef10cd8add520861ec8d5da74b3194d8374e1d9d53e85ef69b89fd9c4196b87045a5d4
  languageName: node
  linkType: hard

"express@npm:4.21.1":
  version: 4.21.1
  resolution: "express@npm:4.21.1"
  dependencies:
    accepts: ~1.3.8
    array-flatten: 1.1.1
    body-parser: 1.20.3
    content-disposition: 0.5.4
    content-type: ~1.0.4
    cookie: 0.7.1
    cookie-signature: 1.0.6
    debug: 2.6.9
    depd: 2.0.0
    encodeurl: ~2.0.0
    escape-html: ~1.0.3
    etag: ~1.8.1
    finalhandler: 1.3.1
    fresh: 0.5.2
    http-errors: 2.0.0
    merge-descriptors: 1.0.3
    methods: ~1.1.2
    on-finished: 2.4.1
    parseurl: ~1.3.3
    path-to-regexp: 0.1.10
    proxy-addr: ~2.0.7
    qs: 6.13.0
    range-parser: ~1.2.1
    safe-buffer: 5.2.1
    send: 0.19.0
    serve-static: 1.16.2
    setprototypeof: 1.2.0
    statuses: 2.0.1
    type-is: ~1.6.18
    utils-merge: 1.0.1
    vary: ~1.1.2
  checksum: 5ac2b26d8aeddda5564fc0907227d29c100f90c0ead2ead9d474dc5108e8fb306c2de2083c4e3ba326e0906466f2b73417dbac16961f4075ff9f03785fd940fe
  languageName: node
  linkType: hard

"exsolve@npm:^1.0.1":
  version: 1.0.4
  resolution: "exsolve@npm:1.0.4"
  checksum: 57eabd784003df951bc43db92ef1b8c4d329a57c8649df2fa863c2bc22a40dc8e8231338a08d1bc382218b261457d553c622fbc6bf01133a391169ec29b98446
  languageName: node
  linkType: hard

"extend@npm:~3.0.2":
  version: 3.0.2
  resolution: "extend@npm:3.0.2"
  checksum: a50a8309ca65ea5d426382ff09f33586527882cf532931cb08ca786ea3146c0553310bda688710ff61d7668eba9f96b923fe1420cdf56a2c3eaf30fcab87b515
  languageName: node
  linkType: hard

"external-editor@npm:^3.0.3":
  version: 3.1.0
  resolution: "external-editor@npm:3.1.0"
  dependencies:
    chardet: ^0.7.0
    iconv-lite: ^0.4.24
    tmp: ^0.0.33
  checksum: 1c2a616a73f1b3435ce04030261bed0e22d4737e14b090bb48e58865da92529c9f2b05b893de650738d55e692d071819b45e1669259b2b354bc3154d27a698c7
  languageName: node
  linkType: hard

"extsprintf@npm:1.3.0, extsprintf@npm:^1.2.0":
  version: 1.3.0
  resolution: "extsprintf@npm:1.3.0"
  checksum: cee7a4a1e34cffeeec18559109de92c27517e5641991ec6bab849aa64e3081022903dd53084f2080d0d2530803aa5ee84f1e9de642c365452f9e67be8f958ce2
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3, fast-deep-equal@npm:~3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: e21a9d8d84f53493b6aa15efc9cfd53dd5b714a1f23f67fb5dc8f574af80df889b3bce25dc081887c6d25457cce704e636395333abad896ccdec03abaf1f3f9d
  languageName: node
  linkType: hard

"fast-fifo@npm:^1.2.0, fast-fifo@npm:^1.3.2":
  version: 1.3.2
  resolution: "fast-fifo@npm:1.3.2"
  checksum: 6bfcba3e4df5af7be3332703b69a7898a8ed7020837ec4395bb341bd96cc3a6d86c3f6071dd98da289618cf2234c70d84b2a6f09a33dd6f988b1ff60d8e54275
  languageName: node
  linkType: hard

"fast-glob@npm:^3.0.3, fast-glob@npm:^3.2.2":
  version: 3.2.7
  resolution: "fast-glob@npm:3.2.7"
  dependencies:
    "@nodelib/fs.stat": ^2.0.2
    "@nodelib/fs.walk": ^1.2.3
    glob-parent: ^5.1.2
    merge2: ^1.3.0
    micromatch: ^4.0.4
  checksum: 2f4708ff112d2b451888129fdd9a0938db88b105b0ddfd043c064e3c4d3e20eed8d7c7615f7565fee660db34ddcf08a2db1bf0ab3c00b87608e4719694642d78
  languageName: node
  linkType: hard

"fast-json-patch@npm:^3.1.1":
  version: 3.1.1
  resolution: "fast-json-patch@npm:3.1.1"
  checksum: c4525b61b2471df60d4b025b4118b036d99778a93431aa44d1084218182841d82ce93056f0f3bbd731a24e6a8e69820128adf1873eb2199a26c62ef58d137833
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0, fast-json-stable-stringify@npm:~2.1.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: b191531e36c607977e5b1c47811158733c34ccb3bfde92c44798929e9b4154884378536d26ad90dfecd32e1ffc09c545d23535ad91b3161a27ddbb8ebe0cbecb
  languageName: node
  linkType: hard

"fast-redact@npm:^3.0.0, fast-redact@npm:^3.1.1":
  version: 3.5.0
  resolution: "fast-redact@npm:3.5.0"
  checksum: ef03f0d1849da074a520a531ad299bf346417b790a643931ab4e01cb72275c8d55b60dc8512fb1f1818647b696790edefaa96704228db9f012da935faa1940af
  languageName: node
  linkType: hard

"fast-safe-stringify@npm:2.1.1":
  version: 2.1.1
  resolution: "fast-safe-stringify@npm:2.1.1"
  checksum: a851cbddc451745662f8f00ddb622d6766f9bd97642dabfd9a405fb0d646d69fc0b9a1243cbf67f5f18a39f40f6fa821737651ff1bceeba06c9992ca2dc5bd3d
  languageName: node
  linkType: hard

"fast-uri@npm:^3.0.1":
  version: 3.0.6
  resolution: "fast-uri@npm:3.0.6"
  checksum: 7161ba2a7944778d679ba8e5f00d6a2bb479a2142df0982f541d67be6c979b17808f7edbb0ce78161c85035974bde3fa52b5137df31da46c0828cb629ba67c4e
  languageName: node
  linkType: hard

"fastest-levenshtein@npm:^1.0.12":
  version: 1.0.16
  resolution: "fastest-levenshtein@npm:1.0.16"
  checksum: a78d44285c9e2ae2c25f3ef0f8a73f332c1247b7ea7fb4a191e6bb51aa6ee1ef0dfb3ed113616dcdc7023e18e35a8db41f61c8d88988e877cf510df8edafbc71
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.15.0
  resolution: "fastq@npm:1.15.0"
  dependencies:
    reusify: ^1.0.4
  checksum: 0170e6bfcd5d57a70412440b8ef600da6de3b2a6c5966aeaf0a852d542daff506a0ee92d6de7679d1de82e644bce69d7a574a6c93f0b03964b5337eed75ada1a
  languageName: node
  linkType: hard

"figures@npm:^5.0.0":
  version: 5.0.0
  resolution: "figures@npm:5.0.0"
  dependencies:
    escape-string-regexp: ^5.0.0
    is-unicode-supported: ^1.2.0
  checksum: e6e8b6d1df2f554d4effae4a5ceff5d796f9449f6d4e912d74dab7d5f25916ecda6c305b9084833157d56485a0c78b37164430ddc5675bcee1330e346710669e
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: ^5.0.1
  checksum: b4abfbca3839a3d55e4ae5ec62e131e2e356bf4859ce8480c64c4876100f4df292a63e5bb1618e1d7460282ca2b305653064f01654474aa35c68000980f17798
  languageName: node
  linkType: hard

"finalhandler@npm:1.3.1":
  version: 1.3.1
  resolution: "finalhandler@npm:1.3.1"
  dependencies:
    debug: 2.6.9
    encodeurl: ~2.0.0
    escape-html: ~1.0.3
    on-finished: 2.4.1
    parseurl: ~1.3.3
    statuses: 2.0.1
    unpipe: ~1.0.0
  checksum: a8c58cd97c9cd47679a870f6833a7b417043f5a288cd6af6d0f49b476c874a506100303a128b6d3b654c3d74fa4ff2ffed68a48a27e8630cda5c918f2977dcf4
  languageName: node
  linkType: hard

"find-root@npm:^1.0.0":
  version: 1.1.0
  resolution: "find-root@npm:1.1.0"
  checksum: b2a59fe4b6c932eef36c45a048ae8f93c85640212ebe8363164814990ee20f154197505965f3f4f102efc33bfb1cbc26fd17c4a2fc739ebc51b886b137cbefaf
  languageName: node
  linkType: hard

"find-up@npm:^4.0.0":
  version: 4.1.0
  resolution: "find-up@npm:4.1.0"
  dependencies:
    locate-path: ^5.0.0
    path-exists: ^4.0.0
  checksum: 4c172680e8f8c1f78839486e14a43ef82e9decd0e74145f40707cc42e7420506d5ec92d9a11c22bd2c48fb0c384ea05dd30e10dd152fefeec6f2f75282a8b844
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.1.1
  resolution: "foreground-child@npm:3.1.1"
  dependencies:
    cross-spawn: ^7.0.0
    signal-exit: ^4.0.1
  checksum: 139d270bc82dc9e6f8bc045fe2aae4001dc2472157044fdfad376d0a3457f77857fa883c1c8b21b491c6caade9a926a4bed3d3d2e8d3c9202b151a4cbbd0bcd5
  languageName: node
  linkType: hard

"forever-agent@npm:~0.6.1":
  version: 0.6.1
  resolution: "forever-agent@npm:0.6.1"
  checksum: 766ae6e220f5fe23676bb4c6a99387cec5b7b62ceb99e10923376e27bfea72f3c3aeec2ba5f45f3f7ba65d6616965aa7c20b15002b6860833bb6e394dea546a8
  languageName: node
  linkType: hard

"form-data@npm:~4.0.0":
  version: 4.0.2
  resolution: "form-data@npm:4.0.2"
  dependencies:
    asynckit: ^0.4.0
    combined-stream: ^1.0.8
    es-set-tostringtag: ^2.1.0
    mime-types: ^2.1.12
  checksum: e887298b22c13c7c9c5a8ba3716f295a479a13ca78bfd855ef11cbce1bcf22bc0ae2062e94808e21d46e5c667664a1a1a8a7f57d7040193c1fefbfb11af58aab
  languageName: node
  linkType: hard

"forwarded@npm:0.2.0":
  version: 0.2.0
  resolution: "forwarded@npm:0.2.0"
  checksum: fd27e2394d8887ebd16a66ffc889dc983fbbd797d5d3f01087c020283c0f019a7d05ee85669383d8e0d216b116d720fc0cef2f6e9b7eb9f4c90c6e0bc7fd28e6
  languageName: node
  linkType: hard

"free-style@npm:3.1.0":
  version: 3.1.0
  resolution: "free-style@npm:3.1.0"
  checksum: 949258ae315deda48cac93ecd5f9a80f36e8a027e19ce2103598dc8d5ab60e963bbad5444b2a4990ddb746798dd188896f430285cf484afbf2141f7d75a191d8
  languageName: node
  linkType: hard

"fresh@npm:0.5.2":
  version: 0.5.2
  resolution: "fresh@npm:0.5.2"
  checksum: 13ea8b08f91e669a64e3ba3a20eb79d7ca5379a81f1ff7f4310d54e2320645503cc0c78daedc93dfb6191287295f6479544a649c64d8e41a1c0fb0c221552346
  languageName: node
  linkType: hard

"fs-extra@npm:^10.1.0":
  version: 10.1.0
  resolution: "fs-extra@npm:10.1.0"
  dependencies:
    graceful-fs: ^4.2.0
    jsonfile: ^6.0.1
    universalify: ^2.0.0
  checksum: dc94ab37096f813cc3ca12f0f1b5ad6744dfed9ed21e953d72530d103cea193c2f81584a39e9dee1bea36de5ee66805678c0dddc048e8af1427ac19c00fffc50
  languageName: node
  linkType: hard

"fs-minipass@npm:^2.0.0, fs-minipass@npm:^2.1.0":
  version: 2.1.0
  resolution: "fs-minipass@npm:2.1.0"
  dependencies:
    minipass: ^3.0.0
  checksum: 1b8d128dae2ac6cc94230cc5ead341ba3e0efaef82dab46a33d171c044caaa6ca001364178d42069b2809c35a1c3c35079a32107c770e9ffab3901b59af8c8b1
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 99ddea01a7e75aa276c250a04eedeffe5662bce66c65c07164ad6264f9de18fb21be9433ead460e54cff20e31721c811f4fb5d70591799df5f85dce6d6746fd0
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.2":
  version: 2.3.2
  resolution: "fsevents@npm:2.3.2"
  dependencies:
    node-gyp: latest
  checksum: 97ade64e75091afee5265e6956cb72ba34db7819b4c3e94c431d4be2b19b8bb7a2d4116da417950c3425f17c8fe693d25e20212cac583ac1521ad066b77ae31f
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@~2.3.2#~builtin<compat/fsevents>":
  version: 2.3.2
  resolution: "fsevents@patch:fsevents@npm%3A2.3.2#~builtin<compat/fsevents>::version=2.3.2&hash=df0bf1"
  dependencies:
    node-gyp: latest
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.1, function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 2b0ff4ce708d99715ad14a6d1f894e2a83242e4a52ccfcefaee5e40050562e5f6dafc1adbb4ce2d4ab47279a45dc736ab91ea5042d843c3c092820dfe032efb1
  languageName: node
  linkType: hard

"gauge@npm:^4.0.3":
  version: 4.0.4
  resolution: "gauge@npm:4.0.4"
  dependencies:
    aproba: ^1.0.3 || ^2.0.0
    color-support: ^1.1.3
    console-control-strings: ^1.1.0
    has-unicode: ^2.0.1
    signal-exit: ^3.0.7
    string-width: ^4.2.3
    strip-ansi: ^6.0.1
    wide-align: ^1.1.5
  checksum: 788b6bfe52f1dd8e263cda800c26ac0ca2ff6de0b6eee2fe0d9e3abf15e149b651bd27bf5226be10e6e3edb5c4e5d5985a5a1a98137e7a892f75eff76467ad2d
  languageName: node
  linkType: hard

"get-caller-file@npm:^2.0.5":
  version: 2.0.5
  resolution: "get-caller-file@npm:2.0.5"
  checksum: b9769a836d2a98c3ee734a88ba712e62703f1df31b94b784762c433c27a386dd6029ff55c2a920c392e33657d80191edbf18c61487e198844844516f843496b9
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.2.5, get-intrinsic@npm:^1.2.6":
  version: 1.2.7
  resolution: "get-intrinsic@npm:1.2.7"
  dependencies:
    call-bind-apply-helpers: ^1.0.1
    es-define-property: ^1.0.1
    es-errors: ^1.3.0
    es-object-atoms: ^1.0.0
    function-bind: ^1.1.2
    get-proto: ^1.0.0
    gopd: ^1.2.0
    has-symbols: ^1.1.0
    hasown: ^2.0.2
    math-intrinsics: ^1.1.0
  checksum: a1597b3b432074f805b6a0ba1182130dd6517c0ea0c4eecc4b8834c803913e1ea62dfc412865be795b3dacb1555a21775b70cf9af7a18b1454ff3414e5442d4a
  languageName: node
  linkType: hard

"get-proto@npm:^1.0.0":
  version: 1.0.1
  resolution: "get-proto@npm:1.0.1"
  dependencies:
    dunder-proto: ^1.0.1
    es-object-atoms: ^1.0.0
  checksum: 4fc96afdb58ced9a67558698b91433e6b037aaa6f1493af77498d7c85b141382cf223c0e5946f334fb328ee85dfe6edd06d218eaf09556f4bc4ec6005d7f5f7b
  languageName: node
  linkType: hard

"get-stream@npm:^5.1.0":
  version: 5.2.0
  resolution: "get-stream@npm:5.2.0"
  dependencies:
    pump: ^3.0.0
  checksum: 8bc1a23174a06b2b4ce600df38d6c98d2ef6d84e020c1ddad632ad75bac4e092eeb40e4c09e0761c35fc2dbc5e7fff5dab5e763a383582c4a167dd69a905bd12
  languageName: node
  linkType: hard

"getpass@npm:^0.1.1":
  version: 0.1.7
  resolution: "getpass@npm:0.1.7"
  dependencies:
    assert-plus: ^1.0.0
  checksum: ab18d55661db264e3eac6012c2d3daeafaab7a501c035ae0ccb193c3c23e9849c6e29b6ac762b9c2adae460266f925d55a3a2a3a3c8b94be2f222df94d70c046
  languageName: node
  linkType: hard

"git-hooks-list@npm:1.0.3":
  version: 1.0.3
  resolution: "git-hooks-list@npm:1.0.3"
  checksum: a1dd03d39c1d727ba08a35dbdbdcc6e96de8c4170c942dc95bf787ca6e34998d39fb5295a00242b58a3d265de0b69a0686d0cf583baa6b7830f268542c4576b9
  languageName: node
  linkType: hard

"github-slugger@npm:^2.0.0":
  version: 2.0.0
  resolution: "github-slugger@npm:2.0.0"
  checksum: 250375cde2058f21454872c2c79f72c4637340c30c51ff158ca4ec71cbc478f33d54477d787a662f9207aeb095a2060f155bc01f15329ba8a5fb6698e0fc81f8
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2, glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: ^4.0.1
  checksum: f4f2bfe2425296e8a47e36864e4f42be38a996db40420fe434565e4480e3322f18eb37589617a98640c5dc8fdec1a387007ee18dbb1f3f5553409c34d17f425e
  languageName: node
  linkType: hard

"glob-to-regexp@npm:^0.4.1":
  version: 0.4.1
  resolution: "glob-to-regexp@npm:0.4.1"
  checksum: e795f4e8f06d2a15e86f76e4d92751cf8bbfcf0157cea5c2f0f35678a8195a750b34096b1256e436f0cebc1883b5ff0888c47348443e69546a5a87f9e1eb1167
  languageName: node
  linkType: hard

"glob@npm:^10.3.7":
  version: 10.3.10
  resolution: "glob@npm:10.3.10"
  dependencies:
    foreground-child: ^3.1.0
    jackspeak: ^2.3.5
    minimatch: ^9.0.1
    minipass: ^5.0.0 || ^6.0.2 || ^7.0.0
    path-scurry: ^1.10.1
  bin:
    glob: dist/esm/bin.mjs
  checksum: 4f2fe2511e157b5a3f525a54092169a5f92405f24d2aed3142f4411df328baca13059f4182f1db1bf933e2c69c0bd89e57ae87edd8950cba8c7ccbe84f721cf3
  languageName: node
  linkType: hard

"glob@npm:^6.0.1":
  version: 6.0.4
  resolution: "glob@npm:6.0.4"
  dependencies:
    inflight: ^1.0.4
    inherits: 2
    minimatch: 2 || 3
    once: ^1.3.0
    path-is-absolute: ^1.0.0
  checksum: c4946c3d015ac81f704d185f2b3a55eb670100693c2cf7bc833d0efd970ec727d860d4839a5178e46a7e594b34a34661bae2f4c3405727c9fd189f84954ca3c0
  languageName: node
  linkType: hard

"glob@npm:^7.1.3, glob@npm:^7.1.4, glob@npm:~7.1.6":
  version: 7.1.7
  resolution: "glob@npm:7.1.7"
  dependencies:
    fs.realpath: ^1.0.0
    inflight: ^1.0.4
    inherits: 2
    minimatch: ^3.0.4
    once: ^1.3.0
    path-is-absolute: ^1.0.0
  checksum: b61f48973bbdcf5159997b0874a2165db572b368b931135832599875919c237fc05c12984e38fe828e69aa8a921eb0e8a4997266211c517c9cfaae8a93988bb8
  languageName: node
  linkType: hard

"glob@npm:^8.0.1":
  version: 8.1.0
  resolution: "glob@npm:8.1.0"
  dependencies:
    fs.realpath: ^1.0.0
    inflight: ^1.0.4
    inherits: 2
    minimatch: ^5.0.1
    once: ^1.3.0
  checksum: 92fbea3221a7d12075f26f0227abac435de868dd0736a17170663783296d0dd8d3d532a5672b4488a439bf5d7fb85cdd07c11185d6cd39184f0385cbdfb86a47
  languageName: node
  linkType: hard

"globals@npm:^15.14.0":
  version: 15.15.0
  resolution: "globals@npm:15.15.0"
  checksum: a2a92199a112db00562a2f85eeef2a7e3943e171f7f7d9b17dfa9231e35fd612588f3c199d1509ab1757273467e413b08c80424cf6e399e96acdaf93deb3ee88
  languageName: node
  linkType: hard

"globby@npm:10.0.0":
  version: 10.0.0
  resolution: "globby@npm:10.0.0"
  dependencies:
    "@types/glob": ^7.1.1
    array-union: ^2.1.0
    dir-glob: ^3.0.1
    fast-glob: ^3.0.3
    glob: ^7.1.3
    ignore: ^5.1.1
    merge2: ^1.2.3
    slash: ^3.0.0
  checksum: fbff58d2fcaedd9207901f6e3b5341ff885b6d499c3a095f7befde0fd03ec1ea634452a82f81e894e46f6a5d704da44b842ba93066f90dced52adf84d4b8d1cc
  languageName: node
  linkType: hard

"gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: cc6d8e655e360955bdccaca51a12a474268f95bb793fc3e1f2bdadb075f28bfd1fd988dab872daf77a61d78cbaf13744bc8727a17cfb1d150d76047d805375f3
  languageName: node
  linkType: hard

"got@npm:^11.7.0, got@npm:^11.8.2":
  version: 11.8.6
  resolution: "got@npm:11.8.6"
  dependencies:
    "@sindresorhus/is": ^4.0.0
    "@szmarczak/http-timer": ^4.0.5
    "@types/cacheable-request": ^6.0.1
    "@types/responselike": ^1.0.0
    cacheable-lookup: ^5.0.3
    cacheable-request: ^7.0.2
    decompress-response: ^6.0.0
    http2-wrapper: ^1.0.0-beta.5.2
    lowercase-keys: ^2.0.0
    p-cancelable: ^2.0.0
    responselike: ^2.0.0
  checksum: bbc783578a8d5030c8164ef7f57ce41b5ad7db2ed13371e1944bef157eeca5a7475530e07c0aaa71610d7085474d0d96222c9f4268d41db333a17e39b463f45d
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.2, graceful-fs@npm:^4.1.3, graceful-fs@npm:^4.1.6, graceful-fs@npm:^4.2.0, graceful-fs@npm:^4.2.11, graceful-fs@npm:^4.2.4, graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: ac85f94da92d8eb6b7f5a8b20ce65e43d66761c55ce85ac96df6865308390da45a8d3f0296dd3a663de65d30ba497bd46c696cc1e248c72b13d6d567138a4fc7
  languageName: node
  linkType: hard

"grapheme-splitter@npm:^1.0.4":
  version: 1.0.4
  resolution: "grapheme-splitter@npm:1.0.4"
  checksum: 0c22ec54dee1b05cd480f78cf14f732cb5b108edc073572c4ec205df4cd63f30f8db8025afc5debc8835a8ddeacf648a1c7992fe3dcd6ad38f9a476d84906620
  languageName: node
  linkType: hard

"gunzip-maybe@npm:^1.4.2":
  version: 1.4.2
  resolution: "gunzip-maybe@npm:1.4.2"
  dependencies:
    browserify-zlib: ^0.1.4
    is-deflate: ^1.0.0
    is-gzip: ^1.0.0
    peek-stream: ^1.1.0
    pumpify: ^1.3.3
    through2: ^2.0.3
  bin:
    gunzip-maybe: bin.js
  checksum: bc4d4977c24a2860238df271de75d53dd72a359d19f1248d1c613807dc221d3b8ae09624e3085c8106663e3e1b59db62a85b261d1138c2cc24efad9df577d4e1
  languageName: node
  linkType: hard

"gzip-size@npm:^6.0.0":
  version: 6.0.0
  resolution: "gzip-size@npm:6.0.0"
  dependencies:
    duplexer: ^0.1.2
  checksum: 2df97f359696ad154fc171dcb55bc883fe6e833bca7a65e457b9358f3cb6312405ed70a8da24a77c1baac0639906cd52358dc0ce2ec1a937eaa631b934c94194
  languageName: node
  linkType: hard

"hachure-fill@npm:^0.5.2":
  version: 0.5.2
  resolution: "hachure-fill@npm:0.5.2"
  checksum: 01cf2ac6b787ec73ced3d6eb393a0f989d55f32431d1e8a1c1c864769d1b8763c9cb6aa1d45fb1c237a065de90167491c6a46193690b688ea6c25f575f84586c
  languageName: node
  linkType: hard

"handlebars@npm:4.7.8, handlebars@npm:^4.7.8":
  version: 4.7.8
  resolution: "handlebars@npm:4.7.8"
  dependencies:
    minimist: ^1.2.5
    neo-async: ^2.6.2
    source-map: ^0.6.1
    uglify-js: ^3.1.4
    wordwrap: ^1.0.0
  dependenciesMeta:
    uglify-js:
      optional: true
  bin:
    handlebars: bin/handlebars
  checksum: 00e68bb5c183fd7b8b63322e6234b5ac8fbb960d712cb3f25587d559c2951d9642df83c04a1172c918c41bcfc81bfbd7a7718bbce93b893e0135fc99edea93ff
  languageName: node
  linkType: hard

"has-flag@npm:^3.0.0":
  version: 3.0.0
  resolution: "has-flag@npm:3.0.0"
  checksum: 4a15638b454bf086c8148979aae044dd6e39d63904cd452d970374fa6a87623423da485dfb814e7be882e05c096a7ccf1ebd48e7e7501d0208d8384ff4dea73b
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 261a1357037ead75e338156b1f9452c016a37dcd3283a972a30d9e4a87441ba372c8b81f818cd0fbcd9c0354b4ae7e18b9e1afa1971164aef6d18c2b6095a8ad
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3, has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: b2316c7302a0e8ba3aaba215f834e96c22c86f192e7310bdf689dd0e6999510c89b00fbc5742571507cebf25764d68c988b3a0da217369a73596191ac0ce694b
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-tostringtag@npm:1.0.2"
  dependencies:
    has-symbols: ^1.0.3
  checksum: 999d60bb753ad714356b2c6c87b7fb74f32463b8426e159397da4bde5bca7e598ab1073f4d8d4deafac297f2eb311484cd177af242776bf05f0d11565680468d
  languageName: node
  linkType: hard

"has-unicode@npm:^2.0.1":
  version: 2.0.1
  resolution: "has-unicode@npm:2.0.1"
  checksum: 1eab07a7436512db0be40a710b29b5dc21fa04880b7f63c9980b706683127e3c1b57cb80ea96d47991bdae2dfe479604f6a1ba410106ee1046a41d1bd0814400
  languageName: node
  linkType: hard

"has@npm:^1.0.3":
  version: 1.0.3
  resolution: "has@npm:1.0.3"
  dependencies:
    function-bind: ^1.1.1
  checksum: b9ad53d53be4af90ce5d1c38331e712522417d017d5ef1ebd0507e07c2fbad8686fffb8e12ddecd4c39ca9b9b47431afbb975b8abf7f3c3b82c98e9aad052792
  languageName: node
  linkType: hard

"hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: ^1.1.2
  checksum: e8516f776a15149ca6c6ed2ae3110c417a00b62260e222590e54aa367cbcd6ed99122020b37b7fbdf05748df57b265e70095d7bf35a47660587619b15ffb93db
  languageName: node
  linkType: hard

"he@npm:^1.2.0":
  version: 1.2.0
  resolution: "he@npm:1.2.0"
  bin:
    he: bin/he
  checksum: 3d4d6babccccd79c5c5a3f929a68af33360d6445587d628087f39a965079d84f18ce9c3d3f917ee1e3978916fc833bb8b29377c3b403f919426f91bc6965e7a7
  languageName: node
  linkType: hard

"highlight-words-core@npm:^1.2.0":
  version: 1.2.2
  resolution: "highlight-words-core@npm:1.2.2"
  checksum: 737758a8a572c82919552b031df300016164b7d0db6a819d24bc6c7ca2279d3cd6d03497728930d6402423c7a3fc2f42c628a9b01b025c704a0b56a635377511
  languageName: node
  linkType: hard

"html-loader@npm:~1.3.0":
  version: 1.3.2
  resolution: "html-loader@npm:1.3.2"
  dependencies:
    html-minifier-terser: ^5.1.1
    htmlparser2: ^4.1.0
    loader-utils: ^2.0.0
    schema-utils: ^3.0.0
  peerDependencies:
    webpack: ^4.0.0 || ^5.0.0
  checksum: 51da7c05e41ee0bdd5c43ca5b9d87e2a69750617503c4333e3e9aa0ca5778f0cc45462e7f5ee1098f319c19782d8b2d7588bf4be66ea0fff7046e54aee47b00b
  languageName: node
  linkType: hard

"html-minifier-terser@npm:^5.1.1":
  version: 5.1.1
  resolution: "html-minifier-terser@npm:5.1.1"
  dependencies:
    camel-case: ^4.1.1
    clean-css: ^4.2.3
    commander: ^4.1.1
    he: ^1.2.0
    param-case: ^3.0.3
    relateurl: ^0.2.7
    terser: ^4.6.3
  bin:
    html-minifier-terser: cli.js
  checksum: 75ff3ff886631b9ecb3035acb8e7dd98c599bb4d4618ad6f7e487ee9752987dddcf6848dc3c1ab1d7fc1ad4484337c2ce39c19eac17b0342b4b15e4294c8a904
  languageName: node
  linkType: hard

"html-minifier-terser@npm:^6.0.2":
  version: 6.1.0
  resolution: "html-minifier-terser@npm:6.1.0"
  dependencies:
    camel-case: ^4.1.2
    clean-css: ^5.2.2
    commander: ^8.3.0
    he: ^1.2.0
    param-case: ^3.0.4
    relateurl: ^0.2.7
    terser: ^5.10.0
  bin:
    html-minifier-terser: cli.js
  checksum: ac52c14006476f773204c198b64838477859dc2879490040efab8979c0207424da55d59df7348153f412efa45a0840a1ca3c757bf14767d23a15e3e389d37a93
  languageName: node
  linkType: hard

"html-webpack-plugin@npm:^5.5.0":
  version: 5.5.0
  resolution: "html-webpack-plugin@npm:5.5.0"
  dependencies:
    "@types/html-minifier-terser": ^6.0.0
    html-minifier-terser: ^6.0.2
    lodash: ^4.17.21
    pretty-error: ^4.0.0
    tapable: ^2.0.0
  peerDependencies:
    webpack: ^5.20.0
  checksum: f3d84d0df71fe2f5bac533cc74dce41ab058558cdcc6ff767d166a2abf1cf6fb8491d54d60ddbb34e95c00394e379ba52e0468e0284d1d0cc6a42987056e8219
  languageName: node
  linkType: hard

"htmlparser2@npm:^4.1.0":
  version: 4.1.0
  resolution: "htmlparser2@npm:4.1.0"
  dependencies:
    domelementtype: ^2.0.1
    domhandler: ^3.0.0
    domutils: ^2.0.0
    entities: ^2.0.0
  checksum: 615fcf34ae74775eba9d2c7c54034201645ac4146dfe2889cda21939aa77806ad3aee27963ae72c5c2da23ce7b0b99b2533e1d9f327b74821cc11f755cc5153f
  languageName: node
  linkType: hard

"htmlparser2@npm:^6.1.0":
  version: 6.1.0
  resolution: "htmlparser2@npm:6.1.0"
  dependencies:
    domelementtype: ^2.0.1
    domhandler: ^4.0.0
    domutils: ^2.5.2
    entities: ^2.0.0
  checksum: 81a7b3d9c3bb9acb568a02fc9b1b81ffbfa55eae7f1c41ae0bf840006d1dbf54cb3aa245b2553e2c94db674840a9f0fdad7027c9a9d01a062065314039058c4e
  languageName: node
  linkType: hard

"htmlparser2@npm:^8.0.0":
  version: 8.0.1
  resolution: "htmlparser2@npm:8.0.1"
  dependencies:
    domelementtype: ^2.3.0
    domhandler: ^5.0.2
    domutils: ^3.0.1
    entities: ^4.3.0
  checksum: 06d5c71e8313597722bc429ae2a7a8333d77bd3ab07ccb916628384b37332027b047f8619448d8f4a3312b6609c6ea3302a4e77435d859e9e686999e6699ca39
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.0.0, http-cache-semantics@npm:^4.1.0":
  version: 4.1.1
  resolution: "http-cache-semantics@npm:4.1.1"
  checksum: 83ac0bc60b17a3a36f9953e7be55e5c8f41acc61b22583060e8dedc9dd5e3607c823a88d0926f9150e571f90946835c7fe150732801010845c72cd8bbff1a236
  languageName: node
  linkType: hard

"http-errors@npm:2.0.0":
  version: 2.0.0
  resolution: "http-errors@npm:2.0.0"
  dependencies:
    depd: 2.0.0
    inherits: 2.0.4
    setprototypeof: 1.2.0
    statuses: 2.0.1
    toidentifier: 1.0.1
  checksum: 9b0a3782665c52ce9dc658a0d1560bcb0214ba5699e4ea15aefb2a496e2ca83db03ebc42e1cce4ac1f413e4e0d2d736a3fd755772c556a9a06853ba2a0b7d920
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^5.0.0":
  version: 5.0.0
  resolution: "http-proxy-agent@npm:5.0.0"
  dependencies:
    "@tootallnate/once": 2
    agent-base: 6
    debug: 4
  checksum: e2ee1ff1656a131953839b2a19cd1f3a52d97c25ba87bd2559af6ae87114abf60971e498021f9b73f9fd78aea8876d1fb0d4656aac8a03c6caa9fc175f22b786
  languageName: node
  linkType: hard

"http-signature@npm:~1.4.0":
  version: 1.4.0
  resolution: "http-signature@npm:1.4.0"
  dependencies:
    assert-plus: ^1.0.0
    jsprim: ^2.0.2
    sshpk: ^1.18.0
  checksum: f07f4cc0481e4461c68b9b7d1a25bf2ec4cef8e0061812b989c1e64f504b4b11f75f88022102aea05d25d47a87789599f1a310b1f8a56945a50c93e54c7ee076
  languageName: node
  linkType: hard

"http-status-codes@npm:2.2.0":
  version: 2.2.0
  resolution: "http-status-codes@npm:2.2.0"
  checksum: 31e1d730856210445da0907d9b484629e69e4fe92ac032478a7aa4d89e5b215e2b4e75d7ebce40d0537b6850bd281b2f65c7cc36cc2677e5de056d6cea1045ce
  languageName: node
  linkType: hard

"http-status-codes@npm:2.3.0":
  version: 2.3.0
  resolution: "http-status-codes@npm:2.3.0"
  checksum: dae3b99e0155441b6df28e8265ff27c56a45f82c6092f736414233e9ccf063d5ea93c1e1279e8b499c4642e2538b37995c76b1640ed3f615d0e2883d3a1dcfd5
  languageName: node
  linkType: hard

"http2-wrapper@npm:^1.0.0-beta.5.2":
  version: 1.0.3
  resolution: "http2-wrapper@npm:1.0.3"
  dependencies:
    quick-lru: ^5.1.1
    resolve-alpn: ^1.0.0
  checksum: 74160b862ec699e3f859739101ff592d52ce1cb207b7950295bf7962e4aa1597ef709b4292c673bece9c9b300efad0559fc86c71b1409c7a1e02b7229456003e
  languageName: node
  linkType: hard

"https-proxy-agent@npm:5.0.1, https-proxy-agent@npm:^5.0.0":
  version: 5.0.1
  resolution: "https-proxy-agent@npm:5.0.1"
  dependencies:
    agent-base: 6
    debug: 4
  checksum: 571fccdf38184f05943e12d37d6ce38197becdd69e58d03f43637f7fa1269cf303a7d228aa27e5b27bbd3af8f09fd938e1c91dcfefff2df7ba77c20ed8dfc765
  languageName: node
  linkType: hard

"humanize-ms@npm:^1.2.1":
  version: 1.2.1
  resolution: "humanize-ms@npm:1.2.1"
  dependencies:
    ms: ^2.0.0
  checksum: 9c7a74a2827f9294c009266c82031030eae811ca87b0da3dceb8d6071b9bde22c9f3daef0469c3c533cc67a97d8a167cd9fc0389350e5f415f61a79b171ded16
  languageName: node
  linkType: hard

"iconv-lite@npm:0.4.24, iconv-lite@npm:^0.4.24":
  version: 0.4.24
  resolution: "iconv-lite@npm:0.4.24"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3"
  checksum: bd9f120f5a5b306f0bc0b9ae1edeb1577161503f5f8252a20f1a9e56ef8775c9959fd01c55f2d3a39d9a8abaf3e30c1abeb1895f367dcbbe0a8fd1c9ca01c4f6
  languageName: node
  linkType: hard

"iconv-lite@npm:0.6, iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3.0.0"
  checksum: 3f60d47a5c8fc3313317edfd29a00a692cc87a19cac0159e2ce711d0ebc9019064108323b5e493625e25594f11c6236647d8e256fbe7a58f4a3b33b89e6d30bf
  languageName: node
  linkType: hard

"icss-utils@npm:^5.0.0, icss-utils@npm:^5.1.0":
  version: 5.1.0
  resolution: "icss-utils@npm:5.1.0"
  peerDependencies:
    postcss: ^8.1.0
  checksum: 5c324d283552b1269cfc13a503aaaa172a280f914e5b81544f3803bc6f06a3b585fb79f66f7c771a2c052db7982c18bf92d001e3b47282e3abbbb4c4cc488d68
  languageName: node
  linkType: hard

"ieee754@npm:^1.2.1":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: 5144c0c9815e54ada181d80a0b810221a253562422e7c6c3a60b1901154184f49326ec239d618c416c1c5945a2e197107aee8d986a3dd836b53dffefd99b5e7e
  languageName: node
  linkType: hard

"ignore@npm:^5.1.1":
  version: 5.2.4
  resolution: "ignore@npm:5.2.4"
  checksum: 3d4c309c6006e2621659311783eaea7ebcd41fe4ca1d78c91c473157ad6666a57a2df790fe0d07a12300d9aac2888204d7be8d59f9aaf665b1c7fcdb432517ef
  languageName: node
  linkType: hard

"import-local@npm:^3.0.2":
  version: 3.1.0
  resolution: "import-local@npm:3.1.0"
  dependencies:
    pkg-dir: ^4.2.0
    resolve-cwd: ^3.0.0
  bin:
    import-local-fixture: fixtures/cli.js
  checksum: bfcdb63b5e3c0e245e347f3107564035b128a414c4da1172a20dc67db2504e05ede4ac2eee1252359f78b0bfd7b19ef180aec427c2fce6493ae782d73a04cddd
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 7cae75c8cd9a50f57dadd77482359f659eaebac0319dd9368bcd1714f55e65badd6929ca58569da2b6494ef13fdd5598cd700b1eba23f8b79c5f19d195a3ecf7
  languageName: node
  linkType: hard

"indent-string@npm:^4.0.0":
  version: 4.0.0
  resolution: "indent-string@npm:4.0.0"
  checksum: 824cfb9929d031dabf059bebfe08cf3137365e112019086ed3dcff6a0a7b698cb80cf67ccccde0e25b9e2d7527aa6cc1fed1ac490c752162496caba3e6699612
  languageName: node
  linkType: hard

"infer-owner@npm:^1.0.4":
  version: 1.0.4
  resolution: "infer-owner@npm:1.0.4"
  checksum: 181e732764e4a0611576466b4b87dac338972b839920b2a8cde43642e4ed6bd54dc1fb0b40874728f2a2df9a1b097b8ff83b56d5f8f8e3927f837fdcb47d8a89
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: ^1.3.0
    wrappy: 1
  checksum: f4f76aa072ce19fae87ce1ef7d221e709afb59d445e05d47fba710e85470923a75de35bfae47da6de1b18afc3ce83d70facf44cfb0aff89f0a3f45c0a0244dfd
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:2.0.4, inherits@npm:^2.0.1, inherits@npm:^2.0.3, inherits@npm:^2.0.4, inherits@npm:~2.0.3":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 4a48a733847879d6cf6691860a6b1e3f0f4754176e4d71494c41f3475553768b10f84b5ce1d40fbd0e34e6bfbb864ee35858ad4dd2cf31e02fc4a154b724d7f1
  languageName: node
  linkType: hard

"ini@npm:~1.3.0":
  version: 1.3.8
  resolution: "ini@npm:1.3.8"
  checksum: dfd98b0ca3a4fc1e323e38a6c8eb8936e31a97a918d3b377649ea15bdb15d481207a0dda1021efbd86b464cae29a0d33c1d7dcaf6c5672bee17fa849bc50a1b3
  languageName: node
  linkType: hard

"inquirer@npm:^9.1.4":
  version: 9.1.4
  resolution: "inquirer@npm:9.1.4"
  dependencies:
    ansi-escapes: ^6.0.0
    chalk: ^5.1.2
    cli-cursor: ^4.0.0
    cli-width: ^4.0.0
    external-editor: ^3.0.3
    figures: ^5.0.0
    lodash: ^4.17.21
    mute-stream: 0.0.8
    ora: ^6.1.2
    run-async: ^2.4.0
    rxjs: ^7.5.7
    string-width: ^5.1.2
    strip-ansi: ^7.0.1
    through: ^2.3.6
    wrap-ansi: ^8.0.1
  checksum: b9acb56dfc01fdc3aac5997260b9c88b84893e270254cb67a8bef8d074d2deeea500963e69247510f1790a6b656b0a11e981441d084ce6d906e7e2a7c5441aa2
  languageName: node
  linkType: hard

"internmap@npm:1 - 2, internmap@npm:^1.0.0":
  version: 1.0.1
  resolution: "internmap@npm:1.0.1"
  checksum: 9d00f8c0cf873a24a53a5a937120dab634c41f383105e066bb318a61864e6292d24eb9516e8e7dccfb4420ec42ca474a0f28ac9a6cc82536898fa09bbbe53813
  languageName: node
  linkType: hard

"interpret@npm:^3.1.1":
  version: 3.1.1
  resolution: "interpret@npm:3.1.1"
  checksum: 35cebcf48c7351130437596d9ab8c8fe131ce4038da4561e6d665f25640e0034702a031cf7e3a5cea60ac7ac548bf17465e0571ede126f3d3a6933152171ac82
  languageName: node
  linkType: hard

"ip@npm:^2.0.0":
  version: 2.0.1
  resolution: "ip@npm:2.0.1"
  checksum: d765c9fd212b8a99023a4cde6a558a054c298d640fec1020567494d257afd78ca77e37126b1a3ef0e053646ced79a816bf50621d38d5e768cdde0431fa3b0d35
  languageName: node
  linkType: hard

"ipaddr.js@npm:1.9.1":
  version: 1.9.1
  resolution: "ipaddr.js@npm:1.9.1"
  checksum: f88d3825981486f5a1942414c8d77dd6674dd71c065adcfa46f578d677edcb99fda25af42675cb59db492fdf427b34a5abfcde3982da11a8fd83a500b41cfe77
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.3.1":
  version: 0.3.2
  resolution: "is-arrayish@npm:0.3.2"
  checksum: 977e64f54d91c8f169b59afcd80ff19227e9f5c791fa28fa2e5bce355cbaf6c2c356711b734656e80c9dd4a854dd7efcf7894402f1031dfc5de5d620775b4d5f
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: ^2.0.0
  checksum: 84192eb88cff70d320426f35ecd63c3d6d495da9d805b19bc65b518984b7c0760280e57dbf119b7e9be6b161784a5a673ab2c6abe83abb5198a432232ad5b35c
  languageName: node
  linkType: hard

"is-core-module@npm:^2.9.0":
  version: 2.11.0
  resolution: "is-core-module@npm:2.11.0"
  dependencies:
    has: ^1.0.3
  checksum: f96fd490c6b48eb4f6d10ba815c6ef13f410b0ba6f7eb8577af51697de523e5f2cd9de1c441b51d27251bf0e4aebc936545e33a5d26d5d51f28d25698d4a8bab
  languageName: node
  linkType: hard

"is-deflate@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-deflate@npm:1.0.0"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: df033653d06d0eb567461e58a7a8c9f940bd8c22274b94bf7671ab36df5719791aae15eef6d83bbb5e23283967f2f984b8914559d4449efda578c775c4be6f85
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 44a30c29457c7fb8f00297bce733f0a64cd22eca270f83e58c105e0d015e45c019491a4ab2faef91ab51d4738c670daff901c799f6a700e27f7314029e99e348
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.1, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: ^2.1.1
  checksum: d381c1319fcb69d341cc6e6c7cd588e17cd94722d9a32dbd60660b993c4fb7d0f19438674e68dfec686d09b7c73139c9166b47597f846af387450224a8101ab4
  languageName: node
  linkType: hard

"is-gzip@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-gzip@npm:1.0.0"
  checksum: 0d28931c1f445fa29c900cf9f48e06e9d1d477a3bf7bd7332e7ce68f1333ccd8cb381de2f0f62a9a262d9c0912608a9a71b4a40e788e201b3dbd67072bb20d86
  languageName: node
  linkType: hard

"is-interactive@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-interactive@npm:2.0.0"
  checksum: e8d52ad490bed7ae665032c7675ec07732bbfe25808b0efbc4d5a76b1a1f01c165f332775c63e25e9a03d319ebb6b24f571a9e902669fc1e40b0a60b5be6e26c
  languageName: node
  linkType: hard

"is-lambda@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-lambda@npm:1.0.1"
  checksum: 93a32f01940220532e5948538699ad610d5924ac86093fcee83022252b363eb0cc99ba53ab084a04e4fb62bf7b5731f55496257a4c38adf87af9c4d352c71c35
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 456ac6f8e0f3111ed34668a624e45315201dff921e5ac181f8ec24923b99e9f32ca1a194912dc79d539c97d33dba17dc635202ff0b2cf98326f608323276d27a
  languageName: node
  linkType: hard

"is-plain-obj@npm:2.1.0":
  version: 2.1.0
  resolution: "is-plain-obj@npm:2.1.0"
  checksum: cec9100678b0a9fe0248a81743041ed990c2d4c99f893d935545cfbc42876cbe86d207f3b895700c690ad2fa520e568c44afc1605044b535a7820c1d40e38daa
  languageName: node
  linkType: hard

"is-plain-object@npm:^2.0.4":
  version: 2.0.4
  resolution: "is-plain-object@npm:2.0.4"
  dependencies:
    isobject: ^3.0.1
  checksum: 2a401140cfd86cabe25214956ae2cfee6fbd8186809555cd0e84574f88de7b17abacb2e477a6a658fa54c6083ecbda1e6ae404c7720244cd198903848fca70ca
  languageName: node
  linkType: hard

"is-plain-object@npm:^5.0.0":
  version: 5.0.0
  resolution: "is-plain-object@npm:5.0.0"
  checksum: e32d27061eef62c0847d303125440a38660517e586f2f3db7c9d179ae5b6674ab0f469d519b2e25c147a1a3bc87156d0d5f4d8821e0ce4a9ee7fe1fcf11ce45c
  languageName: node
  linkType: hard

"is-promise@npm:^2.1.0":
  version: 2.2.2
  resolution: "is-promise@npm:2.2.2"
  checksum: 18bf7d1c59953e0ad82a1ed963fb3dc0d135c8f299a14f89a17af312fc918373136e56028e8831700e1933519630cc2fd4179a777030330fde20d34e96f40c78
  languageName: node
  linkType: hard

"is-typedarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "is-typedarray@npm:1.0.0"
  checksum: 3508c6cd0a9ee2e0df2fa2e9baabcdc89e911c7bd5cf64604586697212feec525aa21050e48affb5ffc3df20f0f5d2e2cf79b08caa64e1ccc9578e251763aef7
  languageName: node
  linkType: hard

"is-unicode-supported@npm:^1.1.0, is-unicode-supported@npm:^1.2.0":
  version: 1.3.0
  resolution: "is-unicode-supported@npm:1.3.0"
  checksum: 20a1fc161afafaf49243551a5ac33b6c4cf0bbcce369fcd8f2951fbdd000c30698ce320de3ee6830497310a8f41880f8066d440aa3eb0a853e2aa4836dd89abc
  languageName: node
  linkType: hard

"isarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "isarray@npm:1.0.0"
  checksum: f032df8e02dce8ec565cf2eb605ea939bdccea528dbcf565cdf92bfa2da9110461159d86a537388ef1acef8815a330642d7885b29010e8f7eac967c9993b65ab
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 26bf6c5480dda5161c820c5b5c751ae1e766c587b1f951ea3fcfc973bafb7831ae5b54a31a69bd670220e42e99ec154475025a468eae58ea262f813fdc8d1c62
  languageName: node
  linkType: hard

"isobject@npm:^3.0.1":
  version: 3.0.1
  resolution: "isobject@npm:3.0.1"
  checksum: db85c4c970ce30693676487cca0e61da2ca34e8d4967c2e1309143ff910c207133a969f9e4ddb2dc6aba670aabce4e0e307146c310350b298e74a31f7d464703
  languageName: node
  linkType: hard

"isomorphic.js@npm:^0.2.4":
  version: 0.2.5
  resolution: "isomorphic.js@npm:0.2.5"
  checksum: d8d1b083f05f3c337a06628b982ac3ce6db953bbef14a9de8ad49131250c3592f864b73c12030fdc9ef138ce97b76ef55c7d96a849561ac215b1b4b9d301c8e9
  languageName: node
  linkType: hard

"isstream@npm:~0.1.2":
  version: 0.1.2
  resolution: "isstream@npm:0.1.2"
  checksum: 1eb2fe63a729f7bdd8a559ab552c69055f4f48eb5c2f03724430587c6f450783c8f1cd936c1c952d0a927925180fcc892ebd5b174236cf1065d4bd5bdb37e963
  languageName: node
  linkType: hard

"jackspeak@npm:^2.3.5":
  version: 2.3.6
  resolution: "jackspeak@npm:2.3.6"
  dependencies:
    "@isaacs/cliui": ^8.0.2
    "@pkgjs/parseargs": ^0.11.0
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: 57d43ad11eadc98cdfe7496612f6bbb5255ea69fe51ea431162db302c2a11011642f50cfad57288bd0aea78384a0612b16e131944ad8ecd09d619041c8531b54
  languageName: node
  linkType: hard

"jest-worker@npm:^27.4.5":
  version: 27.5.1
  resolution: "jest-worker@npm:27.5.1"
  dependencies:
    "@types/node": "*"
    merge-stream: ^2.0.0
    supports-color: ^8.0.0
  checksum: 98cd68b696781caed61c983a3ee30bf880b5bd021c01d98f47b143d4362b85d0737f8523761e2713d45e18b4f9a2b98af1eaee77afade4111bb65c77d6f7c980
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.0 || ^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 8a95213a5a77deb6cbe94d86340e8d9ace2b93bc367790b260101d2f36a2eaf4e4e22d9fa9cf459b38af3a32fb4190e638024cf82ec95ef708680e405ea7cc78
  languageName: node
  linkType: hard

"js-yaml@npm:4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: ^2.0.1
  bin:
    js-yaml: bin/js-yaml.js
  checksum: c7830dfd456c3ef2c6e355cc5a92e6700ceafa1d14bba54497b34a99f0376cecbb3e9ac14d3e5849b426d5a5140709a66237a8c991c675431271c4ce5504151a
  languageName: node
  linkType: hard

"js-yaml@npm:^3.10.0":
  version: 3.14.1
  resolution: "js-yaml@npm:3.14.1"
  dependencies:
    argparse: ^1.0.7
    esprima: ^4.0.0
  bin:
    js-yaml: bin/js-yaml.js
  checksum: bef146085f472d44dee30ec34e5cf36bf89164f5d585435a3d3da89e52622dff0b188a580e4ad091c3341889e14cb88cac6e4deb16dc5b1e9623bb0601fc255c
  languageName: node
  linkType: hard

"jsbn@npm:~0.1.0":
  version: 0.1.1
  resolution: "jsbn@npm:0.1.1"
  checksum: e5ff29c1b8d965017ef3f9c219dacd6e40ad355c664e277d31246c90545a02e6047018c16c60a00f36d561b3647215c41894f5d869ada6908a2e0ce4200c88f2
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.1":
  version: 3.0.1
  resolution: "json-buffer@npm:3.0.1"
  checksum: 9026b03edc2847eefa2e37646c579300a1f3a4586cfb62bf857832b60c852042d0d6ae55d1afb8926163fa54c2b01d83ae24705f34990348bdac6273a29d4581
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^2.3.1":
  version: 2.3.1
  resolution: "json-parse-even-better-errors@npm:2.3.1"
  checksum: 798ed4cf3354a2d9ccd78e86d2169515a0097a5c133337807cdf7f1fc32e1391d207ccfc276518cc1d7d8d4db93288b8a50ba4293d212ad1336e52a8ec0a941f
  languageName: node
  linkType: hard

"json-schema-compare@npm:^0.2.2":
  version: 0.2.2
  resolution: "json-schema-compare@npm:0.2.2"
  dependencies:
    lodash: ^4.17.4
  checksum: dd6f2173857c8e3b77d6ebdfa05bd505bba5b08709ab46b532722f5d1c33b5fee1fc8f3c97d0c0d011db25f9f3b0baf7ab783bb5f55c32abd9f1201760e43c2c
  languageName: node
  linkType: hard

"json-schema-merge-allof@npm:^0.8.1":
  version: 0.8.1
  resolution: "json-schema-merge-allof@npm:0.8.1"
  dependencies:
    compute-lcm: ^1.1.2
    json-schema-compare: ^0.2.2
    lodash: ^4.17.20
  checksum: 82700f6ac77351959138d6b153d77375a8c29cf48d907241b85c8292dd77aabd8cb816400f2b0d17062c4ccc8893832ec4f664ab9c814927ef502e7a595ea873
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 7486074d3ba247769fda17d5181b345c9fb7d12e0da98b22d1d71a5db9698d8b4bd900a3ec1a4ffdd60846fc2556274a5c894d0c48795f14cb03aeae7b55260b
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^1.0.0":
  version: 1.0.0
  resolution: "json-schema-traverse@npm:1.0.0"
  checksum: 02f2f466cdb0362558b2f1fd5e15cce82ef55d60cd7f8fa828cf35ba74330f8d767fcae5c5c2adb7851fa811766c694b9405810879bc4e1ddd78a7c0e03658ad
  languageName: node
  linkType: hard

"json-schema@npm:0.4.0, json-schema@npm:^0.4.0":
  version: 0.4.0
  resolution: "json-schema@npm:0.4.0"
  checksum: 66389434c3469e698da0df2e7ac5a3281bcff75e797a5c127db7c5b56270e01ae13d9afa3c03344f76e32e81678337a8c912bdbb75101c62e487dc3778461d72
  languageName: node
  linkType: hard

"json-stringify-pretty-compact@npm:^3.0.0, json-stringify-pretty-compact@npm:~3.0.0":
  version: 3.0.0
  resolution: "json-stringify-pretty-compact@npm:3.0.0"
  checksum: 01ab5c5c8260299414868d96db97f53aef93c290fe469edd9a1363818e795006e01c952fa2fd7b47cbbab506d5768998eccc25e1da4fa2ccfebd1788c6098791
  languageName: node
  linkType: hard

"json-stringify-safe@npm:~5.0.1":
  version: 5.0.1
  resolution: "json-stringify-safe@npm:5.0.1"
  checksum: 48ec0adad5280b8a96bb93f4563aa1667fd7a36334f79149abd42446d0989f2ddc58274b479f4819f1f00617957e6344c886c55d05a4e15ebb4ab931e4a6a8ee
  languageName: node
  linkType: hard

"json5@npm:^2.1.2, json5@npm:^2.2.3":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 2a7436a93393830bce797d4626275152e37e877b265e94ca69c99e3d20c2b9dab021279146a39cdb700e71b2dd32a4cebd1514cd57cee102b1af906ce5040349
  languageName: node
  linkType: hard

"jsonfile@npm:^6.0.1":
  version: 6.1.0
  resolution: "jsonfile@npm:6.1.0"
  dependencies:
    graceful-fs: ^4.1.6
    universalify: ^2.0.0
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 7af3b8e1ac8fe7f1eccc6263c6ca14e1966fcbc74b618d3c78a0a2075579487547b94f72b7a1114e844a1e15bb00d440e5d1720bfc4612d790a6f285d5ea8354
  languageName: node
  linkType: hard

"jsonparse@npm:^1.2.0":
  version: 1.3.1
  resolution: "jsonparse@npm:1.3.1"
  checksum: 6514a7be4674ebf407afca0eda3ba284b69b07f9958a8d3113ef1005f7ec610860c312be067e450c569aab8b89635e332cee3696789c750692bb60daba627f4d
  languageName: node
  linkType: hard

"jsonpointer@npm:^5.0.1":
  version: 5.0.1
  resolution: "jsonpointer@npm:5.0.1"
  checksum: 0b40f712900ad0c846681ea2db23b6684b9d5eedf55807b4708c656f5894b63507d0e28ae10aa1bddbea551241035afe62b6df0800fc94c2e2806a7f3adecd7c
  languageName: node
  linkType: hard

"jsonwebtoken@npm:9.0.2":
  version: 9.0.2
  resolution: "jsonwebtoken@npm:9.0.2"
  dependencies:
    jws: ^3.2.2
    lodash.includes: ^4.3.0
    lodash.isboolean: ^3.0.3
    lodash.isinteger: ^4.0.4
    lodash.isnumber: ^3.0.3
    lodash.isplainobject: ^4.0.6
    lodash.isstring: ^4.0.1
    lodash.once: ^4.0.0
    ms: ^2.1.1
    semver: ^7.5.4
  checksum: fc739a6a8b33f1974f9772dca7f8493ca8df4cc31c5a09dcfdb7cff77447dcf22f4236fb2774ef3fe50df0abeb8e1c6f4c41eba82f500a804ab101e2fbc9d61a
  languageName: node
  linkType: hard

"jsprim@npm:^2.0.2":
  version: 2.0.2
  resolution: "jsprim@npm:2.0.2"
  dependencies:
    assert-plus: 1.0.0
    extsprintf: 1.3.0
    json-schema: 0.4.0
    verror: 1.10.0
  checksum: d175f6b1991e160cb0aa39bc857da780e035611986b5492f32395411879fdaf4e513d98677f08f7352dac93a16b66b8361c674b86a3fa406e2e7af6b26321838
  languageName: node
  linkType: hard

"jwa@npm:^1.4.1":
  version: 1.4.1
  resolution: "jwa@npm:1.4.1"
  dependencies:
    buffer-equal-constant-time: 1.0.1
    ecdsa-sig-formatter: 1.0.11
    safe-buffer: ^5.0.1
  checksum: ff30ea7c2dcc61f3ed2098d868bf89d43701605090c5b21b5544b512843ec6fd9e028381a4dda466cbcdb885c2d1150f7c62e7168394ee07941b4098e1035e2f
  languageName: node
  linkType: hard

"jws@npm:^3.2.2":
  version: 3.2.2
  resolution: "jws@npm:3.2.2"
  dependencies:
    jwa: ^1.4.1
    safe-buffer: ^5.0.1
  checksum: f0213fe5b79344c56cd443428d8f65c16bf842dc8cb8f5aed693e1e91d79c20741663ad6eff07a6d2c433d1831acc9814e8d7bada6a0471fbb91d09ceb2bf5c2
  languageName: node
  linkType: hard

"katex@npm:^0.16.9":
  version: 0.16.21
  resolution: "katex@npm:0.16.21"
  dependencies:
    commander: ^8.3.0
  bin:
    katex: cli.js
  checksum: 14180322a4e8fe9e4227a08b7d86fde9ee445859ff534e6a540b85eb5022b39ea2be70082776cce8c59b891c247fce3d1c1a090ea7821e005fd8b7bfee714936
  languageName: node
  linkType: hard

"keyv@npm:^4.0.0":
  version: 4.5.2
  resolution: "keyv@npm:4.5.2"
  dependencies:
    json-buffer: 3.0.1
  checksum: 13ad58303acd2261c0d4831b4658451603fd159e61daea2121fcb15feb623e75ee328cded0572da9ca76b7b3ceaf8e614f1806c6b3af5db73c9c35a345259651
  languageName: node
  linkType: hard

"khroma@npm:^2.1.0":
  version: 2.1.0
  resolution: "khroma@npm:2.1.0"
  checksum: b34ba39d3a9a52d388110bded8cb1c12272eb69c249d8eb26feab12d18a96a9bc4ceec4851d2afa43de4569f7d5ea78fa305965a3d0e96a38e02fe77c53677da
  languageName: node
  linkType: hard

"kind-of@npm:^6.0.2":
  version: 6.0.3
  resolution: "kind-of@npm:6.0.3"
  checksum: 3ab01e7b1d440b22fe4c31f23d8d38b4d9b91d9f291df683476576493d5dfd2e03848a8b05813dd0c3f0e835bc63f433007ddeceb71f05cb25c45ae1b19c6d3b
  languageName: node
  linkType: hard

"kleur@npm:4.1.5":
  version: 4.1.5
  resolution: "kleur@npm:4.1.5"
  checksum: 1dc476e32741acf0b1b5b0627ffd0d722e342c1b0da14de3e8ae97821327ca08f9fb944542fb3c126d90ac5f27f9d804edbe7c585bf7d12ef495d115e0f22c12
  languageName: node
  linkType: hard

"kolorist@npm:^1.8.0":
  version: 1.8.0
  resolution: "kolorist@npm:1.8.0"
  checksum: b056de671acc8a17f1e78d6d46c47dae3e06481eabc9fed213dd9079a7454fd3a7ea1226ec718df81c9208877f7475d038ac27a400958fec278d975839e33643
  languageName: node
  linkType: hard

"langium@npm:3.3.1":
  version: 3.3.1
  resolution: "langium@npm:3.3.1"
  dependencies:
    chevrotain: ~11.0.3
    chevrotain-allstar: ~0.3.0
    vscode-languageserver: ~9.0.1
    vscode-languageserver-textdocument: ~1.0.11
    vscode-uri: ~3.0.8
  checksum: b5fcf1cd8d9e8fd9f79425afae5926546f57a30506be20cf7638880a01b2b04ccfe1cd5cae599a7733ad4d38af0bf2ed9bd9e1a95cc5f3de1725628fa7883446
  languageName: node
  linkType: hard

"layout-base@npm:^1.0.0":
  version: 1.0.2
  resolution: "layout-base@npm:1.0.2"
  checksum: e4c312765ac4fa13b49c940e701461309c7a0aa07f784f81d31f626b945dced90a8abf83222388a5af16b7074271f745501a90ef5a3af676abb2e7eb16d55b2e
  languageName: node
  linkType: hard

"layout-base@npm:^2.0.0":
  version: 2.0.1
  resolution: "layout-base@npm:2.0.1"
  checksum: ef93baf044f67c3680f4f3a6d628bf4c7faba0f70f3e0abb16e4811bed087045208560347ca749e123d169cbf872505ad84e11fb21b0be925997227e042c7f43
  languageName: node
  linkType: hard

"lib0@npm:^0.2.42, lib0@npm:^0.2.49":
  version: 0.2.65
  resolution: "lib0@npm:0.2.65"
  dependencies:
    isomorphic.js: ^0.2.4
  bin:
    0gentesthtml: bin/gentesthtml.js
  checksum: c511810d209d8b616005ff9a93966d11c15b0a092953c1fb01f48dd53fb9830d19cbfe7dfffaa3f5913fba5801761fba76e3ca4dae8c2164cb606428a5f6a838
  languageName: node
  linkType: hard

"license-webpack-plugin@npm:^2.3.14":
  version: 2.3.21
  resolution: "license-webpack-plugin@npm:2.3.21"
  dependencies:
    "@types/webpack-sources": ^0.1.5
    webpack-sources: ^1.2.0
  peerDependenciesMeta:
    webpack:
      optional: true
  checksum: 6208bd2060d200fbffbcc89702c929d50c5a4a3f2158b046cf813b3f7f728bbbe4611b9fea2d67843bb5e7d64ad9122cc368a19ac73f5c4ad41765e6283bdc0c
  languageName: node
  linkType: hard

"license-webpack-plugin@npm:^4.0.2":
  version: 4.0.2
  resolution: "license-webpack-plugin@npm:4.0.2"
  dependencies:
    webpack-sources: ^3.0.0
  peerDependenciesMeta:
    webpack:
      optional: true
    webpack-sources:
      optional: true
  checksum: e88ebdb9c8bdfc0926dd7211d7fe2ee8697a44bb00a96bb5e6ca844b6acb7d24dd54eb17ec485e2e0140c3cc86709d1c2bd46e091ab52af076e1e421054c8322
  languageName: node
  linkType: hard

"loader-runner@npm:^4.2.0":
  version: 4.3.0
  resolution: "loader-runner@npm:4.3.0"
  checksum: a90e00dee9a16be118ea43fec3192d0b491fe03a32ed48a4132eb61d498f5536a03a1315531c19d284392a8726a4ecad71d82044c28d7f22ef62e029bf761569
  languageName: node
  linkType: hard

"loader-utils@npm:^2.0.0":
  version: 2.0.4
  resolution: "loader-utils@npm:2.0.4"
  dependencies:
    big.js: ^5.2.2
    emojis-list: ^3.0.0
    json5: ^2.1.2
  checksum: a5281f5fff1eaa310ad5e1164095689443630f3411e927f95031ab4fb83b4a98f388185bb1fe949e8ab8d4247004336a625e9255c22122b815bb9a4c5d8fc3b7
  languageName: node
  linkType: hard

"local-pkg@npm:^1.0.0":
  version: 1.1.1
  resolution: "local-pkg@npm:1.1.1"
  dependencies:
    mlly: ^1.7.4
    pkg-types: ^2.0.1
    quansync: ^0.2.8
  checksum: 523c6ecc67e783986cf1b0aa3372a07e3bdf5ff56d3fd00b15b2be598c6677d921e2be79170471fafa15a0de82ff88972782d81cdce9271c2b2f9b02a9f144bc
  languageName: node
  linkType: hard

"locate-path@npm:^5.0.0":
  version: 5.0.0
  resolution: "locate-path@npm:5.0.0"
  dependencies:
    p-locate: ^4.1.0
  checksum: 83e51725e67517287d73e1ded92b28602e3ae5580b301fe54bfb76c0c723e3f285b19252e375712316774cf52006cb236aed5704692c32db0d5d089b69696e30
  languageName: node
  linkType: hard

"lockfile@npm:1.0.4":
  version: 1.0.4
  resolution: "lockfile@npm:1.0.4"
  dependencies:
    signal-exit: ^3.0.2
  checksum: 8de35aace8acbe883cbca3cc3959e88904d57c79dccd4afffc64aea8f9cf7b4c63598d08b8add66fbf381f8fb3ce4fd4c518cd231c797c266b6c790eb7b33abc
  languageName: node
  linkType: hard

"lodash-es@npm:4.17.21, lodash-es@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash-es@npm:4.17.21"
  checksum: 05cbffad6e2adbb331a4e16fbd826e7faee403a1a04873b82b42c0f22090f280839f85b95393f487c1303c8a3d2a010048bf06151a6cbe03eee4d388fb0a12d2
  languageName: node
  linkType: hard

"lodash.curry@npm:^4.1.1":
  version: 4.1.1
  resolution: "lodash.curry@npm:4.1.1"
  checksum: 9192b70fe7df4d1ff780c0260bee271afa9168c93fe4fa24bc861900240531b59781b5fdaadf4644fea8f4fbcd96f0700539ab294b579ffc1022c6c15dcc462a
  languageName: node
  linkType: hard

"lodash.escape@npm:^4.0.1":
  version: 4.0.1
  resolution: "lodash.escape@npm:4.0.1"
  checksum: fcb54f457497256964d619d5cccbd80a961916fca60df3fe0fa3e7f052715c2944c0ed5aefb4f9e047d127d44aa2d55555f3350cb42c6549e9e293fb30b41e7f
  languageName: node
  linkType: hard

"lodash.includes@npm:^4.3.0":
  version: 4.3.0
  resolution: "lodash.includes@npm:4.3.0"
  checksum: 71092c130515a67ab3bd928f57f6018434797c94def7f46aafa417771e455ce3a4834889f4267b17887d7f75297dfabd96231bf704fd2b8c5096dc4a913568b6
  languageName: node
  linkType: hard

"lodash.isboolean@npm:^3.0.3":
  version: 3.0.3
  resolution: "lodash.isboolean@npm:3.0.3"
  checksum: b70068b4a8b8837912b54052557b21fc4774174e3512ed3c5b94621e5aff5eb6c68089d0a386b7e801d679cd105d2e35417978a5e99071750aa2ed90bffd0250
  languageName: node
  linkType: hard

"lodash.isinteger@npm:^4.0.4":
  version: 4.0.4
  resolution: "lodash.isinteger@npm:4.0.4"
  checksum: 6034821b3fc61a2ffc34e7d5644bb50c5fd8f1c0121c554c21ac271911ee0c0502274852845005f8651d51e199ee2e0cfebfe40aaa49c7fe617f603a8a0b1691
  languageName: node
  linkType: hard

"lodash.isnumber@npm:^3.0.3":
  version: 3.0.3
  resolution: "lodash.isnumber@npm:3.0.3"
  checksum: 913784275b565346255e6ae6a6e30b760a0da70abc29f3e1f409081585875105138cda4a429ff02577e1bc0a7ae2a90e0a3079a37f3a04c3d6c5aaa532f4cab2
  languageName: node
  linkType: hard

"lodash.isplainobject@npm:^4.0.6":
  version: 4.0.6
  resolution: "lodash.isplainobject@npm:4.0.6"
  checksum: 29c6351f281e0d9a1d58f1a4c8f4400924b4c79f18dfc4613624d7d54784df07efaff97c1ff2659f3e085ecf4fff493300adc4837553104cef2634110b0d5337
  languageName: node
  linkType: hard

"lodash.isstring@npm:^4.0.1":
  version: 4.0.1
  resolution: "lodash.isstring@npm:4.0.1"
  checksum: eaac87ae9636848af08021083d796e2eea3d02e80082ab8a9955309569cb3a463ce97fd281d7dc119e402b2e7d8c54a23914b15d2fc7fff56461511dc8937ba0
  languageName: node
  linkType: hard

"lodash.mergewith@npm:^4.6.1":
  version: 4.6.2
  resolution: "lodash.mergewith@npm:4.6.2"
  checksum: a6db2a9339752411f21b956908c404ec1e088e783a65c8b29e30ae5b3b6384f82517662d6f425cc97c2070b546cc2c7daaa8d33f78db7b6e9be06cd834abdeb8
  languageName: node
  linkType: hard

"lodash.once@npm:^4.0.0":
  version: 4.1.1
  resolution: "lodash.once@npm:4.1.1"
  checksum: d768fa9f9b4e1dc6453be99b753906f58990e0c45e7b2ca5a3b40a33111e5d17f6edf2f768786e2716af90a8e78f8f91431ab8435f761fef00f9b0c256f6d245
  languageName: node
  linkType: hard

"lodash@npm:4, lodash@npm:4.17.21, lodash@npm:^4.17.15, lodash@npm:^4.17.20, lodash@npm:^4.17.21, lodash@npm:^4.17.4, lodash@npm:^4.7.0":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: eb835a2e51d381e561e508ce932ea50a8e5a68f4ebdd771ea240d3048244a8d13658acbd502cd4829768c56f2e16bdd4340b9ea141297d472517b83868e677f7
  languageName: node
  linkType: hard

"log-symbols@npm:^5.1.0":
  version: 5.1.0
  resolution: "log-symbols@npm:5.1.0"
  dependencies:
    chalk: ^5.0.0
    is-unicode-supported: ^1.1.0
  checksum: 7291b6e7f1b3df6865bdaeb9b59605c832668ac2fa0965c63b1e7dd3700349aec09c1d7d40c368d5041ff58b7f89461a56e4009471921301af7b3609cbff9a29
  languageName: node
  linkType: hard

"loose-envify@npm:^1.1.0, loose-envify@npm:^1.4.0":
  version: 1.4.0
  resolution: "loose-envify@npm:1.4.0"
  dependencies:
    js-tokens: ^3.0.0 || ^4.0.0
  bin:
    loose-envify: cli.js
  checksum: 6517e24e0cad87ec9888f500c5b5947032cdfe6ef65e1c1936a0c48a524b81e65542c9c3edc91c97d5bddc806ee2a985dbc79be89215d613b1de5db6d1cfe6f4
  languageName: node
  linkType: hard

"lowdb@npm:1.0.0":
  version: 1.0.0
  resolution: "lowdb@npm:1.0.0"
  dependencies:
    graceful-fs: ^4.1.3
    is-promise: ^2.1.0
    lodash: 4
    pify: ^3.0.0
    steno: ^0.4.1
  checksum: 7ae89e3d6e00963129f72c4d4e1fe8e4cda5c08a46b4f4e525109483147e799df90c07d95aeced1c270cc10f4a24c6660fe1601cc4b3a6e2c3f922ad64517eab
  languageName: node
  linkType: hard

"lower-case@npm:^2.0.2":
  version: 2.0.2
  resolution: "lower-case@npm:2.0.2"
  dependencies:
    tslib: ^2.0.3
  checksum: 83a0a5f159ad7614bee8bf976b96275f3954335a84fad2696927f609ddae902802c4f3312d86668722e668bef41400254807e1d3a7f2e8c3eede79691aa1f010
  languageName: node
  linkType: hard

"lowercase-keys@npm:^2.0.0":
  version: 2.0.0
  resolution: "lowercase-keys@npm:2.0.0"
  checksum: 24d7ebd56ccdf15ff529ca9e08863f3c54b0b9d1edb97a3ae1af34940ae666c01a1e6d200707bce730a8ef76cb57cc10e65f245ecaaf7e6bc8639f2fb460ac23
  languageName: node
  linkType: hard

"lru-cache@npm:7.18.3, lru-cache@npm:^7.7.1":
  version: 7.18.3
  resolution: "lru-cache@npm:7.18.3"
  checksum: e550d772384709deea3f141af34b6d4fa392e2e418c1498c078de0ee63670f1f46f5eee746e8ef7e69e1c895af0d4224e62ee33e66a543a14763b0f2e74c1356
  languageName: node
  linkType: hard

"lru-cache@npm:^9.1.1 || ^10.0.0":
  version: 9.1.2
  resolution: "lru-cache@npm:9.1.2"
  checksum: d3415634be3908909081fc4c56371a8d562d9081eba70543d86871b978702fffd0e9e362b83921b27a29ae2b37b90f55675aad770a54ac83bb3e4de5049d4b15
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^10.0.3":
  version: 10.2.1
  resolution: "make-fetch-happen@npm:10.2.1"
  dependencies:
    agentkeepalive: ^4.2.1
    cacache: ^16.1.0
    http-cache-semantics: ^4.1.0
    http-proxy-agent: ^5.0.0
    https-proxy-agent: ^5.0.0
    is-lambda: ^1.0.1
    lru-cache: ^7.7.1
    minipass: ^3.1.6
    minipass-collect: ^1.0.2
    minipass-fetch: ^2.0.3
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    negotiator: ^0.6.3
    promise-retry: ^2.0.1
    socks-proxy-agent: ^7.0.0
    ssri: ^9.0.0
  checksum: 2332eb9a8ec96f1ffeeea56ccefabcb4193693597b132cd110734d50f2928842e22b84cfa1508e921b8385cdfd06dda9ad68645fed62b50fff629a580f5fb72c
  languageName: node
  linkType: hard

"markdown-to-jsx@npm:^7.3.2":
  version: 7.5.0
  resolution: "markdown-to-jsx@npm:7.5.0"
  peerDependencies:
    react: ">= 0.14.0"
  checksum: c9c6f1bfad5f2d9b1d3476eb0313ae3dffd0a9f14011c74efdd7c664fd32ee1842ef48abb16a496046f90361af49aa80a827e9d9c0bc04824a1986fdeb4d1852
  languageName: node
  linkType: hard

"marked-gfm-heading-id@npm:^4.1.1":
  version: 4.1.1
  resolution: "marked-gfm-heading-id@npm:4.1.1"
  dependencies:
    github-slugger: ^2.0.0
  peerDependencies:
    marked: ">=13 <16"
  checksum: 3f761f1b479a98af3b76d3ae40f6b80f3db682b138d052c6d892d061eced7d8e87936d165e6b371e0265cff1933024c0313114550af405f82c2b2ce62f30f186
  languageName: node
  linkType: hard

"marked-mangle@npm:^1.1.10":
  version: 1.1.10
  resolution: "marked-mangle@npm:1.1.10"
  peerDependencies:
    marked: ">=4 <16"
  checksum: 76ca4736e7f3839a5866329bfa2c274fb5699ef4962ed32b16b10b76c0aeb3ca5f67461041c9d7b8ab56900e84cb377aaa596a097f7327baa89342de442a138b
  languageName: node
  linkType: hard

"marked@npm:^15.0.7":
  version: 15.0.7
  resolution: "marked@npm:15.0.7"
  bin:
    marked: bin/marked.js
  checksum: 5863eddd39b9b47dd8dc0d9aa1a4a675580c5c2194ea25b887b905ea9c1e8115e3da50293db4814d5e84a98c466f164c4aebf6c3b9a0e86ecde43c1c4273dbee
  languageName: node
  linkType: hard

"math-intrinsics@npm:^1.1.0":
  version: 1.1.0
  resolution: "math-intrinsics@npm:1.1.0"
  checksum: 0e513b29d120f478c85a70f49da0b8b19bc638975eca466f2eeae0071f3ad00454c621bf66e16dd435896c208e719fc91ad79bbfba4e400fe0b372e7c1c9c9a2
  languageName: node
  linkType: hard

"mathjax-full@npm:^3.2.2":
  version: 3.2.2
  resolution: "mathjax-full@npm:3.2.2"
  dependencies:
    esm: ^3.2.25
    mhchemparser: ^4.1.0
    mj-context-menu: ^0.6.1
    speech-rule-engine: ^4.0.6
  checksum: 6fbccb9338e1fbf686202d924666d79ac9eb658157c1c8102ba018672188978c4cacfb1b6f65adf7d2d51dc79535ff3e32ba86b15e66d3011dda2ab99562d90d
  languageName: node
  linkType: hard

"media-typer@npm:0.3.0":
  version: 0.3.0
  resolution: "media-typer@npm:0.3.0"
  checksum: af1b38516c28ec95d6b0826f6c8f276c58aec391f76be42aa07646b4e39d317723e869700933ca6995b056db4b09a78c92d5440dc23657e6764be5d28874bba1
  languageName: node
  linkType: hard

"memoize-one@npm:^4.0.0":
  version: 4.0.3
  resolution: "memoize-one@npm:4.0.3"
  checksum: addd18c046542f57440ba70bf8ebd48663d17626cade681f777522ef70900a87ec72c5041bed8ece4f6d40a2cb58803bae388b50a4b740d64f36bcda20c147b7
  languageName: node
  linkType: hard

"merge-descriptors@npm:1.0.3":
  version: 1.0.3
  resolution: "merge-descriptors@npm:1.0.3"
  checksum: 52117adbe0313d5defa771c9993fe081e2d2df9b840597e966aadafde04ae8d0e3da46bac7ca4efc37d4d2b839436582659cd49c6a43eacb3fe3050896a105d1
  languageName: node
  linkType: hard

"merge-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-stream@npm:2.0.0"
  checksum: 6fa4dcc8d86629705cea944a4b88ef4cb0e07656ebf223fa287443256414283dd25d91c1cd84c77987f2aec5927af1a9db6085757cb43d90eb170ebf4b47f4f4
  languageName: node
  linkType: hard

"merge2@npm:^1.2.3, merge2@npm:^1.3.0":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 7268db63ed5169466540b6fb947aec313200bcf6d40c5ab722c22e242f651994619bcd85601602972d3c85bd2cc45a358a4c61937e9f11a061919a1da569b0c2
  languageName: node
  linkType: hard

"mermaid@npm:^11.6.0":
  version: 11.6.0
  resolution: "mermaid@npm:11.6.0"
  dependencies:
    "@braintree/sanitize-url": ^7.0.4
    "@iconify/utils": ^2.1.33
    "@mermaid-js/parser": ^0.4.0
    "@types/d3": ^7.4.3
    cytoscape: ^3.29.3
    cytoscape-cose-bilkent: ^4.1.0
    cytoscape-fcose: ^2.2.0
    d3: ^7.9.0
    d3-sankey: ^0.12.3
    dagre-d3-es: 7.0.11
    dayjs: ^1.11.13
    dompurify: ^3.2.4
    katex: ^0.16.9
    khroma: ^2.1.0
    lodash-es: ^4.17.21
    marked: ^15.0.7
    roughjs: ^4.6.6
    stylis: ^4.3.6
    ts-dedent: ^2.2.0
    uuid: ^11.1.0
  checksum: 1796e7c96ed511801f364d301bcda9d3942cd5a6f86be16f087ab1cc5d9988e27f2c5a03db3997bf2d77172d91c37caf1e4a42c95b1a39ce6ebf18836cc5956b
  languageName: node
  linkType: hard

"methods@npm:~1.1.2":
  version: 1.1.2
  resolution: "methods@npm:1.1.2"
  checksum: 0917ff4041fa8e2f2fda5425a955fe16ca411591fbd123c0d722fcf02b73971ed6f764d85f0a6f547ce49ee0221ce2c19a5fa692157931cecb422984f1dcd13a
  languageName: node
  linkType: hard

"mhchemparser@npm:^4.1.0":
  version: 4.1.1
  resolution: "mhchemparser@npm:4.1.1"
  checksum: a030ffa351b234385028498537a93a15f2b5542338678391639632498c98e2c771719f2c3b3645ee6d1338f602f087a87f96e8d85bfe5296b27eb9bdd6664fd9
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.2, micromatch@npm:^4.0.4":
  version: 4.0.5
  resolution: "micromatch@npm:4.0.5"
  dependencies:
    braces: ^3.0.2
    picomatch: ^2.3.1
  checksum: 02a17b671c06e8fefeeb6ef996119c1e597c942e632a21ef589154f23898c9c6a9858526246abb14f8bca6e77734aa9dcf65476fca47cedfb80d9577d52843fc
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0, mime-db@npm:>= 1.43.0 < 2":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 0d99a03585f8b39d68182803b12ac601d9c01abfa28ec56204fa330bc9f3d1c5e14beb049bafadb3dbdf646dfb94b87e24d4ec7b31b7279ef906a8ea9b6a513f
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12, mime-types@npm:^2.1.27, mime-types@npm:~2.1.19, mime-types@npm:~2.1.24, mime-types@npm:~2.1.34":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: 1.52.0
  checksum: 89a5b7f1def9f3af5dad6496c5ed50191ae4331cc5389d7c521c8ad28d5fdad2d06fd81baf38fed813dc4e46bb55c8145bb0ff406330818c9cf712fb2e9b3836
  languageName: node
  linkType: hard

"mime@npm:1.6.0":
  version: 1.6.0
  resolution: "mime@npm:1.6.0"
  bin:
    mime: cli.js
  checksum: fef25e39263e6d207580bdc629f8872a3f9772c923c7f8c7e793175cee22777bbe8bba95e5d509a40aaa292d8974514ce634ae35769faa45f22d17edda5e8557
  languageName: node
  linkType: hard

"mime@npm:2.6.0":
  version: 2.6.0
  resolution: "mime@npm:2.6.0"
  bin:
    mime: cli.js
  checksum: 1497ba7b9f6960694268a557eae24b743fd2923da46ec392b042469f4b901721ba0adcf8b0d3c2677839d0e243b209d76e5edcbd09cfdeffa2dfb6bb4df4b862
  languageName: node
  linkType: hard

"mime@npm:3.0.0":
  version: 3.0.0
  resolution: "mime@npm:3.0.0"
  bin:
    mime: cli.js
  checksum: f43f9b7bfa64534e6b05bd6062961681aeb406a5b53673b53b683f27fcc4e739989941836a355eef831f4478923651ecc739f4a5f6e20a76487b432bfd4db928
  languageName: node
  linkType: hard

"mimic-fn@npm:^2.1.0":
  version: 2.1.0
  resolution: "mimic-fn@npm:2.1.0"
  checksum: d2421a3444848ce7f84bd49115ddacff29c15745db73f54041edc906c14b131a38d05298dae3081667627a59b2eb1ca4b436ff2e1b80f69679522410418b478a
  languageName: node
  linkType: hard

"mimic-response@npm:^1.0.0":
  version: 1.0.1
  resolution: "mimic-response@npm:1.0.1"
  checksum: 034c78753b0e622bc03c983663b1cdf66d03861050e0c8606563d149bc2b02d63f62ce4d32be4ab50d0553ae0ffe647fc34d1f5281184c6e1e8cf4d85e8d9823
  languageName: node
  linkType: hard

"mimic-response@npm:^3.1.0":
  version: 3.1.0
  resolution: "mimic-response@npm:3.1.0"
  checksum: 25739fee32c17f433626bf19f016df9036b75b3d84a3046c7d156e72ec963dd29d7fc8a302f55a3d6c5a4ff24259676b15d915aad6480815a969ff2ec0836867
  languageName: node
  linkType: hard

"mini-css-extract-plugin@npm:^2.7.0":
  version: 2.7.3
  resolution: "mini-css-extract-plugin@npm:2.7.3"
  dependencies:
    schema-utils: ^4.0.0
  peerDependencies:
    webpack: ^5.0.0
  checksum: 9f3a25c1298280d40e56552e5cfd7f84fea25ddd8530bd8ecfd6b08caffe59a1c55c020f1f3c8beb7c04f5e92ce71ed43a52a72deed5570c686316348f13b6f4
  languageName: node
  linkType: hard

"mini-svg-data-uri@npm:^1.4.4":
  version: 1.4.4
  resolution: "mini-svg-data-uri@npm:1.4.4"
  bin:
    mini-svg-data-uri: cli.js
  checksum: 997f1fbd8d59a70f03761e18626d335197a3479cb9d1ff75678e4b64b864d32a0b8fc18115eabde035e5299b8b4a354a78e57dd6ac10f9d604162a6170898d09
  languageName: node
  linkType: hard

"minimatch@npm:2 || 3, minimatch@npm:^3.0.4, minimatch@npm:~3.0.4":
  version: 3.0.5
  resolution: "minimatch@npm:3.0.5"
  dependencies:
    brace-expansion: ^1.1.7
  checksum: a3b84b426eafca947741b864502cee02860c4e7b145de11ad98775cfcf3066fef422583bc0ffce0952ddf4750c1ccf4220b1556430d4ce10139f66247d87d69e
  languageName: node
  linkType: hard

"minimatch@npm:7.4.6":
  version: 7.4.6
  resolution: "minimatch@npm:7.4.6"
  dependencies:
    brace-expansion: ^2.0.1
  checksum: 1a6c8d22618df9d2a88aabeef1de5622eb7b558e9f8010be791cb6b0fa6e102d39b11c28d75b855a1e377b12edc7db8ff12a99c20353441caa6a05e78deb5da9
  languageName: node
  linkType: hard

"minimatch@npm:^5.0.1":
  version: 5.1.6
  resolution: "minimatch@npm:5.1.6"
  dependencies:
    brace-expansion: ^2.0.1
  checksum: 7564208ef81d7065a370f788d337cd80a689e981042cb9a1d0e6580b6c6a8c9279eba80010516e258835a988363f99f54a6f711a315089b8b42694f5da9d0d77
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.1":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: ^2.0.1
  checksum: 2c035575eda1e50623c731ec6c14f65a85296268f749b9337005210bb2b34e2705f8ef1a358b188f69892286ab99dc42c8fb98a57bde55c8d81b3023c19cea28
  languageName: node
  linkType: hard

"minimist@npm:^1.2.0, minimist@npm:^1.2.5, minimist@npm:^1.2.6, minimist@npm:~1.2.0":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 75a6d645fb122dad29c06a7597bddea977258957ed88d7a6df59b5cd3fe4a527e253e9bbf2e783e4b73657f9098b96a5fe96ab8a113655d4109108577ecf85b0
  languageName: node
  linkType: hard

"minipass-collect@npm:^1.0.2":
  version: 1.0.2
  resolution: "minipass-collect@npm:1.0.2"
  dependencies:
    minipass: ^3.0.0
  checksum: 14df761028f3e47293aee72888f2657695ec66bd7d09cae7ad558da30415fdc4752bbfee66287dcc6fd5e6a2fa3466d6c484dc1cbd986525d9393b9523d97f10
  languageName: node
  linkType: hard

"minipass-fetch@npm:^2.0.3":
  version: 2.1.2
  resolution: "minipass-fetch@npm:2.1.2"
  dependencies:
    encoding: ^0.1.13
    minipass: ^3.1.6
    minipass-sized: ^1.0.3
    minizlib: ^2.1.2
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 3f216be79164e915fc91210cea1850e488793c740534985da017a4cbc7a5ff50506956d0f73bb0cb60e4fe91be08b6b61ef35101706d3ef5da2c8709b5f08f91
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: ^3.0.0
  checksum: 56269a0b22bad756a08a94b1ffc36b7c9c5de0735a4dd1ab2b06c066d795cfd1f0ac44a0fcae13eece5589b908ecddc867f04c745c7009be0b566421ea0944cf
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: ^3.0.0
  checksum: b14240dac0d29823c3d5911c286069e36d0b81173d7bdf07a7e4a91ecdef92cdff4baaf31ea3746f1c61e0957f652e641223970870e2353593f382112257971b
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: ^3.0.0
  checksum: 79076749fcacf21b5d16dd596d32c3b6bf4d6e62abb43868fac21674078505c8b15eaca4e47ed844985a4514854f917d78f588fcd029693709417d8f98b2bd60
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0, minipass@npm:^3.1.1, minipass@npm:^3.1.6":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: ^4.0.0
  checksum: a30d083c8054cee83cdcdc97f97e4641a3f58ae743970457b1489ce38ee1167b3aaf7d815cd39ec7a99b9c40397fd4f686e83750e73e652b21cb516f6d845e48
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0":
  version: 5.0.0
  resolution: "minipass@npm:5.0.0"
  checksum: 425dab288738853fded43da3314a0b5c035844d6f3097a8e3b5b29b328da8f3c1af6fc70618b32c29ff906284cf6406b6841376f21caaadd0793c1d5a6a620ea
  languageName: node
  linkType: hard

"minizlib@npm:^2.1.1, minizlib@npm:^2.1.2":
  version: 2.1.2
  resolution: "minizlib@npm:2.1.2"
  dependencies:
    minipass: ^3.0.0
    yallist: ^4.0.0
  checksum: f1fdeac0b07cf8f30fcf12f4b586795b97be856edea22b5e9072707be51fc95d41487faec3f265b42973a304fe3a64acd91a44a3826a963e37b37bafde0212c3
  languageName: node
  linkType: hard

"mj-context-menu@npm:^0.6.1":
  version: 0.6.1
  resolution: "mj-context-menu@npm:0.6.1"
  checksum: 7a036026538662cac9619b760fade98681618c3ddf417cb36eddb7c28a937baf257c56fd0b6318738419e738ba01a00bcb3790b324885fd6edbae03fb0a2c986
  languageName: node
  linkType: hard

"mkdirp@npm:1.0.4, mkdirp@npm:^1.0.3, mkdirp@npm:^1.0.4":
  version: 1.0.4
  resolution: "mkdirp@npm:1.0.4"
  bin:
    mkdirp: bin/cmd.js
  checksum: a96865108c6c3b1b8e1d5e9f11843de1e077e57737602de1b82030815f311be11f96f09cce59bd5b903d0b29834733e5313f9301e3ed6d6f6fba2eae0df4298f
  languageName: node
  linkType: hard

"mkdirp@npm:~0.5.1":
  version: 0.5.6
  resolution: "mkdirp@npm:0.5.6"
  dependencies:
    minimist: ^1.2.6
  bin:
    mkdirp: bin/cmd.js
  checksum: 0c91b721bb12c3f9af4b77ebf73604baf350e64d80df91754dc509491ae93bf238581e59c7188360cec7cb62fc4100959245a42cfe01834efedc5e9d068376c2
  languageName: node
  linkType: hard

"mlly@npm:^1.7.4":
  version: 1.7.4
  resolution: "mlly@npm:1.7.4"
  dependencies:
    acorn: ^8.14.0
    pathe: ^2.0.1
    pkg-types: ^1.3.0
    ufo: ^1.5.4
  checksum: a290da940d208f9d77ceed7ed1db3397e37ff083d28bf75e3c92097a8e58967a2b2e2bea33fdcdc63005e2987854cd081dd0621461d89eee4b61c977b5fa020c
  languageName: node
  linkType: hard

"mrmime@npm:^1.0.0":
  version: 1.0.1
  resolution: "mrmime@npm:1.0.1"
  checksum: cc979da44bbbffebaa8eaf7a45117e851f2d4cb46a3ada6ceb78130466a04c15a0de9a9ce1c8b8ba6f6e1b8618866b1352992bf1757d241c0ddca558b9f28a77
  languageName: node
  linkType: hard

"ms@npm:2.0.0":
  version: 2.0.0
  resolution: "ms@npm:2.0.0"
  checksum: 0e6a22b8b746d2e0b65a430519934fefd41b6db0682e3477c10f60c76e947c4c0ad06f63ffdf1d78d335f83edee8c0aa928aa66a36c7cd95b69b26f468d527f4
  languageName: node
  linkType: hard

"ms@npm:2.1.2":
  version: 2.1.2
  resolution: "ms@npm:2.1.2"
  checksum: 673cdb2c3133eb050c745908d8ce632ed2c02d85640e2edb3ace856a2266a813b30c613569bf3354fdf4ea7d1a1494add3bfa95e2713baa27d0c2c71fc44f58f
  languageName: node
  linkType: hard

"ms@npm:2.1.3, ms@npm:^2.0.0, ms@npm:^2.1.1, ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: aa92de608021b242401676e35cfa5aa42dd70cbdc082b916da7fb925c542173e36bce97ea3e804923fe92c0ad991434e4a38327e15a1b5b5f945d66df615ae6d
  languageName: node
  linkType: hard

"mute-stream@npm:0.0.8":
  version: 0.0.8
  resolution: "mute-stream@npm:0.0.8"
  checksum: ff48d251fc3f827e5b1206cda0ffdaec885e56057ee86a3155e1951bc940fd5f33531774b1cc8414d7668c10a8907f863f6561875ee6e8768931a62121a531a1
  languageName: node
  linkType: hard

"mv@npm:2.1.1":
  version: 2.1.1
  resolution: "mv@npm:2.1.1"
  dependencies:
    mkdirp: ~0.5.1
    ncp: ~2.0.0
    rimraf: ~2.4.0
  checksum: 59d4b5ebff6c265b452d6630ae8873d573c82e36fdc1ed9c34c7901a0bf2d3d357022f49db8e9bded127b743f709c7ef7befec249a2b3967578d649a8029aa06
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.6":
  version: 3.3.8
  resolution: "nanoid@npm:3.3.8"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: dfe0adbc0c77e9655b550c333075f51bb28cfc7568afbf3237249904f9c86c9aaaed1f113f0fddddba75673ee31c758c30c43d4414f014a52a7a626efc5958c9
  languageName: node
  linkType: hard

"ncp@npm:~2.0.0":
  version: 2.0.0
  resolution: "ncp@npm:2.0.0"
  bin:
    ncp: ./bin/ncp
  checksum: ea9b19221da1d1c5529bdb9f8e85c9d191d156bcaae408cce5e415b7fbfd8744c288e792bd7faf1fe3b70fd44c74e22f0d43c39b209bc7ac1fb8016f70793a16
  languageName: node
  linkType: hard

"negotiator@npm:0.6.3":
  version: 0.6.3
  resolution: "negotiator@npm:0.6.3"
  checksum: b8ffeb1e262eff7968fc90a2b6767b04cfd9842582a9d0ece0af7049537266e7b2506dfb1d107a32f06dd849ab2aea834d5830f7f4d0e5cb7d36e1ae55d021d9
  languageName: node
  linkType: hard

"negotiator@npm:^0.6.3, negotiator@npm:~0.6.4":
  version: 0.6.4
  resolution: "negotiator@npm:0.6.4"
  checksum: 7ded10aa02a0707d1d12a9973fdb5954f98547ca7beb60e31cb3a403cc6e8f11138db7a3b0128425cf836fc85d145ec4ce983b2bdf83dca436af879c2d683510
  languageName: node
  linkType: hard

"neo-async@npm:^2.6.2":
  version: 2.6.2
  resolution: "neo-async@npm:2.6.2"
  checksum: deac9f8d00eda7b2e5cd1b2549e26e10a0faa70adaa6fdadca701cc55f49ee9018e427f424bac0c790b7c7e2d3068db97f3093f1093975f2acb8f8818b936ed9
  languageName: node
  linkType: hard

"no-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "no-case@npm:3.0.4"
  dependencies:
    lower-case: ^2.0.2
    tslib: ^2.0.3
  checksum: 0b2ebc113dfcf737d48dde49cfebf3ad2d82a8c3188e7100c6f375e30eafbef9e9124aadc3becef237b042fd5eb0aad2fd78669c20972d045bbe7fea8ba0be5c
  languageName: node
  linkType: hard

"node-fetch@npm:^2.6.7, node-fetch@npm:cjs":
  version: 2.6.7
  resolution: "node-fetch@npm:2.6.7"
  dependencies:
    whatwg-url: ^5.0.0
  peerDependencies:
    encoding: ^0.1.0
  peerDependenciesMeta:
    encoding:
      optional: true
  checksum: 8d816ffd1ee22cab8301c7756ef04f3437f18dace86a1dae22cf81db8ef29c0bf6655f3215cb0cdb22b420b6fe141e64b26905e7f33f9377a7fa59135ea3e10b
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 9.3.1
  resolution: "node-gyp@npm:9.3.1"
  dependencies:
    env-paths: ^2.2.0
    glob: ^7.1.4
    graceful-fs: ^4.2.6
    make-fetch-happen: ^10.0.3
    nopt: ^6.0.0
    npmlog: ^6.0.0
    rimraf: ^3.0.2
    semver: ^7.3.5
    tar: ^6.1.2
    which: ^2.0.2
  bin:
    node-gyp: bin/node-gyp.js
  checksum: b860e9976fa645ca0789c69e25387401b4396b93c8375489b5151a6c55cf2640a3b6183c212b38625ef7c508994930b72198338e3d09b9d7ade5acc4aaf51ea7
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.18":
  version: 2.0.18
  resolution: "node-releases@npm:2.0.18"
  checksum: ef55a3d853e1269a6d6279b7692cd6ff3e40bc74947945101138745bfdc9a5edabfe72cb19a31a8e45752e1910c4c65c77d931866af6357f242b172b7283f5b3
  languageName: node
  linkType: hard

"nopt@npm:^6.0.0":
  version: 6.0.0
  resolution: "nopt@npm:6.0.0"
  dependencies:
    abbrev: ^1.0.0
  bin:
    nopt: bin/nopt.js
  checksum: 82149371f8be0c4b9ec2f863cc6509a7fd0fa729929c009f3a58e4eb0c9e4cae9920e8f1f8eb46e7d032fec8fb01bede7f0f41a67eb3553b7b8e14fa53de1dac
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 88eeb4da891e10b1318c4b2476b6e2ecbeb5ff97d946815ffea7794c31a89017c70d7f34b3c2ebf23ef4e9fc9fb99f7dffe36da22011b5b5c6ffa34f4873ec20
  languageName: node
  linkType: hard

"normalize-url@npm:^6.0.1":
  version: 6.1.0
  resolution: "normalize-url@npm:6.1.0"
  checksum: 4a4944631173e7d521d6b80e4c85ccaeceb2870f315584fa30121f505a6dfd86439c5e3fdd8cd9e0e291290c41d0c3599f0cb12ab356722ed242584c30348e50
  languageName: node
  linkType: hard

"npmlog@npm:^6.0.0":
  version: 6.0.2
  resolution: "npmlog@npm:6.0.2"
  dependencies:
    are-we-there-yet: ^3.0.0
    console-control-strings: ^1.1.0
    gauge: ^4.0.3
    set-blocking: ^2.0.0
  checksum: ae238cd264a1c3f22091cdd9e2b106f684297d3c184f1146984ecbe18aaa86343953f26b9520dedd1b1372bc0316905b736c1932d778dbeb1fcf5a1001390e2a
  languageName: node
  linkType: hard

"nth-check@npm:^2.0.1":
  version: 2.1.1
  resolution: "nth-check@npm:2.1.1"
  dependencies:
    boolbase: ^1.0.0
  checksum: 5afc3dafcd1573b08877ca8e6148c52abd565f1d06b1eb08caf982e3fa289a82f2cae697ffb55b5021e146d60443f1590a5d6b944844e944714a5b549675bcd3
  languageName: node
  linkType: hard

"object-assign@npm:^4, object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: fcc6e4ea8c7fe48abfbb552578b1c53e0d194086e2e6bbbf59e0a536381a292f39943c6e9628af05b5528aa5e3318bb30d6b2e53cadaf5b8fe9e12c4b69af23f
  languageName: node
  linkType: hard

"object-inspect@npm:^1.13.3":
  version: 1.13.4
  resolution: "object-inspect@npm:1.13.4"
  checksum: 582810c6a8d2ef988ea0a39e69e115a138dad8f42dd445383b394877e5816eb4268489f316a6f74ee9c4e0a984b3eab1028e3e79d62b1ed67c726661d55c7a8b
  languageName: node
  linkType: hard

"on-exit-leak-free@npm:^0.2.0":
  version: 0.2.0
  resolution: "on-exit-leak-free@npm:0.2.0"
  checksum: d22b0f0538069110626b578db6e68b6ee0e85b1ee9cc5ef9b4de1bba431431d6a8da91a61e09d2ad46f22a96f968e5237833cb9d0b69bc4d294f7ec82f609b05
  languageName: node
  linkType: hard

"on-exit-leak-free@npm:^2.1.0":
  version: 2.1.2
  resolution: "on-exit-leak-free@npm:2.1.2"
  checksum: 6ce7acdc7b9ceb51cf029b5239cbf41937ee4c8dcd9d4e475e1777b41702564d46caa1150a744e00da0ac6d923ab83471646a39a4470f97481cf6e2d8d253c3f
  languageName: node
  linkType: hard

"on-finished@npm:2.4.1":
  version: 2.4.1
  resolution: "on-finished@npm:2.4.1"
  dependencies:
    ee-first: 1.1.1
  checksum: d20929a25e7f0bb62f937a425b5edeb4e4cde0540d77ba146ec9357f00b0d497cdb3b9b05b9c8e46222407d1548d08166bff69cc56dfa55ba0e4469228920ff0
  languageName: node
  linkType: hard

"on-headers@npm:~1.0.2":
  version: 1.0.2
  resolution: "on-headers@npm:1.0.2"
  checksum: 2bf13467215d1e540a62a75021e8b318a6cfc5d4fc53af8e8f84ad98dbcea02d506c6d24180cd62e1d769c44721ba542f3154effc1f7579a8288c9f7873ed8e5
  languageName: node
  linkType: hard

"once@npm:^1.3.0, once@npm:^1.3.1, once@npm:^1.4.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: 1
  checksum: cd0a88501333edd640d95f0d2700fbde6bff20b3d4d9bdc521bdd31af0656b5706570d6c6afe532045a20bb8dc0849f8332d6f2a416e0ba6d3d3b98806c7db68
  languageName: node
  linkType: hard

"onetime@npm:^5.1.0":
  version: 5.1.2
  resolution: "onetime@npm:5.1.2"
  dependencies:
    mimic-fn: ^2.1.0
  checksum: 2478859ef817fc5d4e9c2f9e5728512ddd1dbc9fb7829ad263765bb6d3b91ce699d6e2332eef6b7dff183c2f490bd3349f1666427eaba4469fba0ac38dfd0d34
  languageName: node
  linkType: hard

"opener@npm:^1.5.2":
  version: 1.5.2
  resolution: "opener@npm:1.5.2"
  bin:
    opener: bin/opener-bin.js
  checksum: 33b620c0d53d5b883f2abc6687dd1c5fd394d270dbe33a6356f2d71e0a2ec85b100d5bac94694198ccf5c30d592da863b2292c5539009c715a9c80c697b4f6cc
  languageName: node
  linkType: hard

"ora@npm:^6.1.2":
  version: 6.1.2
  resolution: "ora@npm:6.1.2"
  dependencies:
    bl: ^5.0.0
    chalk: ^5.0.0
    cli-cursor: ^4.0.0
    cli-spinners: ^2.6.1
    is-interactive: ^2.0.0
    is-unicode-supported: ^1.1.0
    log-symbols: ^5.1.0
    strip-ansi: ^7.0.1
    wcwidth: ^1.0.1
  checksum: d5af3d67ad7affcf3029ffe3ef547f3335fb72abdc382040ae8bd75ad5c629f5d165ed9e398fd4fb08100701eafbec34bb9dc3f29e919f6f75c443290faa1db2
  languageName: node
  linkType: hard

"os-tmpdir@npm:~1.0.2":
  version: 1.0.2
  resolution: "os-tmpdir@npm:1.0.2"
  checksum: 5666560f7b9f10182548bf7013883265be33620b1c1b4a4d405c25be2636f970c5488ff3e6c48de75b55d02bde037249fe5dbfbb4c0fb7714953d56aed062e6d
  languageName: node
  linkType: hard

"os@npm:~0.1.1":
  version: 0.1.2
  resolution: "os@npm:0.1.2"
  checksum: dc2d99759eef13f5dc47ddb12c67b9760a7196fd83a35a7aec2d75b82f91163ca1d4e8872238f8c2a35f4cddd5adf5ce6638a234c0563c748d3cd1d69a9f7153
  languageName: node
  linkType: hard

"p-cancelable@npm:^2.0.0":
  version: 2.1.1
  resolution: "p-cancelable@npm:2.1.1"
  checksum: 3dba12b4fb4a1e3e34524535c7858fc82381bbbd0f247cc32dedc4018592a3950ce66b106d0880b4ec4c2d8d6576f98ca885dc1d7d0f274d1370be20e9523ddf
  languageName: node
  linkType: hard

"p-limit@npm:^2.2.0":
  version: 2.3.0
  resolution: "p-limit@npm:2.3.0"
  dependencies:
    p-try: ^2.0.0
  checksum: 84ff17f1a38126c3314e91ecfe56aecbf36430940e2873dadaa773ffe072dc23b7af8e46d4b6485d302a11673fe94c6b67ca2cfbb60c989848b02100d0594ac1
  languageName: node
  linkType: hard

"p-locate@npm:^4.1.0":
  version: 4.1.0
  resolution: "p-locate@npm:4.1.0"
  dependencies:
    p-limit: ^2.2.0
  checksum: 513bd14a455f5da4ebfcb819ef706c54adb09097703de6aeaa5d26fe5ea16df92b48d1ac45e01e3944ce1e6aa2a66f7f8894742b8c9d6e276e16cd2049a2b870
  languageName: node
  linkType: hard

"p-map@npm:^4.0.0":
  version: 4.0.0
  resolution: "p-map@npm:4.0.0"
  dependencies:
    aggregate-error: ^3.0.0
  checksum: cb0ab21ec0f32ddffd31dfc250e3afa61e103ef43d957cc45497afe37513634589316de4eb88abdfd969fe6410c22c0b93ab24328833b8eb1ccc087fc0442a1c
  languageName: node
  linkType: hard

"p-try@npm:^2.0.0":
  version: 2.2.0
  resolution: "p-try@npm:2.2.0"
  checksum: f8a8e9a7693659383f06aec604ad5ead237c7a261c18048a6e1b5b85a5f8a067e469aa24f5bc009b991ea3b058a87f5065ef4176793a200d4917349881216cae
  languageName: node
  linkType: hard

"package-json@npm:^7.0.0":
  version: 7.0.0
  resolution: "package-json@npm:7.0.0"
  dependencies:
    got: ^11.8.2
    registry-auth-token: ^4.0.0
    registry-url: ^5.0.0
    semver: ^7.3.5
  checksum: c03699b057f665c5bf2d6af76190b8fb32aaf69c70a78faf7c073c03c3d0d5e168d4a96f386739a4d179e925e66289bf0b93a446fbb3f97b05451a3cd2a3bd90
  languageName: node
  linkType: hard

"package-manager-detector@npm:^0.2.8":
  version: 0.2.11
  resolution: "package-manager-detector@npm:0.2.11"
  dependencies:
    quansync: ^0.2.7
  checksum: cea626a294f04028ea291bf0a5a32a21e3914daef4f3959e708ae36f8f2d8097d813e8bb488f5d2b6edaf43a976c6a3d2c361ef8dd9a12c360a7129cd8e29e0f
  languageName: node
  linkType: hard

"pako@npm:~0.2.0":
  version: 0.2.9
  resolution: "pako@npm:0.2.9"
  checksum: 055f9487cd57fbb78df84315873bbdd089ba286f3499daed47d2effdc6253e981f5db6898c23486de76d4a781559f890d643bd3a49f70f1b4a18019c98aa5125
  languageName: node
  linkType: hard

"param-case@npm:^3.0.3, param-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "param-case@npm:3.0.4"
  dependencies:
    dot-case: ^3.0.4
    tslib: ^2.0.3
  checksum: b34227fd0f794e078776eb3aa6247442056cb47761e9cd2c4c881c86d84c64205f6a56ef0d70b41ee7d77da02c3f4ed2f88e3896a8fefe08bdfb4deca037c687
  languageName: node
  linkType: hard

"parse-srcset@npm:^1.0.2":
  version: 1.0.2
  resolution: "parse-srcset@npm:1.0.2"
  checksum: 3a0380380c6082021fcce982f0b89fb8a493ce9dfd7d308e5e6d855201e80db8b90438649b31fdd82a3d6089a8ca17dccddaa2b730a718389af4c037b8539ebf
  languageName: node
  linkType: hard

"parseurl@npm:~1.3.3":
  version: 1.3.3
  resolution: "parseurl@npm:1.3.3"
  checksum: 407cee8e0a3a4c5cd472559bca8b6a45b82c124e9a4703302326e9ab60fc1081442ada4e02628efef1eb16197ddc7f8822f5a91fd7d7c86b51f530aedb17dfa2
  languageName: node
  linkType: hard

"pascal-case@npm:^3.1.2":
  version: 3.1.2
  resolution: "pascal-case@npm:3.1.2"
  dependencies:
    no-case: ^3.0.4
    tslib: ^2.0.3
  checksum: ba98bfd595fc91ef3d30f4243b1aee2f6ec41c53b4546bfa3039487c367abaa182471dcfc830a1f9e1a0df00c14a370514fa2b3a1aacc68b15a460c31116873e
  languageName: node
  linkType: hard

"path-browserify@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-browserify@npm:1.0.1"
  checksum: c6d7fa376423fe35b95b2d67990060c3ee304fc815ff0a2dc1c6c3cfaff2bd0d572ee67e18f19d0ea3bbe32e8add2a05021132ac40509416459fffee35200699
  languageName: node
  linkType: hard

"path-data-parser@npm:0.1.0, path-data-parser@npm:^0.1.0":
  version: 0.1.0
  resolution: "path-data-parser@npm:0.1.0"
  checksum: a23a214adb38074576a8873d25e8dea7e090b8396d86f58f83f3f6c6298ff56b06adc694147b67f0ed22f14dc478efa1d525710d3ec7b2d7b1efbac57e3fafe6
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 505807199dfb7c50737b057dd8d351b82c033029ab94cb10a657609e00c1bc53b951cfdbccab8de04c5584d5eff31128ce6afd3db79281874a5ef2adbba55ed1
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 060840f92cf8effa293bcc1bea81281bd7d363731d214cbe5c227df207c34cd727430f70c6037b5159c8a870b9157cba65e775446b0ab06fd5ecc7e54615a3b8
  languageName: node
  linkType: hard

"path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 55cd7a9dd4b343412a8386a743f9c746ef196e57c823d90ca3ab917f90ab9f13dd0ded27252ba49dbdfcab2b091d998bc446f6220cd3cea65db407502a740020
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 49abf3d81115642938a8700ec580da6e830dde670be21893c62f4e10bd7dd4c3742ddc603fe24f898cba7eb0c6bc1777f8d9ac14185d34540c6d4d80cd9cae8a
  languageName: node
  linkType: hard

"path-scurry@npm:^1.10.1":
  version: 1.10.1
  resolution: "path-scurry@npm:1.10.1"
  dependencies:
    lru-cache: ^9.1.1 || ^10.0.0
    minipass: ^5.0.0 || ^6.0.2 || ^7.0.0
  checksum: e2557cff3a8fb8bc07afdd6ab163a92587884f9969b05bbbaf6fe7379348bfb09af9ed292af12ed32398b15fb443e81692047b786d1eeb6d898a51eb17ed7d90
  languageName: node
  linkType: hard

"path-to-regexp@npm:0.1.10":
  version: 0.1.10
  resolution: "path-to-regexp@npm:0.1.10"
  checksum: ab7a3b7a0b914476d44030340b0a65d69851af2a0f33427df1476100ccb87d409c39e2182837a96b98fb38c4ef2ba6b87bdad62bb70a2c153876b8061760583c
  languageName: node
  linkType: hard

"path-type@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-type@npm:4.0.0"
  checksum: 5b1e2daa247062061325b8fdbfd1fb56dde0a448fb1455453276ea18c60685bdad23a445dc148cf87bc216be1573357509b7d4060494a6fd768c7efad833ee45
  languageName: node
  linkType: hard

"pathe@npm:^2.0.1, pathe@npm:^2.0.3":
  version: 2.0.3
  resolution: "pathe@npm:2.0.3"
  checksum: 0602bdd4acb54d91044e0c56f1fb63467ae7d44ab3afea1f797947b0eb2b4d1d91cf0d58d065fdb0a8ab0c4acbbd8d3a5b424983eaf10dd5285d37a16f6e3ee9
  languageName: node
  linkType: hard

"peek-stream@npm:^1.1.0":
  version: 1.1.3
  resolution: "peek-stream@npm:1.1.3"
  dependencies:
    buffer-from: ^1.0.0
    duplexify: ^3.5.0
    through2: ^2.0.3
  checksum: a0e09d6d1a8a01158a3334f20d6b1cdd91747eba24eb06a1d742eefb620385593121a76d4378cc81f77cdce6a66df0575a41041b1189c510254aec91878afc99
  languageName: node
  linkType: hard

"performance-now@npm:^2.1.0":
  version: 2.1.0
  resolution: "performance-now@npm:2.1.0"
  checksum: 534e641aa8f7cba160f0afec0599b6cecefbb516a2e837b512be0adbe6c1da5550e89c78059c7fabc5c9ffdf6627edabe23eb7c518c4500067a898fa65c2b550
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0, picocolors@npm:^1.0.1":
  version: 1.1.0
  resolution: "picocolors@npm:1.1.0"
  checksum: a64d653d3a188119ff45781dfcdaeedd7625583f45280aea33fcb032c7a0d3959f2368f9b192ad5e8aade75b74dbd954ffe3106c158509a45e4c18ab379a2acd
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 050c865ce81119c4822c45d3c84f1ced46f93a0126febae20737bd05ca20589c564d6e9226977df859ed5e03dc73f02584a2b0faad36e896936238238b0446cf
  languageName: node
  linkType: hard

"pify@npm:^3.0.0":
  version: 3.0.0
  resolution: "pify@npm:3.0.0"
  checksum: 6cdcbc3567d5c412450c53261a3f10991665d660961e06605decf4544a61a97a54fefe70a68d5c37080ff9d6f4cf51444c90198d1ba9f9309a6c0d6e9f5c4fde
  languageName: node
  linkType: hard

"pino-abstract-transport@npm:1.1.0, pino-abstract-transport@npm:v1.1.0":
  version: 1.1.0
  resolution: "pino-abstract-transport@npm:1.1.0"
  dependencies:
    readable-stream: ^4.0.0
    split2: ^4.0.0
  checksum: cc84caabee5647b5753ae484d5f63a1bca0f6e1791845e2db2b6d830a561c2b5dd1177720f68d78994c8a93aecc69f2729e6ac2bc871a1bf5bb4b0ec17210668
  languageName: node
  linkType: hard

"pino-abstract-transport@npm:v0.5.0":
  version: 0.5.0
  resolution: "pino-abstract-transport@npm:0.5.0"
  dependencies:
    duplexify: ^4.1.2
    split2: ^4.0.0
  checksum: c503f867de3189f8217ab9cf794e8a631dddd0029a829f0f985f5511308152ebd53e363764fbc5570b3d1c715b341e3923456ce16ad84cd41be2b9a074ada234
  languageName: node
  linkType: hard

"pino-std-serializers@npm:^4.0.0":
  version: 4.0.0
  resolution: "pino-std-serializers@npm:4.0.0"
  checksum: 89d487729b58c9d3273a0ee851ead068d6d2e2ccc1af8e1c1d28f1b3442423679bec7ec04d9a2aba36f94f335e82be9f4de19dc4fbc161e71c136aaa15b85ad3
  languageName: node
  linkType: hard

"pino-std-serializers@npm:^6.0.0":
  version: 6.2.2
  resolution: "pino-std-serializers@npm:6.2.2"
  checksum: aeb0662edc46ec926de9961ed4780a4f0586bb7c37d212cd469c069639e7816887a62c5093bc93f260a4e0900322f44fc8ab1343b5a9fa2864a888acccdb22a4
  languageName: node
  linkType: hard

"pino@npm:7.11.0":
  version: 7.11.0
  resolution: "pino@npm:7.11.0"
  dependencies:
    atomic-sleep: ^1.0.0
    fast-redact: ^3.0.0
    on-exit-leak-free: ^0.2.0
    pino-abstract-transport: v0.5.0
    pino-std-serializers: ^4.0.0
    process-warning: ^1.0.0
    quick-format-unescaped: ^4.0.3
    real-require: ^0.1.0
    safe-stable-stringify: ^2.1.0
    sonic-boom: ^2.2.1
    thread-stream: ^0.15.1
  bin:
    pino: bin.js
  checksum: b919e7dbe41de978bb050dcef94fd687c012eb78d344a18f75f04ce180d5810fc162be1f136722d70cd005ed05832c4023a38b9acbc1076ae63c9f5ec5ca515c
  languageName: node
  linkType: hard

"pino@npm:8.17.2":
  version: 8.17.2
  resolution: "pino@npm:8.17.2"
  dependencies:
    atomic-sleep: ^1.0.0
    fast-redact: ^3.1.1
    on-exit-leak-free: ^2.1.0
    pino-abstract-transport: v1.1.0
    pino-std-serializers: ^6.0.0
    process-warning: ^3.0.0
    quick-format-unescaped: ^4.0.3
    real-require: ^0.2.0
    safe-stable-stringify: ^2.3.1
    sonic-boom: ^3.7.0
    thread-stream: ^2.0.0
  bin:
    pino: bin.js
  checksum: fc769d3d7b1333de94d51815fbe2abc4a1cc07cb0252a399313e54e26c13da2c0a69b227c296bd95ed52660d7eaa993662a9bf270b7370d0f7553fdd38716b63
  languageName: node
  linkType: hard

"pkg-dir@npm:^4.2.0":
  version: 4.2.0
  resolution: "pkg-dir@npm:4.2.0"
  dependencies:
    find-up: ^4.0.0
  checksum: 9863e3f35132bf99ae1636d31ff1e1e3501251d480336edb1c211133c8d58906bed80f154a1d723652df1fda91e01c7442c2eeaf9dc83157c7ae89087e43c8d6
  languageName: node
  linkType: hard

"pkg-types@npm:^1.3.0":
  version: 1.3.1
  resolution: "pkg-types@npm:1.3.1"
  dependencies:
    confbox: ^0.1.8
    mlly: ^1.7.4
    pathe: ^2.0.1
  checksum: 4fa4edb2bb845646cdbd04c5c6bc43cdbc8f02ed4d1c28bfcafb6e65928aece789bcf1335e4cac5f65dfdc376e4bd7435bd509a35e9ec73ef2c076a1b88e289c
  languageName: node
  linkType: hard

"pkg-types@npm:^2.0.1":
  version: 2.1.0
  resolution: "pkg-types@npm:2.1.0"
  dependencies:
    confbox: ^0.2.1
    exsolve: ^1.0.1
    pathe: ^2.0.3
  checksum: ac0ac8c7d612397276c931888e04e2b5d99bb8169e0c42dce2371b371ad265ce58fbf1f6bdcd44f86fe545b9491983bd79647ac1568beea3f6871ad9dc74692f
  languageName: node
  linkType: hard

"pkginfo@npm:0.4.1":
  version: 0.4.1
  resolution: "pkginfo@npm:0.4.1"
  checksum: 0f13694f3682345647b7cb887fb6fe258df51b635f252324cd75eeb8181b4381cb8b9d91dc2d869849e857192b403bea65038d2f7c05b524eeae69ece5048209
  languageName: node
  linkType: hard

"points-on-curve@npm:0.2.0, points-on-curve@npm:^0.2.0":
  version: 0.2.0
  resolution: "points-on-curve@npm:0.2.0"
  checksum: 05e87d6839e3d869cfac0e63c2b1ca700fc8f1083e3f9ae80841cc50379fd31204f9e1f221407df1a90afcb8bfa98404aee0b0fa00330b7b3b328d33be21cf47
  languageName: node
  linkType: hard

"points-on-path@npm:^0.2.1":
  version: 0.2.1
  resolution: "points-on-path@npm:0.2.1"
  dependencies:
    path-data-parser: 0.1.0
    points-on-curve: 0.2.0
  checksum: 5564dd84d15699579bf07bd33adfd0dc1a5e717c0d36ee11f0832b6b6890941e25e9ea68d15f7858698a9b5ec509f60e6472a0346624bb9dd9c2100cf568ac8f
  languageName: node
  linkType: hard

"postcss-modules-extract-imports@npm:^3.0.0":
  version: 3.0.0
  resolution: "postcss-modules-extract-imports@npm:3.0.0"
  peerDependencies:
    postcss: ^8.1.0
  checksum: 4b65f2f1382d89c4bc3c0a1bdc5942f52f3cb19c110c57bd591ffab3a5fee03fcf831604168205b0c1b631a3dce2255c70b61aaae3ef39d69cd7eb450c2552d2
  languageName: node
  linkType: hard

"postcss-modules-local-by-default@npm:^4.0.0":
  version: 4.0.0
  resolution: "postcss-modules-local-by-default@npm:4.0.0"
  dependencies:
    icss-utils: ^5.0.0
    postcss-selector-parser: ^6.0.2
    postcss-value-parser: ^4.1.0
  peerDependencies:
    postcss: ^8.1.0
  checksum: 6cf570badc7bc26c265e073f3ff9596b69bb954bc6ac9c5c1b8cba2995b80834226b60e0a3cbb87d5f399dbb52e6466bba8aa1d244f6218f99d834aec431a69d
  languageName: node
  linkType: hard

"postcss-modules-scope@npm:^3.0.0":
  version: 3.0.0
  resolution: "postcss-modules-scope@npm:3.0.0"
  dependencies:
    postcss-selector-parser: ^6.0.4
  peerDependencies:
    postcss: ^8.1.0
  checksum: 330b9398dbd44c992c92b0dc612c0626135e2cc840fee41841eb61247a6cfed95af2bd6f67ead9dd9d0bb41f5b0367129d93c6e434fa3e9c58ade391d9a5a138
  languageName: node
  linkType: hard

"postcss-modules-values@npm:^4.0.0":
  version: 4.0.0
  resolution: "postcss-modules-values@npm:4.0.0"
  dependencies:
    icss-utils: ^5.0.0
  peerDependencies:
    postcss: ^8.1.0
  checksum: f7f2cdf14a575b60e919ad5ea52fed48da46fe80db2733318d71d523fc87db66c835814940d7d05b5746b0426e44661c707f09bdb83592c16aea06e859409db6
  languageName: node
  linkType: hard

"postcss-selector-parser@npm:^6.0.2, postcss-selector-parser@npm:^6.0.4":
  version: 6.0.13
  resolution: "postcss-selector-parser@npm:6.0.13"
  dependencies:
    cssesc: ^3.0.0
    util-deprecate: ^1.0.2
  checksum: f89163338a1ce3b8ece8e9055cd5a3165e79a15e1c408e18de5ad8f87796b61ec2d48a2902d179ae0c4b5de10fccd3a325a4e660596549b040bc5ad1b465f096
  languageName: node
  linkType: hard

"postcss-value-parser@npm:^4.1.0, postcss-value-parser@npm:^4.2.0":
  version: 4.2.0
  resolution: "postcss-value-parser@npm:4.2.0"
  checksum: 819ffab0c9d51cf0acbabf8996dffbfafbafa57afc0e4c98db88b67f2094cb44488758f06e5da95d7036f19556a4a732525e84289a425f4f6fd8e412a9d7442f
  languageName: node
  linkType: hard

"postcss@npm:^8.3.11, postcss@npm:^8.4.19":
  version: 8.4.31
  resolution: "postcss@npm:8.4.31"
  dependencies:
    nanoid: ^3.3.6
    picocolors: ^1.0.0
    source-map-js: ^1.0.2
  checksum: 1d8611341b073143ad90486fcdfeab49edd243377b1f51834dc4f6d028e82ce5190e4f11bb2633276864503654fb7cab28e67abdc0fbf9d1f88cad4a0ff0beea
  languageName: node
  linkType: hard

"prettier@npm:~2.6.0":
  version: 2.6.2
  resolution: "prettier@npm:2.6.2"
  bin:
    prettier: bin-prettier.js
  checksum: 48d08dde8e9fb1f5bccdd205baa7f192e9fc8bc98f86e1b97d919de804e28c806b0e6cc685e4a88211aa7987fa9668f30baae19580d87ced3ed0f2ec6572106f
  languageName: node
  linkType: hard

"pretty-error@npm:^4.0.0":
  version: 4.0.0
  resolution: "pretty-error@npm:4.0.0"
  dependencies:
    lodash: ^4.17.20
    renderkid: ^3.0.0
  checksum: a5b9137365690104ded6947dca2e33360bf55e62a4acd91b1b0d7baa3970e43754c628cc9e16eafbdd4e8f8bcb260a5865475d4fc17c3106ff2d61db4e72cdf3
  languageName: node
  linkType: hard

"process-nextick-args@npm:~2.0.0":
  version: 2.0.1
  resolution: "process-nextick-args@npm:2.0.1"
  checksum: 1d38588e520dab7cea67cbbe2efdd86a10cc7a074c09657635e34f035277b59fbb57d09d8638346bf7090f8e8ebc070c96fa5fd183b777fff4f5edff5e9466cf
  languageName: node
  linkType: hard

"process-warning@npm:1.0.0, process-warning@npm:^1.0.0":
  version: 1.0.0
  resolution: "process-warning@npm:1.0.0"
  checksum: c708a03241deec3cabaeee39c4f9ee8c4d71f1c5ef9b746c8252cdb952a6059068cfcdaf348399775244cbc441b6ae5e26a9c87ed371f88335d84f26d19180f9
  languageName: node
  linkType: hard

"process-warning@npm:^3.0.0":
  version: 3.0.0
  resolution: "process-warning@npm:3.0.0"
  checksum: 1fc2eb4524041de3c18423334cc8b4e36bec5ad5472640ca1a936122c6e01da0864c1a4025858ef89aea93eabe7e77db93ccea225b10858617821cb6a8719efe
  languageName: node
  linkType: hard

"process@npm:^0.11.10":
  version: 0.11.10
  resolution: "process@npm:0.11.10"
  checksum: bfcce49814f7d172a6e6a14d5fa3ac92cc3d0c3b9feb1279774708a719e19acd673995226351a082a9ae99978254e320ccda4240ddc474ba31a76c79491ca7c3
  languageName: node
  linkType: hard

"promise-inflight@npm:^1.0.1":
  version: 1.0.1
  resolution: "promise-inflight@npm:1.0.1"
  checksum: 22749483091d2c594261517f4f80e05226d4d5ecc1fc917e1886929da56e22b5718b7f2a75f3807e7a7d471bc3be2907fe92e6e8f373ddf5c64bae35b5af3981
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: ^2.0.2
    retry: ^0.12.0
  checksum: f96a3f6d90b92b568a26f71e966cbbc0f63ab85ea6ff6c81284dc869b41510e6cdef99b6b65f9030f0db422bf7c96652a3fff9f2e8fb4a0f069d8f4430359429
  languageName: node
  linkType: hard

"prop-types@npm:^15.5.8, prop-types@npm:^15.6.1, prop-types@npm:^15.8.1":
  version: 15.8.1
  resolution: "prop-types@npm:15.8.1"
  dependencies:
    loose-envify: ^1.4.0
    object-assign: ^4.1.1
    react-is: ^16.13.1
  checksum: c056d3f1c057cb7ff8344c645450e14f088a915d078dcda795041765047fa080d38e5d626560ccaac94a4e16e3aa15f3557c1a9a8d1174530955e992c675e459
  languageName: node
  linkType: hard

"proxy-addr@npm:~2.0.7":
  version: 2.0.7
  resolution: "proxy-addr@npm:2.0.7"
  dependencies:
    forwarded: 0.2.0
    ipaddr.js: 1.9.1
  checksum: 29c6990ce9364648255454842f06f8c46fcd124d3e6d7c5066df44662de63cdc0bad032e9bf5a3d653ff72141cc7b6019873d685708ac8210c30458ad99f2b74
  languageName: node
  linkType: hard

"pump@npm:^2.0.0":
  version: 2.0.1
  resolution: "pump@npm:2.0.1"
  dependencies:
    end-of-stream: ^1.1.0
    once: ^1.3.1
  checksum: e9f26a17be00810bff37ad0171edb35f58b242487b0444f92fb7d78bc7d61442fa9b9c5bd93a43fd8fd8ddd3cc75f1221f5e04c790f42907e5baab7cf5e2b931
  languageName: node
  linkType: hard

"pump@npm:^3.0.0":
  version: 3.0.0
  resolution: "pump@npm:3.0.0"
  dependencies:
    end-of-stream: ^1.1.0
    once: ^1.3.1
  checksum: e42e9229fba14732593a718b04cb5e1cfef8254544870997e0ecd9732b189a48e1256e4e5478148ecb47c8511dca2b09eae56b4d0aad8009e6fac8072923cfc9
  languageName: node
  linkType: hard

"pumpify@npm:^1.3.3":
  version: 1.5.1
  resolution: "pumpify@npm:1.5.1"
  dependencies:
    duplexify: ^3.6.0
    inherits: ^2.0.3
    pump: ^2.0.0
  checksum: 26ca412ec8d665bd0d5e185c1b8f627728eff603440d75d22a58e421e3c66eaf86ec6fc6a6efc54808ecef65979279fa8e99b109a23ec1fa8d79f37e6978c9bd
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0, punycode@npm:^2.1.1":
  version: 2.3.0
  resolution: "punycode@npm:2.3.0"
  checksum: 39f760e09a2a3bbfe8f5287cf733ecdad69d6af2fe6f97ca95f24b8921858b91e9ea3c9eeec6e08cede96181b3bb33f95c6ffd8c77e63986508aa2e8159fa200
  languageName: node
  linkType: hard

"qs@npm:6.13.0":
  version: 6.13.0
  resolution: "qs@npm:6.13.0"
  dependencies:
    side-channel: ^1.0.6
  checksum: e9404dc0fc2849245107108ce9ec2766cde3be1b271de0bf1021d049dc5b98d1a2901e67b431ac5509f865420a7ed80b7acb3980099fe1c118a1c5d2e1432ad8
  languageName: node
  linkType: hard

"quansync@npm:^0.2.7, quansync@npm:^0.2.8":
  version: 0.2.8
  resolution: "quansync@npm:0.2.8"
  checksum: fddbcbb6c6010beef1ef8999f5cca01905699dbf765a5fb5f8427f66a1e6deffd7cd8cf6f2a5c6492314bc2605eb2d5a86dcc71300d3ae7410b0fa9a852816e9
  languageName: node
  linkType: hard

"querystringify@npm:^2.1.1":
  version: 2.2.0
  resolution: "querystringify@npm:2.2.0"
  checksum: 5641ea231bad7ef6d64d9998faca95611ed4b11c2591a8cae741e178a974f6a8e0ebde008475259abe1621cb15e692404e6b6626e927f7b849d5c09392604b15
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: b676f8c040cdc5b12723ad2f91414d267605b26419d5c821ff03befa817ddd10e238d22b25d604920340fd73efd8ba795465a0377c4adf45a4a41e4234e42dc4
  languageName: node
  linkType: hard

"quick-format-unescaped@npm:^4.0.3":
  version: 4.0.4
  resolution: "quick-format-unescaped@npm:4.0.4"
  checksum: 7bc32b99354a1aa46c089d2a82b63489961002bb1d654cee3e6d2d8778197b68c2d854fd23d8422436ee1fdfd0abaddc4d4da120afe700ade68bd357815b26fd
  languageName: node
  linkType: hard

"quick-lru@npm:^5.1.1":
  version: 5.1.1
  resolution: "quick-lru@npm:5.1.1"
  checksum: a516faa25574be7947969883e6068dbe4aa19e8ef8e8e0fd96cddd6d36485e9106d85c0041a27153286b0770b381328f4072aa40d3b18a19f5f7d2b78b94b5ed
  languageName: node
  linkType: hard

"randombytes@npm:^2.1.0":
  version: 2.1.0
  resolution: "randombytes@npm:2.1.0"
  dependencies:
    safe-buffer: ^5.1.0
  checksum: d779499376bd4cbb435ef3ab9a957006c8682f343f14089ed5f27764e4645114196e75b7f6abf1cbd84fd247c0cb0651698444df8c9bf30e62120fbbc52269d6
  languageName: node
  linkType: hard

"range-parser@npm:~1.2.1":
  version: 1.2.1
  resolution: "range-parser@npm:1.2.1"
  checksum: 0a268d4fea508661cf5743dfe3d5f47ce214fd6b7dec1de0da4d669dd4ef3d2144468ebe4179049eff253d9d27e719c88dae55be64f954e80135a0cada804ec9
  languageName: node
  linkType: hard

"raw-body@npm:2.5.2":
  version: 2.5.2
  resolution: "raw-body@npm:2.5.2"
  dependencies:
    bytes: 3.1.2
    http-errors: 2.0.0
    iconv-lite: 0.4.24
    unpipe: 1.0.0
  checksum: ba1583c8d8a48e8fbb7a873fdbb2df66ea4ff83775421bfe21ee120140949ab048200668c47d9ae3880012f6e217052690628cf679ddfbd82c9fc9358d574676
  languageName: node
  linkType: hard

"rc@npm:1.2.8, rc@npm:^1.2.8":
  version: 1.2.8
  resolution: "rc@npm:1.2.8"
  dependencies:
    deep-extend: ^0.6.0
    ini: ~1.3.0
    minimist: ^1.2.0
    strip-json-comments: ~2.0.1
  bin:
    rc: ./cli.js
  checksum: 2e26e052f8be2abd64e6d1dabfbd7be03f80ec18ccbc49562d31f617d0015fbdbcf0f9eed30346ea6ab789e0fdfe4337f033f8016efdbee0df5354751842080e
  languageName: node
  linkType: hard

"react-base16-styling@npm:^0.9.1":
  version: 0.9.1
  resolution: "react-base16-styling@npm:0.9.1"
  dependencies:
    "@babel/runtime": ^7.16.7
    "@types/base16": ^1.0.2
    "@types/lodash": ^4.14.178
    base16: ^1.0.0
    color: ^3.2.1
    csstype: ^3.0.10
    lodash.curry: ^4.1.1
  checksum: 1e61e1158ee5250ad68860840368f9228685680df15385c0fc4d5c63dd0925f27f4f1d1762134de623fe005e75ef9543191aa648cde2c16d0153341d00ceeecb
  languageName: node
  linkType: hard

"react-dom@npm:^18.2.0":
  version: 18.2.0
  resolution: "react-dom@npm:18.2.0"
  dependencies:
    loose-envify: ^1.1.0
    scheduler: ^0.23.0
  peerDependencies:
    react: ^18.2.0
  checksum: 7d323310bea3a91be2965f9468d552f201b1c27891e45ddc2d6b8f717680c95a75ae0bc1e3f5cf41472446a2589a75aed4483aee8169287909fcd59ad149e8cc
  languageName: node
  linkType: hard

"react-highlight-words@npm:^0.20.0":
  version: 0.20.0
  resolution: "react-highlight-words@npm:0.20.0"
  dependencies:
    highlight-words-core: ^1.2.0
    memoize-one: ^4.0.0
    prop-types: ^15.5.8
  peerDependencies:
    react: ^0.14.0 || ^15.0.0 || ^16.0.0-0 || ^17.0.0-0 || ^18.0.0-0
  checksum: 6794b6fe409ee81390e342ccdb951696e06354d8591b4cac050a6d64dbc77dfc7bb636fee0aabcfda841e57778aa5108fe351e7c1dc27b28abedd36aec8141e7
  languageName: node
  linkType: hard

"react-is@npm:^16.13.1":
  version: 16.13.1
  resolution: "react-is@npm:16.13.1"
  checksum: f7a19ac3496de32ca9ae12aa030f00f14a3d45374f1ceca0af707c831b2a6098ef0d6bdae51bd437b0a306d7f01d4677fcc8de7c0d331eb47ad0f46130e53c5f
  languageName: node
  linkType: hard

"react-is@npm:^18.2.0":
  version: 18.2.0
  resolution: "react-is@npm:18.2.0"
  checksum: e72d0ba81b5922759e4aff17e0252bd29988f9642ed817f56b25a3e217e13eea8a7f2322af99a06edb779da12d5d636e9fda473d620df9a3da0df2a74141d53e
  languageName: node
  linkType: hard

"react-json-tree@npm:^0.18.0":
  version: 0.18.0
  resolution: "react-json-tree@npm:0.18.0"
  dependencies:
    "@babel/runtime": ^7.20.6
    "@types/lodash": ^4.14.191
    react-base16-styling: ^0.9.1
  peerDependencies:
    "@types/react": ^16.8.0 || ^17.0.0 || ^18.0.0
    react: ^16.8.0 || ^17.0.0 || ^18.0.0
  checksum: e59244b1f7866a3fec7b5fe83d68833583568e9ae217c261b09077de196a51cc96642e8b1d6826d963aaf910a496e1cf432240ee142ce7efa965345915bb57ac
  languageName: node
  linkType: hard

"react-paginate@npm:^6.3.2":
  version: 6.5.0
  resolution: "react-paginate@npm:6.5.0"
  dependencies:
    prop-types: ^15.6.1
  peerDependencies:
    react: ^16.0.0
  checksum: 39397de9a330e9ed16b2422d5893f47e16b5f7f188bf59ce4f60beeb16cfdba0ba76c7f0ca1b733d7282821a4e984ae64fb1a7086ecc22b416af95c1ff974fec
  languageName: node
  linkType: hard

"react-toastify@npm:^9.0.8":
  version: 9.1.1
  resolution: "react-toastify@npm:9.1.1"
  dependencies:
    clsx: ^1.1.1
  peerDependencies:
    react: ">=16"
    react-dom: ">=16"
  checksum: 2039255539961a9b4d77b2656f120b20abe46cb0c699a7f3c0af23b4ef669d9c4d24dae6b8f4954b5efd83edf6d6e23614a29e94e9ee0d2647741fba9ba2db85
  languageName: node
  linkType: hard

"react@npm:^18.2.0":
  version: 18.2.0
  resolution: "react@npm:18.2.0"
  dependencies:
    loose-envify: ^1.1.0
  checksum: 88e38092da8839b830cda6feef2e8505dec8ace60579e46aa5490fc3dc9bba0bd50336507dc166f43e3afc1c42939c09fe33b25fae889d6f402721dcd78fca1b
  languageName: node
  linkType: hard

"readable-stream@npm:^2.0.0, readable-stream@npm:~2.3.6":
  version: 2.3.8
  resolution: "readable-stream@npm:2.3.8"
  dependencies:
    core-util-is: ~1.0.0
    inherits: ~2.0.3
    isarray: ~1.0.0
    process-nextick-args: ~2.0.0
    safe-buffer: ~5.1.1
    string_decoder: ~1.1.1
    util-deprecate: ~1.0.1
  checksum: 65645467038704f0c8aaf026a72fbb588a9e2ef7a75cd57a01702ee9db1c4a1e4b03aaad36861a6a0926546a74d174149c8c207527963e0c2d3eee2f37678a42
  languageName: node
  linkType: hard

"readable-stream@npm:^3.1.1, readable-stream@npm:^3.4.0, readable-stream@npm:^3.6.0":
  version: 3.6.2
  resolution: "readable-stream@npm:3.6.2"
  dependencies:
    inherits: ^2.0.3
    string_decoder: ^1.1.1
    util-deprecate: ^1.0.1
  checksum: bdcbe6c22e846b6af075e32cf8f4751c2576238c5043169a1c221c92ee2878458a816a4ea33f4c67623c0b6827c8a400409bfb3cf0bf3381392d0b1dfb52ac8d
  languageName: node
  linkType: hard

"readable-stream@npm:^4.0.0":
  version: 4.3.0
  resolution: "readable-stream@npm:4.3.0"
  dependencies:
    abort-controller: ^3.0.0
    buffer: ^6.0.3
    events: ^3.3.0
    process: ^0.11.10
  checksum: 5f8d5fc1eb0c6eb47771ad4537881126d6280666e1f10ba1e2262a670a0352c36f59e6a04d17c9a6f7c888218984836dc67f55e95a77de8bfdf06fb75f00f670
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: ^2.2.1
  checksum: 1ced032e6e45670b6d7352d71d21ce7edf7b9b928494dcaba6f11fba63180d9da6cd7061ebc34175ffda6ff529f481818c962952004d273178acd70f7059b320
  languageName: node
  linkType: hard

"real-require@npm:^0.1.0":
  version: 0.1.0
  resolution: "real-require@npm:0.1.0"
  checksum: 96745583ed4f82cd5c6a6af012fd1d3c6fc2f13ae1bcff1a3c4f8094696013a1a07c82c5aa66a403d7d4f84949fc2203bc927c7ad120caad125941ca2d7e5e8e
  languageName: node
  linkType: hard

"real-require@npm:^0.2.0":
  version: 0.2.0
  resolution: "real-require@npm:0.2.0"
  checksum: fa060f19f2f447adf678d1376928c76379dce5f72bd334da301685ca6cdcb7b11356813332cc243c88470796bc2e2b1e2917fc10df9143dd93c2ea608694971d
  languageName: node
  linkType: hard

"rechoir@npm:^0.8.0":
  version: 0.8.0
  resolution: "rechoir@npm:0.8.0"
  dependencies:
    resolve: ^1.20.0
  checksum: ad3caed8afdefbc33fbc30e6d22b86c35b3d51c2005546f4e79bcc03c074df804b3640ad18945e6bef9ed12caedc035655ec1082f64a5e94c849ff939dc0a788
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.14.0":
  version: 0.14.1
  resolution: "regenerator-runtime@npm:0.14.1"
  checksum: 9f57c93277b5585d3c83b0cf76be47b473ae8c6d9142a46ce8b0291a04bb2cf902059f0f8445dcabb3fb7378e5fe4bb4ea1e008876343d42e46d3b484534ce38
  languageName: node
  linkType: hard

"regexp-match-indices@npm:^1.0.2":
  version: 1.0.2
  resolution: "regexp-match-indices@npm:1.0.2"
  dependencies:
    regexp-tree: ^0.1.11
  checksum: 8cc779f6cf8f404ead828d09970a7d4bd66bd78d43ab9eb2b5e65f2ef2ba1ed53536f5b5fa839fb90b350365fb44b6a851c7f16289afc3f37789c113ab2a7916
  languageName: node
  linkType: hard

"regexp-tree@npm:^0.1.11":
  version: 0.1.24
  resolution: "regexp-tree@npm:0.1.24"
  bin:
    regexp-tree: bin/regexp-tree
  checksum: 5807013289d9205288d665e0f8d8cff94843dfd55fdedd1833eb9d9bbd07188a37dfa02942ec5cdc671180037f715148fac1ba6f18fd6be4268e5a8feb49d340
  languageName: node
  linkType: hard

"registry-auth-token@npm:^4.0.0":
  version: 4.2.2
  resolution: "registry-auth-token@npm:4.2.2"
  dependencies:
    rc: 1.2.8
  checksum: c5030198546ecfdcbcb0722cbc3e260c4f5f174d8d07bdfedd4620e79bfdf17a2db735aa230d600bd388fce6edd26c0a9ed2eb7e9b4641ec15213a28a806688b
  languageName: node
  linkType: hard

"registry-url@npm:^5.0.0":
  version: 5.1.0
  resolution: "registry-url@npm:5.1.0"
  dependencies:
    rc: ^1.2.8
  checksum: bcea86c84a0dbb66467b53187fadebfea79017cddfb4a45cf27530d7275e49082fe9f44301976eb0164c438e395684bcf3dae4819b36ff9d1640d8cc60c73df9
  languageName: node
  linkType: hard

"relateurl@npm:^0.2.7":
  version: 0.2.7
  resolution: "relateurl@npm:0.2.7"
  checksum: 5891e792eae1dfc3da91c6fda76d6c3de0333a60aa5ad848982ebb6dccaa06e86385fb1235a1582c680a3d445d31be01c6bfc0804ebbcab5aaf53fa856fde6b6
  languageName: node
  linkType: hard

"renderkid@npm:^3.0.0":
  version: 3.0.0
  resolution: "renderkid@npm:3.0.0"
  dependencies:
    css-select: ^4.1.3
    dom-converter: ^0.2.0
    htmlparser2: ^6.1.0
    lodash: ^4.17.21
    strip-ansi: ^6.0.1
  checksum: 77162b62d6f33ab81f337c39efce0439ff0d1f6d441e29c35183151f83041c7850774fb904da163d6c844264d440d10557714e6daa0b19e4561a5cd4ef305d41
  languageName: node
  linkType: hard

"require-directory@npm:^2.1.1":
  version: 2.1.1
  resolution: "require-directory@npm:2.1.1"
  checksum: fb47e70bf0001fdeabdc0429d431863e9475e7e43ea5f94ad86503d918423c1543361cc5166d713eaa7029dd7a3d34775af04764bebff99ef413111a5af18c80
  languageName: node
  linkType: hard

"require-from-string@npm:^2.0.2":
  version: 2.0.2
  resolution: "require-from-string@npm:2.0.2"
  checksum: a03ef6895445f33a4015300c426699bc66b2b044ba7b670aa238610381b56d3f07c686251740d575e22f4c87531ba662d06937508f0f3c0f1ddc04db3130560b
  languageName: node
  linkType: hard

"requires-port@npm:^1.0.0":
  version: 1.0.0
  resolution: "requires-port@npm:1.0.0"
  checksum: eee0e303adffb69be55d1a214e415cf42b7441ae858c76dfc5353148644f6fd6e698926fc4643f510d5c126d12a705e7c8ed7e38061113bdf37547ab356797ff
  languageName: node
  linkType: hard

"resolve-alpn@npm:^1.0.0":
  version: 1.2.1
  resolution: "resolve-alpn@npm:1.2.1"
  checksum: f558071fcb2c60b04054c99aebd572a2af97ef64128d59bef7ab73bd50d896a222a056de40ffc545b633d99b304c259ea9d0c06830d5c867c34f0bfa60b8eae0
  languageName: node
  linkType: hard

"resolve-cwd@npm:^3.0.0":
  version: 3.0.0
  resolution: "resolve-cwd@npm:3.0.0"
  dependencies:
    resolve-from: ^5.0.0
  checksum: 546e0816012d65778e580ad62b29e975a642989108d9a3c5beabfb2304192fa3c9f9146fbdfe213563c6ff51975ae41bac1d3c6e047dd9572c94863a057b4d81
  languageName: node
  linkType: hard

"resolve-from@npm:^5.0.0":
  version: 5.0.0
  resolution: "resolve-from@npm:5.0.0"
  checksum: 4ceeb9113e1b1372d0cd969f3468fa042daa1dd9527b1b6bb88acb6ab55d8b9cd65dbf18819f9f9ddf0db804990901dcdaade80a215e7b2c23daae38e64f5bdf
  languageName: node
  linkType: hard

"resolve@npm:^1.20.0":
  version: 1.22.1
  resolution: "resolve@npm:1.22.1"
  dependencies:
    is-core-module: ^2.9.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: 07af5fc1e81aa1d866cbc9e9460fbb67318a10fa3c4deadc35c3ad8a898ee9a71a86a65e4755ac3195e0ea0cfbe201eb323ebe655ce90526fd61917313a34e4e
  languageName: node
  linkType: hard

"resolve@patch:resolve@^1.20.0#~builtin<compat/resolve>":
  version: 1.22.1
  resolution: "resolve@patch:resolve@npm%3A1.22.1#~builtin<compat/resolve>::version=1.22.1&hash=c3c19d"
  dependencies:
    is-core-module: ^2.9.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: 5656f4d0bedcf8eb52685c1abdf8fbe73a1603bb1160a24d716e27a57f6cecbe2432ff9c89c2bd57542c3a7b9d14b1882b73bfe2e9d7849c9a4c0b8b39f02b8b
  languageName: node
  linkType: hard

"responselike@npm:^2.0.0":
  version: 2.0.1
  resolution: "responselike@npm:2.0.1"
  dependencies:
    lowercase-keys: ^2.0.0
  checksum: b122535466e9c97b55e69c7f18e2be0ce3823c5d47ee8de0d9c0b114aa55741c6db8bfbfce3766a94d1272e61bfb1ebf0a15e9310ac5629fbb7446a861b4fd3a
  languageName: node
  linkType: hard

"restore-cursor@npm:^4.0.0":
  version: 4.0.0
  resolution: "restore-cursor@npm:4.0.0"
  dependencies:
    onetime: ^5.1.0
    signal-exit: ^3.0.2
  checksum: 5b675c5a59763bf26e604289eab35711525f11388d77f409453904e1e69c0d37ae5889295706b2c81d23bd780165084d040f9b68fffc32cc921519031c4fa4af
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 623bd7d2e5119467ba66202d733ec3c2e2e26568074923bc0585b6b99db14f357e79bdedb63cab56cec47491c4a0da7e6021a7465ca6dc4f481d3898fdd3158c
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.0.4
  resolution: "reusify@npm:1.0.4"
  checksum: c3076ebcc22a6bc252cb0b9c77561795256c22b757f40c0d8110b1300723f15ec0fc8685e8d4ea6d7666f36c79ccc793b1939c748bf36f18f542744a4e379fcc
  languageName: node
  linkType: hard

"rimraf@npm:^3.0.2":
  version: 3.0.2
  resolution: "rimraf@npm:3.0.2"
  dependencies:
    glob: ^7.1.3
  bin:
    rimraf: bin.js
  checksum: 87f4164e396f0171b0a3386cc1877a817f572148ee13a7e113b238e48e8a9f2f31d009a92ec38a591ff1567d9662c6b67fd8818a2dbbaed74bc26a87a2a4a9a0
  languageName: node
  linkType: hard

"rimraf@npm:~2.4.0":
  version: 2.4.5
  resolution: "rimraf@npm:2.4.5"
  dependencies:
    glob: ^6.0.1
  bin:
    rimraf: ./bin.js
  checksum: 036793b4055d65344ad7bea73c3f4095640af7455478fe56c19783619463e6bb4374ab3556b9e6d4d6d3dd210eb677b0955ece38813e734c294fd2687201151d
  languageName: node
  linkType: hard

"rimraf@npm:~5.0.5":
  version: 5.0.5
  resolution: "rimraf@npm:5.0.5"
  dependencies:
    glob: ^10.3.7
  bin:
    rimraf: dist/esm/bin.mjs
  checksum: d66eef829b2e23b16445f34e73d75c7b7cf4cbc8834b04720def1c8f298eb0753c3d76df77325fad79d0a2c60470525d95f89c2475283ad985fd7441c32732d1
  languageName: node
  linkType: hard

"robust-predicates@npm:^3.0.0":
  version: 3.0.1
  resolution: "robust-predicates@npm:3.0.1"
  checksum: 45e9de2df4380da84a2a561d4fd54ea92194e878b93ed19d5e4bc90f4e834a13755e846c8516bab8360190309696f0564a0150386c52ef01f70f2b388449dac5
  languageName: node
  linkType: hard

"roughjs@npm:^4.6.6":
  version: 4.6.6
  resolution: "roughjs@npm:4.6.6"
  dependencies:
    hachure-fill: ^0.5.2
    path-data-parser: ^0.1.0
    points-on-curve: ^0.2.0
    points-on-path: ^0.2.1
  checksum: ec4b8266ac4a50c7369e337d8ddff3b2d970506229cac5425ddca56f4e6b29fca07dded4300e9e392bb608da4ba618d349fd241283affb25055cab7c2fe48f8f
  languageName: node
  linkType: hard

"run-async@npm:^2.4.0":
  version: 2.4.1
  resolution: "run-async@npm:2.4.1"
  checksum: a2c88aa15df176f091a2878eb840e68d0bdee319d8d97bbb89112223259cebecb94bc0defd735662b83c2f7a30bed8cddb7d1674eb48ae7322dc602b22d03797
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: ^1.2.2
  checksum: cb4f97ad25a75ebc11a8ef4e33bb962f8af8516bb2001082ceabd8902e15b98f4b84b4f8a9b222e5d57fc3bd1379c483886ed4619367a7680dad65316993021d
  languageName: node
  linkType: hard

"rw@npm:1":
  version: 1.3.3
  resolution: "rw@npm:1.3.3"
  checksum: c20d82421f5a71c86a13f76121b751553a99cd4a70ea27db86f9b23f33db941f3f06019c30f60d50c356d0bd674c8e74764ac146ea55e217c091bde6fba82aa3
  languageName: node
  linkType: hard

"rxjs@npm:^7.5.7":
  version: 7.8.0
  resolution: "rxjs@npm:7.8.0"
  dependencies:
    tslib: ^2.1.0
  checksum: 61b4d4fd323c1043d8d6ceb91f24183b28bcf5def4f01ca111511d5c6b66755bc5578587fe714ef5d67cf4c9f2e26f4490d4e1d8cabf9bd5967687835e9866a2
  languageName: node
  linkType: hard

"safe-buffer@npm:5.2.1, safe-buffer@npm:^5.0.1, safe-buffer@npm:^5.1.0, safe-buffer@npm:^5.1.2":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: b99c4b41fdd67a6aaf280fcd05e9ffb0813654894223afb78a31f14a19ad220bba8aba1cb14eddce1fcfb037155fe6de4e861784eb434f7d11ed58d1e70dd491
  languageName: node
  linkType: hard

"safe-buffer@npm:~5.1.0, safe-buffer@npm:~5.1.1":
  version: 5.1.2
  resolution: "safe-buffer@npm:5.1.2"
  checksum: f2f1f7943ca44a594893a852894055cf619c1fbcb611237fc39e461ae751187e7baf4dc391a72125e0ac4fb2d8c5c0b3c71529622e6a58f46b960211e704903c
  languageName: node
  linkType: hard

"safe-stable-stringify@npm:^2.1.0, safe-stable-stringify@npm:^2.3.1":
  version: 2.5.0
  resolution: "safe-stable-stringify@npm:2.5.0"
  checksum: d3ce103ed43c6c2f523e39607208bfb1c73aa48179fc5be53c3aa97c118390bffd4d55e012f5393b982b65eb3e0ee954dd57b547930d3f242b0053dcdb923d17
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3, safer-buffer@npm:>= 2.1.2 < 3.0.0, safer-buffer@npm:^2.0.2, safer-buffer@npm:^2.1.0, safer-buffer@npm:~2.1.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: cab8f25ae6f1434abee8d80023d7e72b598cf1327164ddab31003c51215526801e40b66c5e65d658a0af1e9d6478cadcb4c745f4bd6751f97d8644786c0978b0
  languageName: node
  linkType: hard

"sanitize-html@npm:~2.12.1":
  version: 2.12.1
  resolution: "sanitize-html@npm:2.12.1"
  dependencies:
    deepmerge: ^4.2.2
    escape-string-regexp: ^4.0.0
    htmlparser2: ^8.0.0
    is-plain-object: ^5.0.0
    parse-srcset: ^1.0.2
    postcss: ^8.3.11
  checksum: fb96ea7170d51b5af2607f5cfd84464c78fc6f47e339407f55783e781c6a0288a8d40bbf97ea6a8758924ba9b2d33dcc4846bb94caacacd90d7f2de10ed8541a
  languageName: node
  linkType: hard

"scheduler@npm:^0.23.0":
  version: 0.23.0
  resolution: "scheduler@npm:0.23.0"
  dependencies:
    loose-envify: ^1.1.0
  checksum: d79192eeaa12abef860c195ea45d37cbf2bbf5f66e3c4dcd16f54a7da53b17788a70d109ee3d3dde1a0fd50e6a8fc171f4300356c5aee4fc0171de526bf35f8a
  languageName: node
  linkType: hard

"schema-utils@npm:^2.7.0":
  version: 2.7.1
  resolution: "schema-utils@npm:2.7.1"
  dependencies:
    "@types/json-schema": ^7.0.5
    ajv: ^6.12.4
    ajv-keywords: ^3.5.2
  checksum: 32c62fc9e28edd101e1bd83453a4216eb9bd875cc4d3775e4452b541908fa8f61a7bbac8ffde57484f01d7096279d3ba0337078e85a918ecbeb72872fb09fb2b
  languageName: node
  linkType: hard

"schema-utils@npm:^3.0.0, schema-utils@npm:^3.1.1, schema-utils@npm:^3.2.0":
  version: 3.3.0
  resolution: "schema-utils@npm:3.3.0"
  dependencies:
    "@types/json-schema": ^7.0.8
    ajv: ^6.12.5
    ajv-keywords: ^3.5.2
  checksum: ea56971926fac2487f0757da939a871388891bc87c6a82220d125d587b388f1704788f3706e7f63a7b70e49fc2db974c41343528caea60444afd5ce0fe4b85c0
  languageName: node
  linkType: hard

"schema-utils@npm:^4.0.0":
  version: 4.0.0
  resolution: "schema-utils@npm:4.0.0"
  dependencies:
    "@types/json-schema": ^7.0.9
    ajv: ^8.8.0
    ajv-formats: ^2.1.1
    ajv-keywords: ^5.0.0
  checksum: c843e92fdd1a5c145dbb6ffdae33e501867f9703afac67bdf35a685e49f85b1dcc10ea250033175a64bd9d31f0555bc6785b8359da0c90bcea30cf6dfbb55a8f
  languageName: node
  linkType: hard

"semver@npm:7.6.3, semver@npm:^7.1.2, semver@npm:^7.3.5, semver@npm:^7.3.8, semver@npm:^7.5.2, semver@npm:^7.5.4":
  version: 7.6.3
  resolution: "semver@npm:7.6.3"
  bin:
    semver: bin/semver.js
  checksum: 4110ec5d015c9438f322257b1c51fe30276e5f766a3f64c09edd1d7ea7118ecbc3f379f3b69032bacf13116dc7abc4ad8ce0d7e2bd642e26b0d271b56b61a7d8
  languageName: node
  linkType: hard

"semver@npm:^5.4.1":
  version: 5.7.2
  resolution: "semver@npm:5.7.2"
  bin:
    semver: bin/semver
  checksum: fb4ab5e0dd1c22ce0c937ea390b4a822147a9c53dbd2a9a0132f12fe382902beef4fbf12cf51bb955248d8d15874ce8cd89532569756384f994309825f10b686
  languageName: node
  linkType: hard

"send@npm:0.19.0":
  version: 0.19.0
  resolution: "send@npm:0.19.0"
  dependencies:
    debug: 2.6.9
    depd: 2.0.0
    destroy: 1.2.0
    encodeurl: ~1.0.2
    escape-html: ~1.0.3
    etag: ~1.8.1
    fresh: 0.5.2
    http-errors: 2.0.0
    mime: 1.6.0
    ms: 2.1.3
    on-finished: 2.4.1
    range-parser: ~1.2.1
    statuses: 2.0.1
  checksum: 5ae11bd900c1c2575525e2aa622e856804e2f96a09281ec1e39610d089f53aa69e13fd8db84b52f001d0318cf4bb0b3b904ad532fc4c0014eb90d32db0cff55f
  languageName: node
  linkType: hard

"serialize-javascript@npm:^6.0.1":
  version: 6.0.1
  resolution: "serialize-javascript@npm:6.0.1"
  dependencies:
    randombytes: ^2.1.0
  checksum: 3c4f4cb61d0893b988415bdb67243637333f3f574e9e9cc9a006a2ced0b390b0b3b44aef8d51c951272a9002ec50885eefdc0298891bc27eb2fe7510ea87dc4f
  languageName: node
  linkType: hard

"serve-static@npm:1.16.2":
  version: 1.16.2
  resolution: "serve-static@npm:1.16.2"
  dependencies:
    encodeurl: ~2.0.0
    escape-html: ~1.0.3
    parseurl: ~1.3.3
    send: 0.19.0
  checksum: dffc52feb4cc5c68e66d0c7f3c1824d4e989f71050aefc9bd5f822a42c54c9b814f595fc5f2b717f4c7cc05396145f3e90422af31186a93f76cf15f707019759
  languageName: node
  linkType: hard

"set-blocking@npm:^2.0.0":
  version: 2.0.0
  resolution: "set-blocking@npm:2.0.0"
  checksum: 6e65a05f7cf7ebdf8b7c75b101e18c0b7e3dff4940d480efed8aad3a36a4005140b660fa1d804cb8bce911cac290441dc728084a30504d3516ac2ff7ad607b02
  languageName: node
  linkType: hard

"setprototypeof@npm:1.2.0":
  version: 1.2.0
  resolution: "setprototypeof@npm:1.2.0"
  checksum: be18cbbf70e7d8097c97f713a2e76edf84e87299b40d085c6bf8b65314e994cc15e2e317727342fa6996e38e1f52c59720b53fe621e2eb593a6847bf0356db89
  languageName: node
  linkType: hard

"shallow-clone@npm:^3.0.0":
  version: 3.0.1
  resolution: "shallow-clone@npm:3.0.1"
  dependencies:
    kind-of: ^6.0.2
  checksum: 39b3dd9630a774aba288a680e7d2901f5c0eae7b8387fc5c8ea559918b29b3da144b7bdb990d7ccd9e11be05508ac9e459ce51d01fd65e583282f6ffafcba2e7
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: ^3.0.0
  checksum: 6b52fe87271c12968f6a054e60f6bde5f0f3d2db483a1e5c3e12d657c488a15474121a1d55cd958f6df026a54374ec38a4a963988c213b7570e1d51575cea7fa
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 1a2bcae50de99034fcd92ad4212d8e01eedf52c7ec7830eedcf886622804fe36884278f2be8be0ea5fde3fd1c23911643a4e0f726c8685b61871c8908af01222
  languageName: node
  linkType: hard

"side-channel-list@npm:^1.0.0":
  version: 1.0.0
  resolution: "side-channel-list@npm:1.0.0"
  dependencies:
    es-errors: ^1.3.0
    object-inspect: ^1.13.3
  checksum: 603b928997abd21c5a5f02ae6b9cc36b72e3176ad6827fab0417ead74580cc4fb4d5c7d0a8a2ff4ead34d0f9e35701ed7a41853dac8a6d1a664fcce1a044f86f
  languageName: node
  linkType: hard

"side-channel-map@npm:^1.0.1":
  version: 1.0.1
  resolution: "side-channel-map@npm:1.0.1"
  dependencies:
    call-bound: ^1.0.2
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.5
    object-inspect: ^1.13.3
  checksum: 42501371cdf71f4ccbbc9c9e2eb00aaaab80a4c1c429d5e8da713fd4d39ef3b8d4a4b37ed4f275798a65260a551a7131fd87fe67e922dba4ac18586d6aab8b06
  languageName: node
  linkType: hard

"side-channel-weakmap@npm:^1.0.2":
  version: 1.0.2
  resolution: "side-channel-weakmap@npm:1.0.2"
  dependencies:
    call-bound: ^1.0.2
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.5
    object-inspect: ^1.13.3
    side-channel-map: ^1.0.1
  checksum: a815c89bc78c5723c714ea1a77c938377ea710af20d4fb886d362b0d1f8ac73a17816a5f6640f354017d7e292a43da9c5e876c22145bac00b76cfb3468001736
  languageName: node
  linkType: hard

"side-channel@npm:^1.0.6":
  version: 1.1.0
  resolution: "side-channel@npm:1.1.0"
  dependencies:
    es-errors: ^1.3.0
    object-inspect: ^1.13.3
    side-channel-list: ^1.0.0
    side-channel-map: ^1.0.1
    side-channel-weakmap: ^1.0.2
  checksum: bf73d6d6682034603eb8e99c63b50155017ed78a522d27c2acec0388a792c3ede3238b878b953a08157093b85d05797217d270b7666ba1f111345fbe933380ff
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.2, signal-exit@npm:^3.0.7":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: a2f098f247adc367dffc27845853e9959b9e88b01cb301658cfe4194352d8d2bb32e18467c786a7fe15f1d44b233ea35633d076d5e737870b7139949d1ab6318
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.0.2
  resolution: "signal-exit@npm:4.0.2"
  checksum: 41f5928431cc6e91087bf0343db786a6313dd7c6fd7e551dbc141c95bb5fb26663444fd9df8ea47c5d7fc202f60aa7468c3162a9365cbb0615fc5e1b1328fe31
  languageName: node
  linkType: hard

"simple-swizzle@npm:^0.2.2":
  version: 0.2.2
  resolution: "simple-swizzle@npm:0.2.2"
  dependencies:
    is-arrayish: ^0.3.1
  checksum: a7f3f2ab5c76c4472d5c578df892e857323e452d9f392e1b5cf74b74db66e6294a1e1b8b390b519fa1b96b5b613f2a37db6cffef52c3f1f8f3c5ea64eb2d54c0
  languageName: node
  linkType: hard

"sirv@npm:^1.0.7":
  version: 1.0.19
  resolution: "sirv@npm:1.0.19"
  dependencies:
    "@polka/url": ^1.0.0-next.20
    mrmime: ^1.0.0
    totalist: ^1.0.0
  checksum: c943cfc61baf85f05f125451796212ec35d4377af4da90ae8ec1fa23e6d7b0b4d9c74a8fbf65af83c94e669e88a09dc6451ba99154235eead4393c10dda5b07c
  languageName: node
  linkType: hard

"slash@npm:^3.0.0":
  version: 3.0.0
  resolution: "slash@npm:3.0.0"
  checksum: 94a93fff615f25a999ad4b83c9d5e257a7280c90a32a7cb8b4a87996e4babf322e469c42b7f649fd5796edd8687652f3fb452a86dc97a816f01113183393f11c
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: b5167a7142c1da704c0e3af85c402002b597081dd9575031a90b4f229ca5678e9a36e8a374f1814c8156a725d17008ae3bde63b92f9cfd132526379e580bec8b
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^7.0.0":
  version: 7.0.0
  resolution: "socks-proxy-agent@npm:7.0.0"
  dependencies:
    agent-base: ^6.0.2
    debug: ^4.3.3
    socks: ^2.6.2
  checksum: 720554370154cbc979e2e9ce6a6ec6ced205d02757d8f5d93fe95adae454fc187a5cbfc6b022afab850a5ce9b4c7d73e0f98e381879cf45f66317a4895953846
  languageName: node
  linkType: hard

"socks@npm:^2.6.2":
  version: 2.7.1
  resolution: "socks@npm:2.7.1"
  dependencies:
    ip: ^2.0.0
    smart-buffer: ^4.2.0
  checksum: 259d9e3e8e1c9809a7f5c32238c3d4d2a36b39b83851d0f573bfde5f21c4b1288417ce1af06af1452569cd1eb0841169afd4998f0e04ba04656f6b7f0e46d748
  languageName: node
  linkType: hard

"sonic-boom@npm:3.8.0, sonic-boom@npm:^3.7.0":
  version: 3.8.0
  resolution: "sonic-boom@npm:3.8.0"
  dependencies:
    atomic-sleep: ^1.0.0
  checksum: c21ece61a0cabb78db96547aecb4e9086eba2db2d53030221ed07215bfda2d25bb02906366ea2584cbe73d236dd7dd109122d3d7287914b76a9630e0a36ad819
  languageName: node
  linkType: hard

"sonic-boom@npm:^2.2.1":
  version: 2.8.0
  resolution: "sonic-boom@npm:2.8.0"
  dependencies:
    atomic-sleep: ^1.0.0
  checksum: c7f9c89f931d7f60f8e0741551a729f0d81e6dc407a99420fc847a9a4c25af048a615b1188ab3c4f1fb3708fe4904973ddab6ebcc8ed5b78b50ab81a99045910
  languageName: node
  linkType: hard

"sort-object-keys@npm:^1.1.3":
  version: 1.1.3
  resolution: "sort-object-keys@npm:1.1.3"
  checksum: abea944d6722a1710a1aa6e4f9509da085d93d5fc0db23947cb411eedc7731f80022ce8fa68ed83a53dd2ac7441fcf72a3f38c09b3d9bbc4ff80546aa2e151ad
  languageName: node
  linkType: hard

"sort-package-json@npm:~1.53.1":
  version: 1.53.1
  resolution: "sort-package-json@npm:1.53.1"
  dependencies:
    detect-indent: ^6.0.0
    detect-newline: 3.1.0
    git-hooks-list: 1.0.3
    globby: 10.0.0
    is-plain-obj: 2.1.0
    sort-object-keys: ^1.1.3
  bin:
    sort-package-json: cli.js
  checksum: 3bf0b1a625566eb061d7d811f4d8b1cebc2c4d85dcb746fa2b27d39703d3d78edead1aa990273b02264af0a618aa6e7edbf24621e280f03bb06418cbd1f07889
  languageName: node
  linkType: hard

"source-list-map@npm:^2.0.0":
  version: 2.0.1
  resolution: "source-list-map@npm:2.0.1"
  checksum: 806efc6f75e7cd31e4815e7a3aaf75a45c704871ea4075cb2eb49882c6fca28998f44fc5ac91adb6de03b2882ee6fb02f951fdc85e6a22b338c32bfe19557938
  languageName: node
  linkType: hard

"source-map-js@npm:^1.0.2":
  version: 1.0.2
  resolution: "source-map-js@npm:1.0.2"
  checksum: c049a7fc4deb9a7e9b481ae3d424cc793cb4845daa690bc5a05d428bf41bf231ced49b4cf0c9e77f9d42fdb3d20d6187619fc586605f5eabe995a316da8d377c
  languageName: node
  linkType: hard

"source-map-loader@npm:~1.0.2":
  version: 1.0.2
  resolution: "source-map-loader@npm:1.0.2"
  dependencies:
    data-urls: ^2.0.0
    iconv-lite: ^0.6.2
    loader-utils: ^2.0.0
    schema-utils: ^2.7.0
    source-map: ^0.6.1
  peerDependencies:
    webpack: ^4.0.0 || ^5.0.0
  checksum: 0360b536e904f8fea452d0e122b9199661765229dc62a4b8093cc9d14e985f2ddd146355ede6d11acdd0b9bf4639b364e2526afcf9d3218ed45af63aa5eb053f
  languageName: node
  linkType: hard

"source-map-support@npm:~0.5.12, source-map-support@npm:~0.5.20":
  version: 0.5.21
  resolution: "source-map-support@npm:0.5.21"
  dependencies:
    buffer-from: ^1.0.0
    source-map: ^0.6.0
  checksum: 43e98d700d79af1d36f859bdb7318e601dfc918c7ba2e98456118ebc4c4872b327773e5a1df09b0524e9e5063bb18f0934538eace60cca2710d1fa687645d137
  languageName: node
  linkType: hard

"source-map@npm:^0.6.0, source-map@npm:^0.6.1, source-map@npm:~0.6.0, source-map@npm:~0.6.1":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 59ce8640cf3f3124f64ac289012c2b8bd377c238e316fb323ea22fbfe83da07d81e000071d7242cad7a23cd91c7de98e4df8830ec3f133cb6133a5f6e9f67bc2
  languageName: node
  linkType: hard

"speech-rule-engine@npm:^4.0.6":
  version: 4.0.7
  resolution: "speech-rule-engine@npm:4.0.7"
  dependencies:
    commander: 9.2.0
    wicked-good-xpath: 1.3.0
    xmldom-sre: 0.1.31
  bin:
    sre: bin/sre
  checksum: e5b8a5878be61d0344d5e9e0327e6bdf25a23de8fb66bd1898719d52b5f12f42b7e11a3387b8f293420c5eaab57b3ed9099be0adcc2132177301e81134612f38
  languageName: node
  linkType: hard

"split2@npm:^4.0.0":
  version: 4.1.0
  resolution: "split2@npm:4.1.0"
  checksum: ec581597cb74c13cdfb5e2047543dd40cb1e8e9803c7b1e0c29ede05f2b4f049b2d6e7f2788a225d544549375719658b8f38e9366364dec35dc7a12edfda5ee5
  languageName: node
  linkType: hard

"sprintf-js@npm:~1.0.2":
  version: 1.0.3
  resolution: "sprintf-js@npm:1.0.3"
  checksum: 19d79aec211f09b99ec3099b5b2ae2f6e9cdefe50bc91ac4c69144b6d3928a640bb6ae5b3def70c2e85a2c3d9f5ec2719921e3a59d3ca3ef4b2fd1a4656a0df3
  languageName: node
  linkType: hard

"sshpk@npm:^1.18.0":
  version: 1.18.0
  resolution: "sshpk@npm:1.18.0"
  dependencies:
    asn1: ~0.2.3
    assert-plus: ^1.0.0
    bcrypt-pbkdf: ^1.0.0
    dashdash: ^1.12.0
    ecc-jsbn: ~0.1.1
    getpass: ^0.1.1
    jsbn: ~0.1.0
    safer-buffer: ^2.0.2
    tweetnacl: ~0.14.0
  bin:
    sshpk-conv: bin/sshpk-conv
    sshpk-sign: bin/sshpk-sign
    sshpk-verify: bin/sshpk-verify
  checksum: 01d43374eee3a7e37b3b82fdbecd5518cbb2e47ccbed27d2ae30f9753f22bd6ffad31225cb8ef013bc3fb7785e686cea619203ee1439a228f965558c367c3cfa
  languageName: node
  linkType: hard

"ssri@npm:^9.0.0":
  version: 9.0.1
  resolution: "ssri@npm:9.0.1"
  dependencies:
    minipass: ^3.1.1
  checksum: fb58f5e46b6923ae67b87ad5ef1c5ab6d427a17db0bead84570c2df3cd50b4ceb880ebdba2d60726588272890bae842a744e1ecce5bd2a2a582fccd5068309eb
  languageName: node
  linkType: hard

"statuses@npm:2.0.1":
  version: 2.0.1
  resolution: "statuses@npm:2.0.1"
  checksum: 18c7623fdb8f646fb213ca4051be4df7efb3484d4ab662937ca6fbef7ced9b9e12842709872eb3020cc3504b93bde88935c9f6417489627a7786f24f8031cbcb
  languageName: node
  linkType: hard

"steno@npm:^0.4.1":
  version: 0.4.4
  resolution: "steno@npm:0.4.4"
  dependencies:
    graceful-fs: ^4.1.3
  checksum: 87df4121cf8159fceb3dc925111aff1e237bdea2d37f6684eabbcdea63bfcff79b3234f2a61ffe8de5cf17fcb97e2cf09075a2a98993251f79e2868fe0d5ba1e
  languageName: node
  linkType: hard

"stream-shift@npm:^1.0.0":
  version: 1.0.1
  resolution: "stream-shift@npm:1.0.1"
  checksum: 59b82b44b29ec3699b5519a49b3cedcc6db58c72fb40c04e005525dfdcab1c75c4e0c180b923c380f204bed78211b9bad8faecc7b93dece4d004c3f6ec75737b
  languageName: node
  linkType: hard

"streamx@npm:^2.15.0":
  version: 2.22.0
  resolution: "streamx@npm:2.22.0"
  dependencies:
    bare-events: ^2.2.0
    fast-fifo: ^1.3.2
    text-decoder: ^1.1.0
  dependenciesMeta:
    bare-events:
      optional: true
  checksum: 9b2772a084281129d402f298bddf8d5f3c09b6b3d9b5c93df942e886b0b963c742a89736415cc53ffb8fc1f6f5b0b3ea171ed0ba86f1b31cde6ed35db5e07f6d
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^1.0.2 || 2 || 3 || 4, string-width@npm:^4.1.0, string-width@npm:^4.2.0, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: ^8.0.0
    is-fullwidth-code-point: ^3.0.0
    strip-ansi: ^6.0.1
  checksum: e52c10dc3fbfcd6c3a15f159f54a90024241d0f149cf8aed2982a2d801d2e64df0bf1dc351cf8e95c3319323f9f220c16e740b06faecd53e2462df1d2b5443fb
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: ^0.2.0
    emoji-regex: ^9.2.2
    strip-ansi: ^7.0.1
  checksum: 7369deaa29f21dda9a438686154b62c2c5f661f8dda60449088f9f980196f7908fc39fdd1803e3e01541970287cf5deae336798337e9319a7055af89dafa7193
  languageName: node
  linkType: hard

"string_decoder@npm:^1.1.1, string_decoder@npm:~1.1.1":
  version: 1.1.1
  resolution: "string_decoder@npm:1.1.1"
  dependencies:
    safe-buffer: ~5.1.0
  checksum: 9ab7e56f9d60a28f2be697419917c50cac19f3e8e6c28ef26ed5f4852289fe0de5d6997d29becf59028556f2c62983790c1d9ba1e2a3cc401768ca12d5183a5b
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: ^5.0.1
  checksum: f3cd25890aef3ba6e1a74e20896c21a46f482e93df4a06567cebf2b57edabb15133f1f94e57434e0a958d61186087b1008e89c94875d019910a213181a14fc8c
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.0.1
  resolution: "strip-ansi@npm:7.0.1"
  dependencies:
    ansi-regex: ^6.0.1
  checksum: 257f78fa433520e7f9897722731d78599cb3fce29ff26a20a5e12ba4957463b50a01136f37c43707f4951817a75e90820174853d6ccc240997adc5df8f966039
  languageName: node
  linkType: hard

"strip-json-comments@npm:~2.0.1":
  version: 2.0.1
  resolution: "strip-json-comments@npm:2.0.1"
  checksum: 1074ccb63270d32ca28edfb0a281c96b94dc679077828135141f27d52a5a398ef5e78bcf22809d23cadc2b81dfbe345eb5fd8699b385c8b1128907dec4a7d1e1
  languageName: node
  linkType: hard

"style-loader@npm:~3.3.1":
  version: 3.3.1
  resolution: "style-loader@npm:3.3.1"
  peerDependencies:
    webpack: ^5.0.0
  checksum: 470feef680f59e2fce4d6601b5c55b88c01ad8d1dd693c528ffd591ff5fd7c01a4eff3bdbe62f26f847d6bd2430c9ab594be23307cfe7a3446ab236683f0d066
  languageName: node
  linkType: hard

"style-mod@npm:^4.0.0, style-mod@npm:^4.1.0":
  version: 4.1.2
  resolution: "style-mod@npm:4.1.2"
  checksum: 7c5c3e82747f9bcf5f288d8d07f50848e4630fe5ff7bfe4d94cc87d6b6a2588227cbf21b4c792ac6406e5852293300a75e710714479a5c59a06af677f0825ef8
  languageName: node
  linkType: hard

"stylis@npm:^4.3.6":
  version: 4.3.6
  resolution: "stylis@npm:4.3.6"
  checksum: 4f56a087caace85b34c3a163cf9d662f58f42dc865b2447af5c3ee3588eebaffe90875fe294578cce26f172ff527cad2b01433f6e1ae156400ec38c37c79fd61
  languageName: node
  linkType: hard

"supports-color@npm:^5.3.0":
  version: 5.5.0
  resolution: "supports-color@npm:5.5.0"
  dependencies:
    has-flag: ^3.0.0
  checksum: 95f6f4ba5afdf92f495b5a912d4abee8dcba766ae719b975c56c084f5004845f6f5a5f7769f52d53f40e21952a6d87411bafe34af4a01e65f9926002e38e1dac
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0, supports-color@npm:^7.2.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: ^4.0.0
  checksum: 3dda818de06ebbe5b9653e07842d9479f3555ebc77e9a0280caf5a14fb877ffee9ed57007c3b78f5a6324b8dbeec648d9e97a24e2ed9fdb81ddc69ea07100f4a
  languageName: node
  linkType: hard

"supports-color@npm:^8.0.0":
  version: 8.1.1
  resolution: "supports-color@npm:8.1.1"
  dependencies:
    has-flag: ^4.0.0
  checksum: c052193a7e43c6cdc741eb7f378df605636e01ad434badf7324f17fb60c69a880d8d8fcdcb562cf94c2350e57b937d7425ab5b8326c67c2adc48f7c87c1db406
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 53b1e247e68e05db7b3808b99b892bd36fb096e6fba213a06da7fab22045e97597db425c724f2bbd6c99a3c295e1e73f3e4de78592289f38431049e1277ca0ae
  languageName: node
  linkType: hard

"tabbable@npm:^5.2.0":
  version: 5.3.3
  resolution: "tabbable@npm:5.3.3"
  checksum: 1aa56e1bb617cc10616c407f4e756f0607f3e2d30f9803664d70b85db037ca27e75918ed1c71443f3dc902e21dc9f991ce4b52d63a538c9b69b3218d3babcd70
  languageName: node
  linkType: hard

"tapable@npm:^2.0.0, tapable@npm:^2.1.1, tapable@npm:^2.2.0":
  version: 2.2.1
  resolution: "tapable@npm:2.2.1"
  checksum: 3b7a1b4d86fa940aad46d9e73d1e8739335efd4c48322cb37d073eb6f80f5281889bf0320c6d8ffcfa1a0dd5bfdbd0f9d037e252ef972aca595330538aac4d51
  languageName: node
  linkType: hard

"tar-stream@npm:^3.1.7":
  version: 3.1.7
  resolution: "tar-stream@npm:3.1.7"
  dependencies:
    b4a: ^1.6.4
    fast-fifo: ^1.2.0
    streamx: ^2.15.0
  checksum: 6393a6c19082b17b8dcc8e7fd349352bb29b4b8bfe1075912b91b01743ba6bb4298f5ff0b499a3bbaf82121830e96a1a59d4f21a43c0df339e54b01789cb8cc6
  languageName: node
  linkType: hard

"tar@npm:^6.0.5, tar@npm:^6.1.11, tar@npm:^6.1.2":
  version: 6.1.11
  resolution: "tar@npm:6.1.11"
  dependencies:
    chownr: ^2.0.0
    fs-minipass: ^2.0.0
    minipass: ^3.0.0
    minizlib: ^2.1.1
    mkdirp: ^1.0.3
    yallist: ^4.0.0
  checksum: a04c07bb9e2d8f46776517d4618f2406fb977a74d914ad98b264fc3db0fe8224da5bec11e5f8902c5b9bcb8ace22d95fbe3c7b36b8593b7dfc8391a25898f32f
  languageName: node
  linkType: hard

"terser-webpack-plugin@npm:^5.3.10, terser-webpack-plugin@npm:^5.3.7":
  version: 5.3.10
  resolution: "terser-webpack-plugin@npm:5.3.10"
  dependencies:
    "@jridgewell/trace-mapping": ^0.3.20
    jest-worker: ^27.4.5
    schema-utils: ^3.1.1
    serialize-javascript: ^6.0.1
    terser: ^5.26.0
  peerDependencies:
    webpack: ^5.1.0
  peerDependenciesMeta:
    "@swc/core":
      optional: true
    esbuild:
      optional: true
    uglify-js:
      optional: true
  checksum: bd6e7596cf815f3353e2a53e79cbdec959a1b0276f5e5d4e63e9d7c3c5bb5306df567729da287d1c7b39d79093e56863c569c42c6c24cc34c76aa313bd2cbcea
  languageName: node
  linkType: hard

"terser@npm:^4.6.3":
  version: 4.8.1
  resolution: "terser@npm:4.8.1"
  dependencies:
    commander: ^2.20.0
    source-map: ~0.6.1
    source-map-support: ~0.5.12
  bin:
    terser: bin/terser
  checksum: b342819bf7e82283059aaa3f22bb74deb1862d07573ba5a8947882190ad525fd9b44a15074986be083fd379c58b9a879457a330b66dcdb77b485c44267f9a55a
  languageName: node
  linkType: hard

"terser@npm:^5.10.0, terser@npm:^5.26.0":
  version: 5.32.0
  resolution: "terser@npm:5.32.0"
  dependencies:
    "@jridgewell/source-map": ^0.3.3
    acorn: ^8.8.2
    commander: ^2.20.0
    source-map-support: ~0.5.20
  bin:
    terser: bin/terser
  checksum: 61e7c064ed693222c67413294181713798258ab4326b2f0b14ce889df530fb9683e7f2af2dfd228f1b9aae90c38c44dcbdfd22c0a3e020c56dff2770d1dc4a6d
  languageName: node
  linkType: hard

"text-decoder@npm:^1.1.0":
  version: 1.2.3
  resolution: "text-decoder@npm:1.2.3"
  dependencies:
    b4a: ^1.6.4
  checksum: d7642a61f9d72330eac52ff6b6e8d34dea03ebbb1e82749a8734e7892e246cf262ed70730d20c4351c5dc5334297b9cc6c0b6a8725a204a63a197d7728bb35e5
  languageName: node
  linkType: hard

"thread-stream@npm:^0.15.1":
  version: 0.15.2
  resolution: "thread-stream@npm:0.15.2"
  dependencies:
    real-require: ^0.1.0
  checksum: 0547795a8f357ba1ac0dba29c71f965182e29e21752951a04a7167515ee37524bfba6c410f31e65a01a8d3e5b93400b812889aa09523e38ce4d744c894ffa6c0
  languageName: node
  linkType: hard

"thread-stream@npm:^2.0.0":
  version: 2.7.0
  resolution: "thread-stream@npm:2.7.0"
  dependencies:
    real-require: ^0.2.0
  checksum: 75ab019cda628344c7779e5f5a88f7759764efd29d320327ad2e6c2622778b5f1c43a3966d76a9ee5744086d61c680b413548f5521030f9e9055487684436165
  languageName: node
  linkType: hard

"through2@npm:^2.0.3":
  version: 2.0.5
  resolution: "through2@npm:2.0.5"
  dependencies:
    readable-stream: ~2.3.6
    xtend: ~4.0.1
  checksum: beb0f338aa2931e5660ec7bf3ad949e6d2e068c31f4737b9525e5201b824ac40cac6a337224856b56bd1ddd866334bbfb92a9f57cd6f66bc3f18d3d86fc0fe50
  languageName: node
  linkType: hard

"through@npm:>=2.2.7 <3, through@npm:^2.3.6":
  version: 2.3.8
  resolution: "through@npm:2.3.8"
  checksum: a38c3e059853c494af95d50c072b83f8b676a9ba2818dcc5b108ef252230735c54e0185437618596c790bbba8fcdaef5b290405981ffa09dce67b1f1bf190cbd
  languageName: node
  linkType: hard

"tinyexec@npm:^0.3.2":
  version: 0.3.2
  resolution: "tinyexec@npm:0.3.2"
  checksum: bd491923020610bdeadb0d8cf5d70e7cbad5a3201620fd01048c9bf3b31ffaa75c33254e1540e13b993ce4e8187852b0b5a93057bb598e7a57afa2ca2048a35c
  languageName: node
  linkType: hard

"tinylogic@npm:^2.0.0":
  version: 2.0.0
  resolution: "tinylogic@npm:2.0.0"
  checksum: b966cbb41241a048095fb9e685d5e2020475fdea2c65b4ae51e5dee48964860a4505d987503c004b8a76e96b64c7da2f49954dd36c691d559c315d878ce7da29
  languageName: node
  linkType: hard

"tldts-core@npm:^6.1.78":
  version: 6.1.78
  resolution: "tldts-core@npm:6.1.78"
  checksum: e9c4bc7f2927639c99a6443a15181f63aaecdbf1cde331aa0a8e01734eb58d3c31cb4e5edb2f3792200a03c2b51eebcb21e5ca856668c4f676f6c6db2af6bc55
  languageName: node
  linkType: hard

"tldts@npm:^6.1.32":
  version: 6.1.78
  resolution: "tldts@npm:6.1.78"
  dependencies:
    tldts-core: ^6.1.78
  bin:
    tldts: bin/cli.js
  checksum: d7e1dfc6394779c0559fbd640a950d60aae0e14ae2ee967b39750e37e0658d5051d949d05cf57756f83adf3b3155572967c0f3e90286473d379644dce7a3abb2
  languageName: node
  linkType: hard

"tmp@npm:^0.0.33":
  version: 0.0.33
  resolution: "tmp@npm:0.0.33"
  dependencies:
    os-tmpdir: ~1.0.2
  checksum: 902d7aceb74453ea02abbf58c203f4a8fc1cead89b60b31e354f74ed5b3fb09ea817f94fb310f884a5d16987dd9fa5a735412a7c2dd088dd3d415aa819ae3a28
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: ^7.0.0
  checksum: f76fa01b3d5be85db6a2a143e24df9f60dd047d151062d0ba3df62953f2f697b16fe5dad9b0ac6191c7efc7b1d9dcaa4b768174b7b29da89d4428e64bc0a20ed
  languageName: node
  linkType: hard

"toidentifier@npm:1.0.1":
  version: 1.0.1
  resolution: "toidentifier@npm:1.0.1"
  checksum: 952c29e2a85d7123239b5cfdd889a0dde47ab0497f0913d70588f19c53f7e0b5327c95f4651e413c74b785147f9637b17410ac8c846d5d4a20a5a33eb6dc3a45
  languageName: node
  linkType: hard

"topojson-client@npm:^3.1.0":
  version: 3.1.0
  resolution: "topojson-client@npm:3.1.0"
  dependencies:
    commander: 2
  bin:
    topo2geo: bin/topo2geo
    topomerge: bin/topomerge
    topoquantize: bin/topoquantize
  checksum: 8c029a4f18324ace0b8b55dd90edbd40c9e3c6de18bafbb5da37ca20ebf20e26fbd4420891acb3c2c264e214185f7557871f5651a9eee517028663be98d836de
  languageName: node
  linkType: hard

"totalist@npm:^1.0.0":
  version: 1.1.0
  resolution: "totalist@npm:1.1.0"
  checksum: dfab80c7104a1d170adc8c18782d6c04b7df08352dec452191208c66395f7ef2af7537ddfa2cf1decbdcfab1a47afbbf0dec6543ea191da98c1c6e1599f86adc
  languageName: node
  linkType: hard

"tough-cookie@npm:^5.0.0":
  version: 5.1.1
  resolution: "tough-cookie@npm:5.1.1"
  dependencies:
    tldts: ^6.1.32
  checksum: 051d2d09df12448642928de9e1da7c296ae1019c6531e87f45f51fd29e8f235efbe94ef6502b37e874df72047c13a34da8816f2c05c7c358ead27ef4fbbd8117
  languageName: node
  linkType: hard

"tr46@npm:^2.1.0":
  version: 2.1.0
  resolution: "tr46@npm:2.1.0"
  dependencies:
    punycode: ^2.1.1
  checksum: ffe6049b9dca3ae329b059aada7f515b0f0064c611b39b51ff6b53897e954650f6f63d9319c6c008d36ead477c7b55e5f64c9dc60588ddc91ff720d64eb710b3
  languageName: node
  linkType: hard

"tr46@npm:~0.0.3":
  version: 0.0.3
  resolution: "tr46@npm:0.0.3"
  checksum: 726321c5eaf41b5002e17ffbd1fb7245999a073e8979085dacd47c4b4e8068ff5777142fc6726d6ca1fd2ff16921b48788b87225cbc57c72636f6efa8efbffe3
  languageName: node
  linkType: hard

"treeify@npm:^1.1.0":
  version: 1.1.0
  resolution: "treeify@npm:1.1.0"
  checksum: aa00dded220c1dd052573bd6fc2c52862f09870851a284f0d3650d72bf913ba9b4f6b824f4f1ab81899bae29375f4266b07fe47cbf82343a1efa13cc09ce87af
  languageName: node
  linkType: hard

"ts-dedent@npm:^2.2.0":
  version: 2.2.0
  resolution: "ts-dedent@npm:2.2.0"
  checksum: 93ed8f7878b6d5ed3c08d99b740010eede6bccfe64bce61c5a4da06a2c17d6ddbb80a8c49c2d15251de7594a4f93ffa21dd10e7be75ef66a4dc9951b4a94e2af
  languageName: node
  linkType: hard

"tslib@npm:^1.13.0":
  version: 1.14.1
  resolution: "tslib@npm:1.14.1"
  checksum: dbe628ef87f66691d5d2959b3e41b9ca0045c3ee3c7c7b906cc1e328b39f199bb1ad9e671c39025bd56122ac57dfbf7385a94843b1cc07c60a4db74795829acd
  languageName: node
  linkType: hard

"tslib@npm:^2.0.3, tslib@npm:^2.1.0, tslib@npm:^2.4.0, tslib@npm:^2.5.0, tslib@npm:~2.5.0":
  version: 2.5.0
  resolution: "tslib@npm:2.5.0"
  checksum: ae3ed5f9ce29932d049908ebfdf21b3a003a85653a9a140d614da6b767a93ef94f460e52c3d787f0e4f383546981713f165037dc2274df212ea9f8a4541004e1
  languageName: node
  linkType: hard

"tunnel-agent@npm:^0.6.0":
  version: 0.6.0
  resolution: "tunnel-agent@npm:0.6.0"
  dependencies:
    safe-buffer: ^5.0.1
  checksum: 05f6510358f8afc62a057b8b692f05d70c1782b70db86d6a1e0d5e28a32389e52fa6e7707b6c5ecccacc031462e4bc35af85ecfe4bbc341767917b7cf6965711
  languageName: node
  linkType: hard

"tunnel@npm:^0.0.6":
  version: 0.0.6
  resolution: "tunnel@npm:0.0.6"
  checksum: c362948df9ad34b649b5585e54ce2838fa583aa3037091aaed66793c65b423a264e5229f0d7e9a95513a795ac2bd4cb72cda7e89a74313f182c1e9ae0b0994fa
  languageName: node
  linkType: hard

"tweetnacl@npm:^0.14.3, tweetnacl@npm:~0.14.0":
  version: 0.14.5
  resolution: "tweetnacl@npm:0.14.5"
  checksum: 6061daba1724f59473d99a7bb82e13f211cdf6e31315510ae9656fefd4779851cb927adad90f3b488c8ed77c106adc0421ea8055f6f976ff21b27c5c4e918487
  languageName: node
  linkType: hard

"typanion@npm:^3.8.0":
  version: 3.14.0
  resolution: "typanion@npm:3.14.0"
  checksum: fc0590d02c13c659eb1689e8adf7777e6c00dc911377e44cd36fe1b1271cfaca71547149f12cdc275058c0de5562a14e5273adbae66d47e6e0320e36007f5912
  languageName: node
  linkType: hard

"type-fest@npm:^3.0.0":
  version: 3.13.1
  resolution: "type-fest@npm:3.13.1"
  checksum: c06b0901d54391dc46de3802375f5579868949d71f93b425ce564e19a428a0d411ae8d8cb0e300d330071d86152c3ea86e744c3f2860a42a79585b6ec2fdae8e
  languageName: node
  linkType: hard

"type-is@npm:~1.6.18":
  version: 1.6.18
  resolution: "type-is@npm:1.6.18"
  dependencies:
    media-typer: 0.3.0
    mime-types: ~2.1.24
  checksum: 2c8e47675d55f8b4e404bcf529abdf5036c537a04c2b20177bcf78c9e3c1da69da3942b1346e6edb09e823228c0ee656ef0e033765ec39a70d496ef601a0c657
  languageName: node
  linkType: hard

"typescript@npm:~5.5.4":
  version: 5.5.4
  resolution: "typescript@npm:5.5.4"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: b309040f3a1cd91c68a5a58af6b9fdd4e849b8c42d837b2c2e73f9a4f96a98c4f1ed398a9aab576ee0a4748f5690cf594e6b99dbe61de7839da748c41e6d6ca8
  languageName: node
  linkType: hard

"typescript@patch:typescript@~5.5.4#~builtin<compat/typescript>":
  version: 5.5.4
  resolution: "typescript@patch:typescript@npm%3A5.5.4#~builtin<compat/typescript>::version=5.5.4&hash=85af82"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: fc52962f31a5bcb716d4213bef516885e4f01f30cea797a831205fc9ef12b405a40561c40eae3127ab85ba1548e7df49df2bcdee6b84a94bfbe3a0d7eff16b14
  languageName: node
  linkType: hard

"typestyle@npm:^2.0.4":
  version: 2.4.0
  resolution: "typestyle@npm:2.4.0"
  dependencies:
    csstype: 3.0.10
    free-style: 3.1.0
  checksum: 8b4f02c24f67b594f98507b15a753dabd4db5eb0af007e1d310527c64030e11e9464b25b5a6bc65fb5eec9a4459a8336050121ecc29063ac87b8b47a6d698893
  languageName: node
  linkType: hard

"ufo@npm:^1.5.4":
  version: 1.5.4
  resolution: "ufo@npm:1.5.4"
  checksum: f244703b7d4f9f0df4f9af23921241ab73410b591f4e5b39c23e3147f3159b139a4b1fb5903189c306129f7a16b55995dac0008e0fbae88a37c3e58cbc34d833
  languageName: node
  linkType: hard

"uglify-js@npm:^3.1.4":
  version: 3.17.4
  resolution: "uglify-js@npm:3.17.4"
  bin:
    uglifyjs: bin/uglifyjs
  checksum: 7b3897df38b6fc7d7d9f4dcd658599d81aa2b1fb0d074829dd4e5290f7318dbca1f4af2f45acb833b95b1fe0ed4698662ab61b87e94328eb4c0a0d3435baf924
  languageName: node
  linkType: hard

"undici-types@npm:~5.26.4":
  version: 5.26.5
  resolution: "undici-types@npm:5.26.5"
  checksum: 3192ef6f3fd5df652f2dc1cd782b49d6ff14dc98e5dced492aa8a8c65425227da5da6aafe22523c67f035a272c599bb89cfe803c1db6311e44bed3042fc25487
  languageName: node
  linkType: hard

"unique-filename@npm:^2.0.0":
  version: 2.0.1
  resolution: "unique-filename@npm:2.0.1"
  dependencies:
    unique-slug: ^3.0.0
  checksum: 807acf3381aff319086b64dc7125a9a37c09c44af7620bd4f7f3247fcd5565660ac12d8b80534dcbfd067e6fe88a67e621386dd796a8af828d1337a8420a255f
  languageName: node
  linkType: hard

"unique-slug@npm:^3.0.0":
  version: 3.0.0
  resolution: "unique-slug@npm:3.0.0"
  dependencies:
    imurmurhash: ^0.1.4
  checksum: 49f8d915ba7f0101801b922062ee46b7953256c93ceca74303bd8e6413ae10aa7e8216556b54dc5382895e8221d04f1efaf75f945c2e4a515b4139f77aa6640c
  languageName: node
  linkType: hard

"universalify@npm:^2.0.0":
  version: 2.0.0
  resolution: "universalify@npm:2.0.0"
  checksum: 2406a4edf4a8830aa6813278bab1f953a8e40f2f63a37873ffa9a3bc8f9745d06cc8e88f3572cb899b7e509013f7f6fcc3e37e8a6d914167a5381d8440518c44
  languageName: node
  linkType: hard

"unix-crypt-td-js@npm:1.1.4":
  version: 1.1.4
  resolution: "unix-crypt-td-js@npm:1.1.4"
  checksum: c1bfcd699fa0fa15eac087760e34fdf7e2e686de1c40dde7f550c2429389fd7ef68bf83ce804ce7882551573330832aae32e80be3ce991f7080aabd98f8bd554
  languageName: node
  linkType: hard

"unpipe@npm:1.0.0, unpipe@npm:~1.0.0":
  version: 1.0.0
  resolution: "unpipe@npm:1.0.0"
  checksum: 4fa18d8d8d977c55cb09715385c203197105e10a6d220087ec819f50cb68870f02942244f1017565484237f1f8c5d3cd413631b1ae104d3096f24fdfde1b4aa2
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.1.0":
  version: 1.1.0
  resolution: "update-browserslist-db@npm:1.1.0"
  dependencies:
    escalade: ^3.1.2
    picocolors: ^1.0.1
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 7b74694d96f0c360f01b702e72353dc5a49df4fe6663d3ee4e5c628f061576cddf56af35a3a886238c01dd3d8f231b7a86a8ceaa31e7a9220ae31c1c1238e562
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: ^2.1.0
  checksum: 7167432de6817fe8e9e0c9684f1d2de2bb688c94388f7569f7dbdb1587c9f4ca2a77962f134ec90be0cc4d004c939ff0d05acc9f34a0db39a3c797dada262633
  languageName: node
  linkType: hard

"url-parse@npm:~1.5.4":
  version: 1.5.10
  resolution: "url-parse@npm:1.5.10"
  dependencies:
    querystringify: ^2.1.1
    requires-port: ^1.0.0
  checksum: fbdba6b1d83336aca2216bbdc38ba658d9cfb8fc7f665eb8b17852de638ff7d1a162c198a8e4ed66001ddbf6c9888d41e4798912c62b4fd777a31657989f7bdf
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1, util-deprecate@npm:^1.0.2, util-deprecate@npm:~1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 474acf1146cb2701fe3b074892217553dfcf9a031280919ba1b8d651a068c9b15d863b7303cb15bd00a862b498e6cf4ad7b4a08fb134edd5a6f7641681cb54a2
  languageName: node
  linkType: hard

"utila@npm:~0.4":
  version: 0.4.0
  resolution: "utila@npm:0.4.0"
  checksum: 97ffd3bd2bb80c773429d3fb8396469115cd190dded1e733f190d8b602bd0a1bcd6216b7ce3c4395ee3c79e3c879c19d268dbaae3093564cb169ad1212d436f4
  languageName: node
  linkType: hard

"utils-merge@npm:1.0.1":
  version: 1.0.1
  resolution: "utils-merge@npm:1.0.1"
  checksum: c81095493225ecfc28add49c106ca4f09cdf56bc66731aa8dabc2edbbccb1e1bfe2de6a115e5c6a380d3ea166d1636410b62ef216bb07b3feb1cfde1d95d5080
  languageName: node
  linkType: hard

"uuid@npm:^11.1.0":
  version: 11.1.0
  resolution: "uuid@npm:11.1.0"
  bin:
    uuid: dist/esm/bin/uuid
  checksum: 840f19758543c4631e58a29439e51b5b669d5f34b4dd2700b6a1d15c5708c7a6e0c3e2c8c4a2eae761a3a7caa7e9884d00c86c02622ba91137bd3deade6b4b4a
  languageName: node
  linkType: hard

"uuid@npm:^8.3.2":
  version: 8.3.2
  resolution: "uuid@npm:8.3.2"
  bin:
    uuid: dist/bin/uuid
  checksum: 5575a8a75c13120e2f10e6ddc801b2c7ed7d8f3c8ac22c7ed0c7b2ba6383ec0abda88c905085d630e251719e0777045ae3236f04c812184b7c765f63a70e58df
  languageName: node
  linkType: hard

"validate.io-array@npm:^1.0.3":
  version: 1.0.6
  resolution: "validate.io-array@npm:1.0.6"
  checksum: 54eca83ebc702e3e46499f9d9e77287a95ae25c4e727cd2fafee29c7333b3a36cca0c5d8f090b9406262786de80750fba85e7e7ef41e20bf8cc67d5570de449b
  languageName: node
  linkType: hard

"validate.io-function@npm:^1.0.2":
  version: 1.0.2
  resolution: "validate.io-function@npm:1.0.2"
  checksum: e4cce2479a20cb7c42e8630c777fb107059c27bc32925f769e3a73ca5fd62b4892d897b3c80227e14d5fcd1c5b7d05544e0579d63e59f14034c0052cda7f7c44
  languageName: node
  linkType: hard

"validate.io-integer-array@npm:^1.0.0":
  version: 1.0.0
  resolution: "validate.io-integer-array@npm:1.0.0"
  dependencies:
    validate.io-array: ^1.0.3
    validate.io-integer: ^1.0.4
  checksum: 5f6d7fab8df7d2bf546a05e830201768464605539c75a2c2417b632b4411a00df84b462f81eac75e1be95303e7e0ac92f244c137424739f4e15cd21c2eb52c7f
  languageName: node
  linkType: hard

"validate.io-integer@npm:^1.0.4":
  version: 1.0.5
  resolution: "validate.io-integer@npm:1.0.5"
  dependencies:
    validate.io-number: ^1.0.3
  checksum: 88b3f8bb5a5277a95305d64abbfc437079220ce4f57a148cc6113e7ccec03dd86b10a69d413982602aa90a62b8d516148a78716f550dcd3aff863ac1c2a7a5e6
  languageName: node
  linkType: hard

"validate.io-number@npm:^1.0.3":
  version: 1.0.3
  resolution: "validate.io-number@npm:1.0.3"
  checksum: 42418aeb6c969efa745475154fe576809b02eccd0961aad0421b090d6e7a12d23a3e28b0d5dddd2c6347c1a6bdccb82bba5048c716131cd20207244d50e07282
  languageName: node
  linkType: hard

"validator@npm:13.12.0":
  version: 13.12.0
  resolution: "validator@npm:13.12.0"
  checksum: fb8f070724770b1449ea1a968605823fdb112dbd10507b2802f8841cda3e7b5c376c40f18c84e6a7b59de320a06177e471554101a85f1fa8a70bac1a84e48adf
  languageName: node
  linkType: hard

"vary@npm:^1, vary@npm:~1.1.2":
  version: 1.1.2
  resolution: "vary@npm:1.1.2"
  checksum: ae0123222c6df65b437669d63dfa8c36cee20a504101b2fcd97b8bf76f91259c17f9f2b4d70a1e3c6bbcee7f51b28392833adb6b2770b23b01abec84e369660b
  languageName: node
  linkType: hard

"vega-canvas@npm:^1.2.7":
  version: 1.2.7
  resolution: "vega-canvas@npm:1.2.7"
  checksum: 6ff92fcdf0c359f2f662909c859a7f4cb4a502436136ab2f4c02373c47a621996ec0eea23e2108f11d62a618be301de86cd8528b5058c2e207a53ddd7ff58d1b
  languageName: node
  linkType: hard

"vega-crossfilter@npm:~4.1.3":
  version: 4.1.3
  resolution: "vega-crossfilter@npm:4.1.3"
  dependencies:
    d3-array: ^3.2.2
    vega-dataflow: ^5.7.7
    vega-util: ^1.17.3
  checksum: a0117bf07c6d94192333f16b0994c90748ad88db675702a79c1073b0d94100619ab3c75898bd45430fee6b6d46028b4c3b205819062de4fc72e93f5a9fc7bb0d
  languageName: node
  linkType: hard

"vega-dataflow@npm:^5.7.7, vega-dataflow@npm:~5.7.7":
  version: 5.7.7
  resolution: "vega-dataflow@npm:5.7.7"
  dependencies:
    vega-format: ^1.1.3
    vega-loader: ^4.5.3
    vega-util: ^1.17.3
  checksum: 43c1c039fea007dbca1a7c942821ac734a3068b3ef848f87b3b51265eb2dbd63796f58295313dda9ee8a6d308d9dfb7083530f69a5ffb0f756ce5345544cc60e
  languageName: node
  linkType: hard

"vega-embed@npm:^6.2.1":
  version: 6.21.3
  resolution: "vega-embed@npm:6.21.3"
  dependencies:
    fast-json-patch: ^3.1.1
    json-stringify-pretty-compact: ^3.0.0
    semver: ^7.3.8
    tslib: ^2.5.0
    vega-interpreter: ^1.0.4
    vega-schema-url-parser: ^2.2.0
    vega-themes: ^2.12.1
    vega-tooltip: ^0.30.1
    yallist: "*"
  peerDependencies:
    vega: ^5.21.0
    vega-lite: "*"
  checksum: e1c40e2111916760dd806cb562b09f5028c2ab941af0469e929d5df436c651a730ebe9aa6fcaed3461196f18c96ff94a5c590adc0a7cb50f33d4c9ef78096417
  languageName: node
  linkType: hard

"vega-encode@npm:~4.10.2":
  version: 4.10.2
  resolution: "vega-encode@npm:4.10.2"
  dependencies:
    d3-array: ^3.2.2
    d3-interpolate: ^3.0.1
    vega-dataflow: ^5.7.7
    vega-scale: ^7.4.2
    vega-util: ^1.17.3
  checksum: d8aa4250debf405ed6c9ca641aaa2a2d6a7ba7c3b7f7d2a319c5a1719fc9ff3b0303563120da2572fe8dd6f2db89b2f1eaeb254ec180d8ca8b16b8dbf8826124
  languageName: node
  linkType: hard

"vega-event-selector@npm:^3.0.1, vega-event-selector@npm:~3.0.0, vega-event-selector@npm:~3.0.1":
  version: 3.0.1
  resolution: "vega-event-selector@npm:3.0.1"
  checksum: 66d09b5800a19a9b0c75f28811b140a1a2e70e84be6d6f87c568cdbce6e17c8e195f130f4e3de5d6dc737142d1f46f4fe7645177e154582cc8ba27c6845b54e8
  languageName: node
  linkType: hard

"vega-expression@npm:^5.2.0, vega-expression@npm:~5.2.0":
  version: 5.2.0
  resolution: "vega-expression@npm:5.2.0"
  dependencies:
    "@types/estree": ^1.0.0
    vega-util: ^1.17.3
  checksum: 38df3b25ea994b28ec59fba869b9cb1e87518cedd3525886dc7aef5205372bac323635cd7be9907e4d71281e129689f475d3a688ea1ed015f4607088a27ca72a
  languageName: node
  linkType: hard

"vega-expression@npm:~5.0.0":
  version: 5.0.1
  resolution: "vega-expression@npm:5.0.1"
  dependencies:
    "@types/estree": ^1.0.0
    vega-util: ^1.17.1
  checksum: 396e950209a98a3fb1e28ba554f179c07aaeac7d11cfac9298a2af0b98456d69ec6573ecc7f21eff6f9f95bbfa8c59a1093d25e8ce586d0c0c589c230784db17
  languageName: node
  linkType: hard

"vega-force@npm:~4.2.2":
  version: 4.2.2
  resolution: "vega-force@npm:4.2.2"
  dependencies:
    d3-force: ^3.0.0
    vega-dataflow: ^5.7.7
    vega-util: ^1.17.3
  checksum: 9a632423cf8e1bd36b953ad99de7fde1a06fc1f1fccdde5692a90e7c1c6df7c4baa9ee950eca5c9c028db3b9ea23efef73af337d1129caa6343f6f45a484c5fe
  languageName: node
  linkType: hard

"vega-format@npm:^1.1.3, vega-format@npm:~1.1.3":
  version: 1.1.3
  resolution: "vega-format@npm:1.1.3"
  dependencies:
    d3-array: ^3.2.2
    d3-format: ^3.1.0
    d3-time-format: ^4.1.0
    vega-time: ^2.1.3
    vega-util: ^1.17.3
  checksum: ed46385be98ed837bc23f2578e6e81bfe30be18de0c2bacea8d3d02811d62ca91631f875ffae385be201b669e55f4e1d48cbf926a5377da02ff73f12f8e4f9bf
  languageName: node
  linkType: hard

"vega-functions@npm:^5.18.0, vega-functions@npm:~5.18.0":
  version: 5.18.0
  resolution: "vega-functions@npm:5.18.0"
  dependencies:
    d3-array: ^3.2.2
    d3-color: ^3.1.0
    d3-geo: ^3.1.0
    vega-dataflow: ^5.7.7
    vega-expression: ^5.2.0
    vega-scale: ^7.4.2
    vega-scenegraph: ^4.13.1
    vega-selections: ^5.6.0
    vega-statistics: ^1.9.0
    vega-time: ^2.1.3
    vega-util: ^1.17.3
  checksum: 3255bc908079d7f901c66e96dd65aa2a825a7be7e74b74d1ed6a07ba52e1e2419a9281c730be9251f2272803aaeb9a11b21ad9994fc1435f6e1a5aa9043d9e23
  languageName: node
  linkType: hard

"vega-geo@npm:~4.4.3":
  version: 4.4.3
  resolution: "vega-geo@npm:4.4.3"
  dependencies:
    d3-array: ^3.2.2
    d3-color: ^3.1.0
    d3-geo: ^3.1.0
    vega-canvas: ^1.2.7
    vega-dataflow: ^5.7.7
    vega-projection: ^1.6.2
    vega-statistics: ^1.9.0
    vega-util: ^1.17.3
  checksum: d4576ff22301d8c0dbac83c9a008c8bc01b8097765f0c79100b864caf907032a0b2280855a8cc955da795ccf9d9b6cacf1734fec7c8cc417832487e921e8cdd0
  languageName: node
  linkType: hard

"vega-hierarchy@npm:~4.1.3":
  version: 4.1.3
  resolution: "vega-hierarchy@npm:4.1.3"
  dependencies:
    d3-hierarchy: ^3.1.2
    vega-dataflow: ^5.7.7
    vega-util: ^1.17.3
  checksum: f51c417a76c4d404cbcd79c43871316db49e6c8862cf11ea62fa053219735152863f7cc0e4ad1fb62949a06adc69293ca85c511f291ca1d96ded7b192532d829
  languageName: node
  linkType: hard

"vega-interpreter@npm:^1.0.4":
  version: 1.0.5
  resolution: "vega-interpreter@npm:1.0.5"
  checksum: ed54bbeddc7942aa442ddf224b620fe68122ef4b93967376a03b6463feddf3da7a837e7b1e0a8d23fbf55898d3ac4a00a034f07acecb05b2d3e09621609dd19e
  languageName: node
  linkType: hard

"vega-label@npm:~1.3.1":
  version: 1.3.1
  resolution: "vega-label@npm:1.3.1"
  dependencies:
    vega-canvas: ^1.2.7
    vega-dataflow: ^5.7.7
    vega-scenegraph: ^4.13.1
    vega-util: ^1.17.3
  checksum: 2a8ea6a59d76f668eece257bc9c7649652882c6d1d0fabd3ee7fbddc7948be2d7c608ab9f211e68574e89f4a8cb34cce127cba08dfc0ce228c3c47b558943f0b
  languageName: node
  linkType: hard

"vega-lite@npm:^5.6.1-next.1":
  version: 5.6.1
  resolution: "vega-lite@npm:5.6.1"
  dependencies:
    "@types/clone": ~2.1.1
    clone: ~2.1.2
    fast-deep-equal: ~3.1.3
    fast-json-stable-stringify: ~2.1.0
    json-stringify-pretty-compact: ~3.0.0
    tslib: ~2.5.0
    vega-event-selector: ~3.0.0
    vega-expression: ~5.0.0
    vega-util: ~1.17.0
    yargs: ~17.6.2
  peerDependencies:
    vega: ^5.22.0
  bin:
    vl2pdf: bin/vl2pdf
    vl2png: bin/vl2png
    vl2svg: bin/vl2svg
    vl2vg: bin/vl2vg
  checksum: a06cbd0531cc71a7aeacae3ffde936a80d3cf58ff5942fb89f3544a7b5e0e055f00069dd51950cd09d667f0eefbeb12c7dccb64dd43cbb23b9eff2be1a990979
  languageName: node
  linkType: hard

"vega-loader@npm:^4.5.3, vega-loader@npm:~4.5.3":
  version: 4.5.3
  resolution: "vega-loader@npm:4.5.3"
  dependencies:
    d3-dsv: ^3.0.1
    node-fetch: ^2.6.7
    topojson-client: ^3.1.0
    vega-format: ^1.1.3
    vega-util: ^1.17.3
  checksum: f3b87f12725de843ecd6a44f29800d5aae9966c1b07d632a70fef87cae93f433bc5e47ba25f45f99725f988cb424016453d1b0452ca695401b7b40c47b47b411
  languageName: node
  linkType: hard

"vega-parser@npm:~6.6.0":
  version: 6.6.0
  resolution: "vega-parser@npm:6.6.0"
  dependencies:
    vega-dataflow: ^5.7.7
    vega-event-selector: ^3.0.1
    vega-functions: ^5.18.0
    vega-scale: ^7.4.2
    vega-util: ^1.17.3
  checksum: 3f810bddf85b3b3e125e85a774324a0d9a5f7487a9da1936c3137f676df32665fa192d2124d04d5df9de43f2ef8015a4b7f56c9fac0319dc1e9ce20c547ca4a6
  languageName: node
  linkType: hard

"vega-projection@npm:^1.6.2, vega-projection@npm:~1.6.2":
  version: 1.6.2
  resolution: "vega-projection@npm:1.6.2"
  dependencies:
    d3-geo: ^3.1.0
    d3-geo-projection: ^4.0.0
    vega-scale: ^7.4.2
  checksum: 55deb14c22b4d39ec3d1248dc1718d2ae9addad8d11937617158a8c979fcfdaacf274ee55cebd111a72d6489af25c8351a5e7dbcc19bddb3221bf28ae5dd140d
  languageName: node
  linkType: hard

"vega-regression@npm:~1.3.1":
  version: 1.3.1
  resolution: "vega-regression@npm:1.3.1"
  dependencies:
    d3-array: ^3.2.2
    vega-dataflow: ^5.7.7
    vega-statistics: ^1.9.0
    vega-util: ^1.17.3
  checksum: 6bf8dd47f64ec37c8c7d8820b71b55c6b0833a53df1acad6ce5d831d9119d0a576dda464523d1d6941f53971826062355259ba9bf483101928344f1804ac64cb
  languageName: node
  linkType: hard

"vega-runtime@npm:^6.2.1, vega-runtime@npm:~6.2.1":
  version: 6.2.1
  resolution: "vega-runtime@npm:6.2.1"
  dependencies:
    vega-dataflow: ^5.7.7
    vega-util: ^1.17.3
  checksum: 5481f943c18c4277d3f0aabd3e06a2d37b57fde52ef2d2e93ade759184f530bbca4ed38e3708342338dac7d56ce94eb3c3cb30ef2e3d10a7b8ab796dee40e8e4
  languageName: node
  linkType: hard

"vega-scale@npm:^7.4.2, vega-scale@npm:~7.4.2":
  version: 7.4.2
  resolution: "vega-scale@npm:7.4.2"
  dependencies:
    d3-array: ^3.2.2
    d3-interpolate: ^3.0.1
    d3-scale: ^4.0.2
    d3-scale-chromatic: ^3.1.0
    vega-time: ^2.1.3
    vega-util: ^1.17.3
  checksum: 3168ad4e9e80075f8d521947070225906085bdfeafe774c31f7549b3b80d31d5cb3f94e9cd8d408098bfe6431347fa95ae7654b006d7893f33057a789cc75156
  languageName: node
  linkType: hard

"vega-scenegraph@npm:^4.13.1, vega-scenegraph@npm:~4.13.1":
  version: 4.13.1
  resolution: "vega-scenegraph@npm:4.13.1"
  dependencies:
    d3-path: ^3.1.0
    d3-shape: ^3.2.0
    vega-canvas: ^1.2.7
    vega-loader: ^4.5.3
    vega-scale: ^7.4.2
    vega-util: ^1.17.3
  checksum: 66ef0e8fd50f3794aece2f951c2c092518e52448b4ecd334cca944e1161b3c1b9680b8743ca0358c1b3e92e1b81aaa6ce5042dce5c5a20e3bcdd22613378b7d0
  languageName: node
  linkType: hard

"vega-schema-url-parser@npm:^2.2.0":
  version: 2.2.0
  resolution: "vega-schema-url-parser@npm:2.2.0"
  checksum: 1ab17cde0a2514f42cfd0a1a19c7451e104025c68c09a15c9fe6a0f09bcc7b1c814a8a40f28ab5a69f3c9bda9824ca3f553a7b4338c5c64f7072edcd7bc3d130
  languageName: node
  linkType: hard

"vega-selections@npm:^5.6.0":
  version: 5.6.0
  resolution: "vega-selections@npm:5.6.0"
  dependencies:
    d3-array: 3.2.4
    vega-expression: ^5.2.0
    vega-util: ^1.17.3
  checksum: 6533b003fbc4cd523ae5c7650e5e905007fe37ac3db01ad6f652b62b3ab6a846441ddd38018a7e13cf275e176d92c193e2100c02aac8f03788f51326aeafd5fd
  languageName: node
  linkType: hard

"vega-statistics@npm:^1.9.0, vega-statistics@npm:~1.9.0":
  version: 1.9.0
  resolution: "vega-statistics@npm:1.9.0"
  dependencies:
    d3-array: ^3.2.2
  checksum: bbf2ea088c5a6a662c6aed1bf57996c06a82a98228730ada8a97e57824a6ed391999ea974f16dcde6e73bf88799976d91aff748842848d38ab45dbb9fafba3f9
  languageName: node
  linkType: hard

"vega-themes@npm:^2.12.1":
  version: 2.12.1
  resolution: "vega-themes@npm:2.12.1"
  peerDependencies:
    vega: "*"
    vega-lite: "*"
  checksum: 2ef22dc8e25c579b27d94d41df0058660a14fbf2d68ce53da08f7e6cc0afdf6cde020b92fddd1a00fbc286e6a95d8d4813a6828c6a9ad8b4a3b5aa000e0db418
  languageName: node
  linkType: hard

"vega-time@npm:^2.1.3, vega-time@npm:~2.1.3":
  version: 2.1.3
  resolution: "vega-time@npm:2.1.3"
  dependencies:
    d3-array: ^3.2.2
    d3-time: ^3.1.0
    vega-util: ^1.17.3
  checksum: c2a72ac6593ea1fabab95b72ce33b89a6ce2161330bcbaaa6508d61bbcd7edc940a9e76ae6d9a979be9d7c81eef4b26cd3592209bac201eca7356dfea7cc38ac
  languageName: node
  linkType: hard

"vega-tooltip@npm:^0.30.1":
  version: 0.30.1
  resolution: "vega-tooltip@npm:0.30.1"
  dependencies:
    vega-util: ^1.17.0
  checksum: 427dd60459334716af3c7f13ed5dde1b9643d512b68e1e7ef750f6675f1305e48cce3e57469dc83300d7289035dd3f30953dca541dde98063c4f0937fab60acb
  languageName: node
  linkType: hard

"vega-transforms@npm:~4.12.1":
  version: 4.12.1
  resolution: "vega-transforms@npm:4.12.1"
  dependencies:
    d3-array: ^3.2.2
    vega-dataflow: ^5.7.7
    vega-statistics: ^1.9.0
    vega-time: ^2.1.3
    vega-util: ^1.17.3
  checksum: 60489c52f7a50d52fb0f226c5e958298bbb8e34fd0f0faec17edd1c0de193436b22b308e4e71a12be1015f1fe3adc90f6474905c8ae0083be97e744b8f9d085e
  languageName: node
  linkType: hard

"vega-typings@npm:~1.5.0":
  version: 1.5.0
  resolution: "vega-typings@npm:1.5.0"
  dependencies:
    "@types/geojson": 7946.0.4
    vega-event-selector: ^3.0.1
    vega-expression: ^5.2.0
    vega-util: ^1.17.3
  checksum: 847999836a15bfaebfd60208114eca6ca9262d0d67abfbc51d05932cd879017dcec5190fb1131a00e1060a6eb8672d975889cfca782ef2e99dc7a23c4cc49acf
  languageName: node
  linkType: hard

"vega-util@npm:^1.17.0, vega-util@npm:^1.17.1, vega-util@npm:^1.17.3, vega-util@npm:~1.17.0, vega-util@npm:~1.17.2":
  version: 1.17.3
  resolution: "vega-util@npm:1.17.3"
  checksum: d8bb21e2cb2ffa005bc3d9859d13aca8a0f13d6a143b8e12598c307de011ce1bc947402769e735ceb62d3b4e648214bdc00664aea1d819ad56563090e96d44b5
  languageName: node
  linkType: hard

"vega-view-transforms@npm:~4.6.1":
  version: 4.6.1
  resolution: "vega-view-transforms@npm:4.6.1"
  dependencies:
    vega-dataflow: ^5.7.7
    vega-scenegraph: ^4.13.1
    vega-util: ^1.17.3
  checksum: 36c06f3feb018d9f86546cd329392dce19699308f6fde3c931805dffa805e3dbf2d55b1f76831d7a754067645281d3ada51910e190b9c08e7111932f14cc2031
  languageName: node
  linkType: hard

"vega-view@npm:~5.16.0":
  version: 5.16.0
  resolution: "vega-view@npm:5.16.0"
  dependencies:
    d3-array: ^3.2.2
    d3-timer: ^3.0.1
    vega-dataflow: ^5.7.7
    vega-format: ^1.1.3
    vega-functions: ^5.18.0
    vega-runtime: ^6.2.1
    vega-scenegraph: ^4.13.1
    vega-util: ^1.17.3
  checksum: e17d2a476969d12e72520bfd32d3fb83cf011655e271fcd8d3640f3903b21c26798d43206961e39f87b9335311fd6324d760fad38c4617fffea65f9774dd3820
  languageName: node
  linkType: hard

"vega-voronoi@npm:~4.2.4":
  version: 4.2.4
  resolution: "vega-voronoi@npm:4.2.4"
  dependencies:
    d3-delaunay: ^6.0.2
    vega-dataflow: ^5.7.7
    vega-util: ^1.17.3
  checksum: 66a6664d74973a28ecbf0eb1aba4c6c290eb8ccda670b61e2d3d10fefed28ff5de71f0800850f01b7c18032031f5501e833ce3edcc2278c5449401809d185be6
  languageName: node
  linkType: hard

"vega-wordcloud@npm:~4.1.6":
  version: 4.1.6
  resolution: "vega-wordcloud@npm:4.1.6"
  dependencies:
    vega-canvas: ^1.2.7
    vega-dataflow: ^5.7.7
    vega-scale: ^7.4.2
    vega-statistics: ^1.9.0
    vega-util: ^1.17.3
  checksum: 5f0cd55d6d4fcec1345be496eec57b38cd78b8294d4f88ea0cacc6241fc64543b50f4a3165640f93e73cf0a3d8866218673ce3720a467741c778a413899207e8
  languageName: node
  linkType: hard

"vega@npm:^5.20.0":
  version: 5.33.0
  resolution: "vega@npm:5.33.0"
  dependencies:
    vega-crossfilter: ~4.1.3
    vega-dataflow: ~5.7.7
    vega-encode: ~4.10.2
    vega-event-selector: ~3.0.1
    vega-expression: ~5.2.0
    vega-force: ~4.2.2
    vega-format: ~1.1.3
    vega-functions: ~5.18.0
    vega-geo: ~4.4.3
    vega-hierarchy: ~4.1.3
    vega-label: ~1.3.1
    vega-loader: ~4.5.3
    vega-parser: ~6.6.0
    vega-projection: ~1.6.2
    vega-regression: ~1.3.1
    vega-runtime: ~6.2.1
    vega-scale: ~7.4.2
    vega-scenegraph: ~4.13.1
    vega-statistics: ~1.9.0
    vega-time: ~2.1.3
    vega-transforms: ~4.12.1
    vega-typings: ~1.5.0
    vega-util: ~1.17.2
    vega-view: ~5.16.0
    vega-view-transforms: ~4.6.1
    vega-voronoi: ~4.2.4
    vega-wordcloud: ~4.1.6
  checksum: c8adb982b8852d0f5b55d8929302635d51d8155c2bbec477d2ee5635e6705a59f3a4efc472c9e74d6d5ab18c8ff3d3a1304219706a32129ac147e2cca13a4d91
  languageName: node
  linkType: hard

"verdaccio-audit@npm:13.0.0-next-8.1":
  version: 13.0.0-next-8.1
  resolution: "verdaccio-audit@npm:13.0.0-next-8.1"
  dependencies:
    "@verdaccio/config": 8.0.0-next-8.1
    "@verdaccio/core": 8.0.0-next-8.1
    express: 4.21.0
    https-proxy-agent: 5.0.1
    node-fetch: cjs
  checksum: 930fe9bfc782601664504688547444d9de167046ce8d0d24d113de4881d3b1507cd5293a8edb5285880ae796ea94c6f7d50e09148d519e8700df057dcf41d1d9
  languageName: node
  linkType: hard

"verdaccio-htpasswd@npm:13.0.0-next-8.1":
  version: 13.0.0-next-8.1
  resolution: "verdaccio-htpasswd@npm:13.0.0-next-8.1"
  dependencies:
    "@verdaccio/core": 8.0.0-next-8.1
    "@verdaccio/file-locking": 13.0.0-next-8.0
    apache-md5: 1.1.8
    bcryptjs: 2.4.3
    core-js: 3.37.1
    debug: 4.3.7
    http-errors: 2.0.0
    unix-crypt-td-js: 1.1.4
  checksum: d637d5ba6af5b74a2cf477235677b6cb6fdaf51aed1f96bb5b5b3faa0780055ac180c6225e4783f75e16848166f9223fb71fbe6606e84b31d96392356c94d0a9
  languageName: node
  linkType: hard

"verdaccio@npm:^5.33.0":
  version: 5.33.0
  resolution: "verdaccio@npm:5.33.0"
  dependencies:
    "@cypress/request": 3.0.6
    "@verdaccio/auth": 8.0.0-next-8.1
    "@verdaccio/config": 8.0.0-next-8.1
    "@verdaccio/core": 8.0.0-next-8.1
    "@verdaccio/local-storage-legacy": 11.0.2
    "@verdaccio/logger-7": 8.0.0-next-8.1
    "@verdaccio/middleware": 8.0.0-next-8.1
    "@verdaccio/search-indexer": 8.0.0-next-8.0
    "@verdaccio/signature": 8.0.0-next-8.0
    "@verdaccio/streams": 10.2.1
    "@verdaccio/tarball": 13.0.0-next-8.1
    "@verdaccio/ui-theme": 8.0.0-next-8.1
    "@verdaccio/url": 13.0.0-next-8.1
    "@verdaccio/utils": 7.0.1-next-8.1
    JSONStream: 1.3.5
    async: 3.2.6
    clipanion: 4.0.0-rc.4
    compression: 1.7.5
    cors: 2.8.5
    debug: ^4.3.7
    envinfo: 7.14.0
    express: 4.21.1
    express-rate-limit: 5.5.1
    fast-safe-stringify: 2.1.1
    handlebars: 4.7.8
    js-yaml: 4.1.0
    jsonwebtoken: 9.0.2
    kleur: 4.1.5
    lodash: 4.17.21
    lru-cache: 7.18.3
    mime: 3.0.0
    mkdirp: 1.0.4
    mv: 2.1.1
    pkginfo: 0.4.1
    semver: 7.6.3
    validator: 13.12.0
    verdaccio-audit: 13.0.0-next-8.1
    verdaccio-htpasswd: 13.0.0-next-8.1
  bin:
    verdaccio: bin/verdaccio
  checksum: 0474cccb9e788f356468fe7227f3e2faa7cb594b1a30785e1e7516ef7c8486216abd22208ab15427ab7b274f6adee4ed1cbde064bf29d631d27693f0db67d35e
  languageName: node
  linkType: hard

"verror@npm:1.10.0":
  version: 1.10.0
  resolution: "verror@npm:1.10.0"
  dependencies:
    assert-plus: ^1.0.0
    core-util-is: 1.0.2
    extsprintf: ^1.2.0
  checksum: c431df0bedf2088b227a4e051e0ff4ca54df2c114096b0c01e1cbaadb021c30a04d7dd5b41ab277bcd51246ca135bf931d4c4c796ecae7a4fef6d744ecef36ea
  languageName: node
  linkType: hard

"vscode-jsonrpc@npm:8.2.0, vscode-jsonrpc@npm:^8.0.2":
  version: 8.2.0
  resolution: "vscode-jsonrpc@npm:8.2.0"
  checksum: f302a01e59272adc1ae6494581fa31c15499f9278df76366e3b97b2236c7c53ebfc71efbace9041cfd2caa7f91675b9e56f2407871a1b3c7f760a2e2ee61484a
  languageName: node
  linkType: hard

"vscode-jsonrpc@npm:^6.0.0":
  version: 6.0.0
  resolution: "vscode-jsonrpc@npm:6.0.0"
  checksum: 3a67a56f287e8c449f2d9752eedf91e704dc7b9a326f47fb56ac07667631deb45ca52192e9bccb2ab108764e48409d70fa64b930d46fc3822f75270b111c5f53
  languageName: node
  linkType: hard

"vscode-languageserver-protocol@npm:3.17.5, vscode-languageserver-protocol@npm:^3.17.0":
  version: 3.17.5
  resolution: "vscode-languageserver-protocol@npm:3.17.5"
  dependencies:
    vscode-jsonrpc: 8.2.0
    vscode-languageserver-types: 3.17.5
  checksum: dfb42d276df5dfea728267885b99872ecff62f6c20448b8539fae71bb196b420f5351c5aca7c1047bf8fb1f89fa94a961dce2bc5bf7e726198f4be0bb86a1e71
  languageName: node
  linkType: hard

"vscode-languageserver-textdocument@npm:~1.0.11":
  version: 1.0.12
  resolution: "vscode-languageserver-textdocument@npm:1.0.12"
  checksum: 49415c8f065860693fdd6cb0f7b8a24470130dc941e887a396b6e6bbae93be132323a644aa1edd7d0eec38a730e05a2d013aebff6bddd30c5af374ef3f4cd9ab
  languageName: node
  linkType: hard

"vscode-languageserver-types@npm:3.17.5":
  version: 3.17.5
  resolution: "vscode-languageserver-types@npm:3.17.5"
  checksum: 79b420e7576398d396579ca3a461c9ed70e78db4403cd28bbdf4d3ed2b66a2b4114031172e51fad49f0baa60a2180132d7cb2ea35aa3157d7af3c325528210ac
  languageName: node
  linkType: hard

"vscode-languageserver@npm:~9.0.1":
  version: 9.0.1
  resolution: "vscode-languageserver@npm:9.0.1"
  dependencies:
    vscode-languageserver-protocol: 3.17.5
  bin:
    installServerIntoExtension: bin/installServerIntoExtension
  checksum: 8b7dfda47fb64c3f48a9dabd3f01938cc8d39f3f068f1ee586eaf0a373536180a1047bdde8d876f965cfc04160d1587e99828b61b742b0342595fee67c8814ea
  languageName: node
  linkType: hard

"vscode-uri@npm:~3.0.8":
  version: 3.0.8
  resolution: "vscode-uri@npm:3.0.8"
  checksum: 514249126850c0a41a7d8c3c2836cab35983b9dc1938b903cfa253b9e33974c1416d62a00111385adcfa2b98df456437ab704f709a2ecca76a90134ef5eb4832
  languageName: node
  linkType: hard

"vscode-ws-jsonrpc@npm:~1.0.2":
  version: 1.0.2
  resolution: "vscode-ws-jsonrpc@npm:1.0.2"
  dependencies:
    vscode-jsonrpc: ^8.0.2
  checksum: eb2fdb5c96f124326505f06564dfc6584318b748fd6e39b4c0ba16a0d383d13ba0e9433596abdb841428dfc2a5501994c3206723d1cb38c6af5fcac1faf4be26
  languageName: node
  linkType: hard

"w3c-keyname@npm:^2.2.4":
  version: 2.2.6
  resolution: "w3c-keyname@npm:2.2.6"
  checksum: 59a31d23ca9953c01c99ed6695fee5b6ea36eb2412d76a21fe4302ab33a3f5cd96c006a763940b6115c3d042c16d3564eeee1156832217d028af0518098b3a42
  languageName: node
  linkType: hard

"watchpack@npm:^2.4.1":
  version: 2.4.2
  resolution: "watchpack@npm:2.4.2"
  dependencies:
    glob-to-regexp: ^0.4.1
    graceful-fs: ^4.1.2
  checksum: 92d9d52ce3d16fd83ed6994d1dd66a4d146998882f4c362d37adfea9ab77748a5b4d1e0c65fa104797928b2d40f635efa8f9b925a6265428a69f1e1852ca3441
  languageName: node
  linkType: hard

"wcwidth@npm:^1.0.1":
  version: 1.0.1
  resolution: "wcwidth@npm:1.0.1"
  dependencies:
    defaults: ^1.0.3
  checksum: 814e9d1ddcc9798f7377ffa448a5a3892232b9275ebb30a41b529607691c0491de47cba426e917a4d08ded3ee7e9ba2f3fe32e62ee3cd9c7d3bafb7754bd553c
  languageName: node
  linkType: hard

"webidl-conversions@npm:^3.0.0":
  version: 3.0.1
  resolution: "webidl-conversions@npm:3.0.1"
  checksum: c92a0a6ab95314bde9c32e1d0a6dfac83b578f8fa5f21e675bc2706ed6981bc26b7eb7e6a1fab158e5ce4adf9caa4a0aee49a52505d4d13c7be545f15021b17c
  languageName: node
  linkType: hard

"webidl-conversions@npm:^6.1.0":
  version: 6.1.0
  resolution: "webidl-conversions@npm:6.1.0"
  checksum: 1f526507aa491f972a0c1409d07f8444e1d28778dfa269a9971f2e157182f3d496dc33296e4ed45b157fdb3bf535bb90c90bf10c50dcf1dd6caacb2a34cc84fb
  languageName: node
  linkType: hard

"webpack-bundle-analyzer@npm:^4.8.0":
  version: 4.8.0
  resolution: "webpack-bundle-analyzer@npm:4.8.0"
  dependencies:
    "@discoveryjs/json-ext": 0.5.7
    acorn: ^8.0.4
    acorn-walk: ^8.0.0
    chalk: ^4.1.0
    commander: ^7.2.0
    gzip-size: ^6.0.0
    lodash: ^4.17.20
    opener: ^1.5.2
    sirv: ^1.0.7
    ws: ^7.3.1
  bin:
    webpack-bundle-analyzer: lib/bin/analyzer.js
  checksum: acd86f68abb2bcb1a240043c6e2d8e53079499363afed94b96c0ec1abcc4fca2b7a7cbeeb5e13027d02a993c6ea8153194c69e7697faf47528bdaff1e2ce297e
  languageName: node
  linkType: hard

"webpack-cli@npm:^5.0.1":
  version: 5.0.1
  resolution: "webpack-cli@npm:5.0.1"
  dependencies:
    "@discoveryjs/json-ext": ^0.5.0
    "@webpack-cli/configtest": ^2.0.1
    "@webpack-cli/info": ^2.0.1
    "@webpack-cli/serve": ^2.0.1
    colorette: ^2.0.14
    commander: ^9.4.1
    cross-spawn: ^7.0.3
    envinfo: ^7.7.3
    fastest-levenshtein: ^1.0.12
    import-local: ^3.0.2
    interpret: ^3.1.1
    rechoir: ^0.8.0
    webpack-merge: ^5.7.3
  peerDependencies:
    webpack: 5.x.x
  peerDependenciesMeta:
    "@webpack-cli/generators":
      optional: true
    webpack-bundle-analyzer:
      optional: true
    webpack-dev-server:
      optional: true
  bin:
    webpack-cli: bin/cli.js
  checksum: b1544eea669442e78c3dba9f79c0f8d0136759b8b2fe9cd32c0d410250fd719988ae037778ba88993215d44971169f2c268c0c934068be561711615f1951bd53
  languageName: node
  linkType: hard

"webpack-merge@npm:^5.7.3, webpack-merge@npm:^5.8.0":
  version: 5.8.0
  resolution: "webpack-merge@npm:5.8.0"
  dependencies:
    clone-deep: ^4.0.1
    wildcard: ^2.0.0
  checksum: 88786ab91013f1bd2a683834ff381be81c245a4b0f63304a5103e90f6653f44dab496a0768287f8531761f8ad957d1f9f3ccb2cb55df0de1bd9ee343e079da26
  languageName: node
  linkType: hard

"webpack-sources@npm:^1.2.0":
  version: 1.4.3
  resolution: "webpack-sources@npm:1.4.3"
  dependencies:
    source-list-map: ^2.0.0
    source-map: ~0.6.1
  checksum: 37463dad8d08114930f4bc4882a9602941f07c9f0efa9b6bc78738cd936275b990a596d801ef450d022bb005b109b9f451dd087db2f3c9baf53e8e22cf388f79
  languageName: node
  linkType: hard

"webpack-sources@npm:^3.0.0, webpack-sources@npm:^3.2.3":
  version: 3.2.3
  resolution: "webpack-sources@npm:3.2.3"
  checksum: 989e401b9fe3536529e2a99dac8c1bdc50e3a0a2c8669cbafad31271eadd994bc9405f88a3039cd2e29db5e6d9d0926ceb7a1a4e7409ece021fe79c37d9c4607
  languageName: node
  linkType: hard

"webpack@npm:^5.76.1":
  version: 5.94.0
  resolution: "webpack@npm:5.94.0"
  dependencies:
    "@types/estree": ^1.0.5
    "@webassemblyjs/ast": ^1.12.1
    "@webassemblyjs/wasm-edit": ^1.12.1
    "@webassemblyjs/wasm-parser": ^1.12.1
    acorn: ^8.7.1
    acorn-import-attributes: ^1.9.5
    browserslist: ^4.21.10
    chrome-trace-event: ^1.0.2
    enhanced-resolve: ^5.17.1
    es-module-lexer: ^1.2.1
    eslint-scope: 5.1.1
    events: ^3.2.0
    glob-to-regexp: ^0.4.1
    graceful-fs: ^4.2.11
    json-parse-even-better-errors: ^2.3.1
    loader-runner: ^4.2.0
    mime-types: ^2.1.27
    neo-async: ^2.6.2
    schema-utils: ^3.2.0
    tapable: ^2.1.1
    terser-webpack-plugin: ^5.3.10
    watchpack: ^2.4.1
    webpack-sources: ^3.2.3
  peerDependenciesMeta:
    webpack-cli:
      optional: true
  bin:
    webpack: bin/webpack.js
  checksum: 6a3d667be304a69cd6dcb8d676bc29f47642c0d389af514cfcd646eaaa809961bc6989fc4b2621a717dfc461130f29c6e20006d62a32e012dafaa9517813a4e6
  languageName: node
  linkType: hard

"whatwg-mimetype@npm:^2.3.0":
  version: 2.3.0
  resolution: "whatwg-mimetype@npm:2.3.0"
  checksum: 23eb885940bcbcca4ff841c40a78e9cbb893ec42743993a42bf7aed16085b048b44b06f3402018931687153550f9a32d259dfa524e4f03577ab898b6965e5383
  languageName: node
  linkType: hard

"whatwg-url@npm:^5.0.0":
  version: 5.0.0
  resolution: "whatwg-url@npm:5.0.0"
  dependencies:
    tr46: ~0.0.3
    webidl-conversions: ^3.0.0
  checksum: b8daed4ad3356cc4899048a15b2c143a9aed0dfae1f611ebd55073310c7b910f522ad75d727346ad64203d7e6c79ef25eafd465f4d12775ca44b90fa82ed9e2c
  languageName: node
  linkType: hard

"whatwg-url@npm:^8.0.0":
  version: 8.7.0
  resolution: "whatwg-url@npm:8.7.0"
  dependencies:
    lodash: ^4.7.0
    tr46: ^2.1.0
    webidl-conversions: ^6.1.0
  checksum: a87abcc6cefcece5311eb642858c8fdb234e51ec74196bfacf8def2edae1bfbffdf6acb251646ed6301f8cee44262642d8769c707256125a91387e33f405dd1e
  languageName: node
  linkType: hard

"which@npm:^2.0.1, which@npm:^2.0.2":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: ^2.0.0
  bin:
    node-which: ./bin/node-which
  checksum: 1a5c563d3c1b52d5f893c8b61afe11abc3bab4afac492e8da5bde69d550de701cf9806235f20a47b5c8fa8a1d6a9135841de2596535e998027a54589000e66d1
  languageName: node
  linkType: hard

"wicked-good-xpath@npm:1.3.0":
  version: 1.3.0
  resolution: "wicked-good-xpath@npm:1.3.0"
  checksum: 1aa84bd57426aa07f95d7eca0b0410e841b8e7a35248c9404fa235eaf6a0932c811a96cbdc763c3df18ab76c7644fd8e807d8f185146154d3fc6baf554dcc7e3
  languageName: node
  linkType: hard

"wide-align@npm:^1.1.5":
  version: 1.1.5
  resolution: "wide-align@npm:1.1.5"
  dependencies:
    string-width: ^1.0.2 || 2 || 3 || 4
  checksum: d5fc37cd561f9daee3c80e03b92ed3e84d80dde3365a8767263d03dacfc8fa06b065ffe1df00d8c2a09f731482fcacae745abfbb478d4af36d0a891fad4834d3
  languageName: node
  linkType: hard

"wildcard@npm:^2.0.0":
  version: 2.0.0
  resolution: "wildcard@npm:2.0.0"
  checksum: 1f4fe4c03dfc492777c60f795bbba597ac78794f1b650d68f398fbee9adb765367c516ebd4220889b6a81e9626e7228bbe0d66237abb311573c2ee1f4902a5ad
  languageName: node
  linkType: hard

"wordwrap@npm:^1.0.0":
  version: 1.0.0
  resolution: "wordwrap@npm:1.0.0"
  checksum: 2a44b2788165d0a3de71fd517d4880a8e20ea3a82c080ce46e294f0b68b69a2e49cff5f99c600e275c698a90d12c5ea32aff06c311f0db2eb3f1201f3e7b2a04
  languageName: node
  linkType: hard

"worker-loader@npm:^3.0.2":
  version: 3.0.8
  resolution: "worker-loader@npm:3.0.8"
  dependencies:
    loader-utils: ^2.0.0
    schema-utils: ^3.0.0
  peerDependencies:
    webpack: ^4.0.0 || ^5.0.0
  checksum: 84f4a7eeb2a1d8b9704425837e017c91eedfae67ac89e0b866a2dcf283323c1dcabe0258196278b7d5fd0041392da895c8a0c59ddf3a94f1b2e003df68ddfec3
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0, wrap-ansi@npm:^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: ^4.0.0
    string-width: ^4.1.0
    strip-ansi: ^6.0.0
  checksum: a790b846fd4505de962ba728a21aaeda189b8ee1c7568ca5e817d85930e06ef8d1689d49dbf0e881e8ef84436af3a88bc49115c2e2788d841ff1b8b5b51a608b
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.0.1, wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: ^6.1.0
    string-width: ^5.0.1
    strip-ansi: ^7.0.1
  checksum: 371733296dc2d616900ce15a0049dca0ef67597d6394c57347ba334393599e800bab03c41d4d45221b6bc967b8c453ec3ae4749eff3894202d16800fdfe0e238
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 159da4805f7e84a3d003d8841557196034155008f817172d4e986bd591f74aa82aa7db55929a54222309e01079a65a92a9e6414da5a6aa4b01ee44a511ac3ee5
  languageName: node
  linkType: hard

"ws@npm:^7.3.1":
  version: 7.5.10
  resolution: "ws@npm:7.5.10"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ^5.0.2
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: f9bb062abf54cc8f02d94ca86dcd349c3945d63851f5d07a3a61c2fcb755b15a88e943a63cf580cbdb5b74436d67ef6b67f745b8f7c0814e411379138e1863cb
  languageName: node
  linkType: hard

"ws@npm:^8.11.0":
  version: 8.17.1
  resolution: "ws@npm:8.17.1"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 442badcce1f1178ec87a0b5372ae2e9771e07c4929a3180321901f226127f252441e8689d765aa5cfba5f50ac60dd830954afc5aeae81609aefa11d3ddf5cecf
  languageName: node
  linkType: hard

"xmldom-sre@npm:0.1.31":
  version: 0.1.31
  resolution: "xmldom-sre@npm:0.1.31"
  checksum: dbd101600a64c1640b06fb2b5c626ce6d909fd40c966fcae84a2b64c708fe466630766173b5760e0275db2a2c542e048b14a2a6568feece2c315f0cd22a2f642
  languageName: node
  linkType: hard

"xtend@npm:~4.0.1":
  version: 4.0.2
  resolution: "xtend@npm:4.0.2"
  checksum: ac5dfa738b21f6e7f0dd6e65e1b3155036d68104e67e5d5d1bde74892e327d7e5636a076f625599dc394330a731861e87343ff184b0047fef1360a7ec0a5a36a
  languageName: node
  linkType: hard

"y-protocols@npm:^1.0.5":
  version: 1.0.5
  resolution: "y-protocols@npm:1.0.5"
  dependencies:
    lib0: ^0.2.42
  checksum: d19404a4ebafcf3761c28b881abe8c32ab6e457db0e5ffc7dbb749cbc2c3bb98e003a43f3e8eba7f245b2698c76f2c4cdd1c2db869f8ec0c6ef94736d9a88652
  languageName: node
  linkType: hard

"y18n@npm:^5.0.5":
  version: 5.0.8
  resolution: "y18n@npm:5.0.8"
  checksum: 54f0fb95621ee60898a38c572c515659e51cc9d9f787fb109cef6fde4befbe1c4602dc999d30110feee37456ad0f1660fa2edcfde6a9a740f86a290999550d30
  languageName: node
  linkType: hard

"yallist@npm:*, yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 343617202af32df2a15a3be36a5a8c0c8545208f3d3dfbc6bb7c3e3b7e8c6f8e7485432e4f3b88da3031a6e20afa7c711eded32ddfb122896ac5d914e75848d5
  languageName: node
  linkType: hard

"yargs-parser@npm:^21.1.1":
  version: 21.1.1
  resolution: "yargs-parser@npm:21.1.1"
  checksum: ed2d96a616a9e3e1cc7d204c62ecc61f7aaab633dcbfab2c6df50f7f87b393993fe6640d017759fe112d0cb1e0119f2b4150a87305cc873fd90831c6a58ccf1c
  languageName: node
  linkType: hard

"yargs@npm:~17.6.2":
  version: 17.6.2
  resolution: "yargs@npm:17.6.2"
  dependencies:
    cliui: ^8.0.1
    escalade: ^3.1.1
    get-caller-file: ^2.0.5
    require-directory: ^2.1.1
    string-width: ^4.2.3
    y18n: ^5.0.5
    yargs-parser: ^21.1.1
  checksum: 47da1b0d854fa16d45a3ded57b716b013b2179022352a5f7467409da5a04a1eef5b3b3d97a2dfc13e8bbe5f2ffc0afe3bc6a4a72f8254e60f5a4bd7947138643
  languageName: node
  linkType: hard

"yjs@npm:^13.5.40":
  version: 13.5.49
  resolution: "yjs@npm:13.5.49"
  dependencies:
    lib0: ^0.2.49
  checksum: d5e0ec9ce055cff0abf6335106ee6b946948995cf29ae2a1702070e1d4418e62bdf1a73348a6f552c467181a8a5bb4c7d72f1acaaacd27eac5977014ffd2a210
  languageName: node
  linkType: hard
