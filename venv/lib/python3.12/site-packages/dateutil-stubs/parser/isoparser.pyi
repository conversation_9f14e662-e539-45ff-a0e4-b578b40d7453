from _typeshed import SupportsRead
from datetime import date, datetime, time, tzinfo
from typing_extensions import TypeAlias

_Readable: TypeAlias = SupportsRead[str | bytes]
_TakesAscii: TypeAlias = str | bytes | _Readable

__all__ = ["isoparse", "isoparser"]

class isoparser:
    def __init__(self, sep: str | bytes | None = None): ...
    def isoparse(self, dt_str: _TakesAscii) -> datetime: ...
    def parse_isodate(self, datestr: _TakesAscii) -> date: ...
    def parse_isotime(self, timestr: _TakesAscii) -> time: ...
    def parse_tzstr(self, tzstr: _TakesAscii, zero_as_utc: bool = True) -> tzinfo: ...

def isoparse(dt_str: _TakesAscii) -> datetime: ...
