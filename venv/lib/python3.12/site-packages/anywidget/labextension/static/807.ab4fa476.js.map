{"version": 3, "file": "807.ab4fa476.js", "sources": ["webpack://@anywidget/monorepo/./packages/anywidget/src/widget.js", "webpack://@anywidget/monorepo/./packages/anywidget/src/plugin.js"], "sourcesContent": ["import * as uuid from \"@lukeed/uuid\";\nimport * as solid from \"solid-js\";\n\n/** @import * as base from \"@jupyter-widgets/base\" */\n/** @import { Initialize, Render, AnyModel } from \"@anywidget/types\" */\n\n/**\n * @template T\n * @typedef {T | PromiseLike<T>} Awaitable\n */\n\n/**\n * @typedef AnyWidget\n * @prop initialize {Initialize}\n * @prop render {Render}\n */\n\n/**\n *  @typedef AnyWidgetModule\n *  @prop render {Render=}\n *  @prop default {AnyWidget | (() => AnyWidget | Promise<AnyWidget>)=}\n */\n\n/**\n * @param {unknown} condition\n * @param {string} message\n * @returns {asserts condition}\n */\nfunction assert(condition, message) {\n\tif (!condition) throw new Error(message);\n}\n\n/**\n * @param {string} str\n * @returns {str is \"https://${string}\" | \"http://${string}\"}\n */\nfunction is_href(str) {\n\treturn str.startsWith(\"http://\") || str.startsWith(\"https://\");\n}\n\n/**\n * @param {string} href\n * @param {string} anywidget_id\n * @returns {Promise<void>}\n */\nasync function load_css_href(href, anywidget_id) {\n\t/** @type {HTMLLinkElement | null} */\n\tlet prev = document.querySelector(`link[id='${anywidget_id}']`);\n\n\t// Adapted from https://github.com/vitejs/vite/blob/d59e1acc2efc0307488364e9f2fad528ec57f204/packages/vite/src/client/client.ts#L185-L201\n\t// Swaps out old styles with new, but avoids flash of unstyled content.\n\t// No need to await the load since we already have styles applied.\n\tif (prev) {\n\t\tlet newLink = /** @type {HTMLLinkElement} */ (prev.cloneNode());\n\t\tnewLink.href = href;\n\t\tnewLink.addEventListener(\"load\", () => prev?.remove());\n\t\tnewLink.addEventListener(\"error\", () => prev?.remove());\n\t\tprev.after(newLink);\n\t\treturn;\n\t}\n\n\treturn new Promise((resolve) => {\n\t\tlet link = Object.assign(document.createElement(\"link\"), {\n\t\t\trel: \"stylesheet\",\n\t\t\thref,\n\t\t\tonload: resolve,\n\t\t});\n\t\tdocument.head.appendChild(link);\n\t});\n}\n\n/**\n * @param {string} css_text\n * @param {string} anywidget_id\n * @returns {void}\n */\nfunction load_css_text(css_text, anywidget_id) {\n\t/** @type {HTMLStyleElement | null} */\n\tlet prev = document.querySelector(`style[id='${anywidget_id}']`);\n\tif (prev) {\n\t\t// replace instead of creating a new DOM node\n\t\tprev.textContent = css_text;\n\t\treturn;\n\t}\n\tlet style = Object.assign(document.createElement(\"style\"), {\n\t\tid: anywidget_id,\n\t\ttype: \"text/css\",\n\t});\n\tstyle.appendChild(document.createTextNode(css_text));\n\tdocument.head.appendChild(style);\n}\n\n/**\n * @param {string | undefined} css\n * @param {string} anywidget_id\n * @returns {Promise<void>}\n */\nasync function load_css(css, anywidget_id) {\n\tif (!css || !anywidget_id) return;\n\tif (is_href(css)) return load_css_href(css, anywidget_id);\n\treturn load_css_text(css, anywidget_id);\n}\n\n/**\n * @param {string} esm\n * @returns {Promise<AnyWidgetModule>}\n */\nasync function load_esm(esm) {\n\tif (is_href(esm)) {\n\t\treturn await import(/* webpackIgnore: true */ /* @vite-ignore */ esm);\n\t}\n\tlet url = URL.createObjectURL(new Blob([esm], { type: \"text/javascript\" }));\n\tlet mod = await import(/* webpackIgnore: true */ /* @vite-ignore */ url);\n\tURL.revokeObjectURL(url);\n\treturn mod;\n}\n\n/** @param {string} anywidget_id */\nfunction warn_render_deprecation(anywidget_id) {\n\tconsole.warn(`\\\n[anywidget] Deprecation Warning for ${anywidget_id}: Direct export of a 'render' will likely be deprecated in the future. To migrate ...\n\nRemove the 'export' keyword from 'render'\n-----------------------------------------\n\nexport function render({ model, el }) { ... }\n^^^^^^\n\nCreate a default export that returns an object with 'render'\n------------------------------------------------------------\n\nfunction render({ model, el }) { ... }\n         ^^^^^^\nexport default { render }\n                 ^^^^^^\n\nPin to anywidget>=0.9.0 in your pyproject.toml\n----------------------------------------------\n\ndependencies = [\"anywidget>=0.9.0\"]\n\nTo learn more, please see: https://github.com/manzt/anywidget/pull/395.\n`);\n}\n\n/**\n * @param {string} esm\n * @param {string} anywidget_id\n * @returns {Promise<AnyWidget>}\n */\nasync function load_widget(esm, anywidget_id) {\n\tlet mod = await load_esm(esm);\n\tif (mod.render) {\n\t\twarn_render_deprecation(anywidget_id);\n\t\treturn {\n\t\t\tasync initialize() {},\n\t\t\trender: mod.render,\n\t\t};\n\t}\n\tassert(\n\t\tmod.default,\n\t\t`[anywidget] module must export a default function or object.`,\n\t);\n\tlet widget =\n\t\ttypeof mod.default === \"function\" ? await mod.default() : mod.default;\n\treturn widget;\n}\n\n/**\n * This is a trick so that we can cleanup event listeners added\n * by the user-defined function.\n */\nlet INITIALIZE_MARKER = Symbol(\"anywidget.initialize\");\n\n/**\n * @param {base.DOMWidgetModel} model\n * @param {unknown} context\n * @return {import(\"@anywidget/types\").AnyModel}\n *\n * Prunes the view down to the minimum context necessary.\n *\n * Calls to `model.get` and `model.set` automatically add the\n * `context`, so we can gracefully unsubscribe from events\n * added by user-defined hooks.\n */\nfunction model_proxy(model, context) {\n\treturn {\n\t\tget: model.get.bind(model),\n\t\tset: model.set.bind(model),\n\t\tsave_changes: model.save_changes.bind(model),\n\t\tsend: model.send.bind(model),\n\t\t// @ts-expect-error\n\t\ton(name, callback) {\n\t\t\tmodel.on(name, callback, context);\n\t\t},\n\t\toff(name, callback) {\n\t\t\tmodel.off(name, callback, context);\n\t\t},\n\t\t// @ts-expect-error - the widget_manager type is wider than what\n\t\t// we want to expose to developers.\n\t\t// In a future version, we will expose a more limited API but\n\t\t// that can wait for a minor version bump.\n\t\twidget_manager: model.widget_manager,\n\t};\n}\n\n/**\n * @param {void | (() => Awaitable<void>)} fn\n * @param {string} kind\n */\nasync function safe_cleanup(fn, kind) {\n\treturn Promise.resolve()\n\t\t.then(() => fn?.())\n\t\t.catch((e) => console.warn(`[anywidget] error cleaning up ${kind}.`, e));\n}\n\n/**\n * @template T\n * @typedef Ready\n * @property {\"ready\"} status\n * @property {T} data\n */\n\n/**\n * @typedef Pending\n * @property {\"pending\"} status\n */\n\n/**\n * @typedef Errored\n * @property {\"error\"} status\n * @property {unknown} error\n */\n\n/**\n * @template T\n * @typedef {Pending | Ready<T> | Errored} Result\n */\n\n/**\n * Cleans up the stack trace at anywidget boundary.\n * You can fully inspect the entire stack trace in the console interactively,\n * but the initial error message is cleaned up to be more user-friendly.\n *\n * @param {unknown} source\n */\nfunction throw_anywidget_error(source) {\n\tif (!(source instanceof Error)) {\n\t\t// Don't know what to do with this.\n\t\tthrow source;\n\t}\n\tlet lines = source.stack?.split(\"\\n\") ?? [];\n\tlet anywidget_index = lines.findIndex((line) => line.includes(\"anywidget\"));\n\tlet clean_stack =\n\t\tanywidget_index === -1 ? lines : lines.slice(0, anywidget_index + 1);\n\tsource.stack = clean_stack.join(\"\\n\");\n\tconsole.error(source);\n\tthrow source;\n}\n\n/**\n * @typedef InvokeOptions\n * @prop {DataView[]} [buffers]\n * @prop {AbortSignal} [signal]\n */\n\n/**\n * @template T\n * @param {import(\"@anywidget/types\").AnyModel} model\n * @param {string} name\n * @param {any} [msg]\n * @param {InvokeOptions} [options]\n * @return {Promise<[T, DataView[]]>}\n */\nexport function invoke(model, name, msg, options = {}) {\n\t// crypto.randomUUID() is not available in non-secure contexts (i.e., http://)\n\t// so we use simple (non-secure) polyfill.\n\tlet id = uuid.v4();\n\tlet signal = options.signal ?? AbortSignal.timeout(3000);\n\n\treturn new Promise((resolve, reject) => {\n\t\tif (signal.aborted) {\n\t\t\treject(signal.reason);\n\t\t}\n\t\tsignal.addEventListener(\"abort\", () => {\n\t\t\tmodel.off(\"msg:custom\", handler);\n\t\t\treject(signal.reason);\n\t\t});\n\n\t\t/**\n\t\t * @param {{ id: string, kind: \"anywidget-command-response\", response: T }} msg\n\t\t * @param {DataView[]} buffers\n\t\t */\n\t\tfunction handler(msg, buffers) {\n\t\t\tif (!(msg.id === id)) return;\n\t\t\tresolve([msg.response, buffers]);\n\t\t\tmodel.off(\"msg:custom\", handler);\n\t\t}\n\t\tmodel.on(\"msg:custom\", handler);\n\t\tmodel.send(\n\t\t\t{ id, kind: \"anywidget-command\", name, msg },\n\t\t\tundefined,\n\t\t\toptions.buffers ?? [],\n\t\t);\n\t});\n}\n\n/**\n * Polyfill for {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/withResolvers Promise.withResolvers}\n *\n * Trevor(2025-03-14): Should be able to remove once more stable across browsers.\n *\n * @template T\n * @returns {PromiseWithResolvers<T>}\n */\nfunction promise_with_resolvers() {\n\tlet resolve;\n\tlet reject;\n\tlet promise = new Promise((res, rej) => {\n\t\tresolve = res;\n\t\treject = rej;\n\t});\n\t// @ts-expect-error - We know these types are ok\n\treturn { promise, resolve, reject };\n}\n\n/**\n * @template {Record<string, unknown>} T\n * @template {keyof T & string} K\n * @param {AnyModel<T>} model\n * @param {K} name\n * @param {{ signal?: AbortSignal}} options\n * @returns {solid.Accessor<T[K]>}\n */\nfunction observe(model, name, { signal }) {\n\tlet [get, set] = solid.createSignal(model.get(name));\n\tlet update = () => set(() => model.get(name));\n\tmodel.on(`change:${name}`, update);\n\tsignal?.addEventListener(\"abort\", () => {\n\t\tmodel.off(`change:${name}`, update);\n\t});\n\treturn get;\n}\n\n/**\n * @typedef State\n * @property {string} _esm\n * @property {string} _anywidget_id\n * @property {string | undefined} _css\n */\n\nclass Runtime {\n\t/** @type {solid.Accessor<Result<AnyWidget>>} */\n\t// @ts-expect-error - Set synchronously in constructor.\n\t#widget_result;\n\t/** @type {AbortSignal} */\n\t#signal;\n\t/** @type {Promise<void>} */\n\tready;\n\n\t/**\n\t * @param {base.DOMWidgetModel} model\n\t * @param {{ signal: AbortSignal }} options\n\t */\n\tconstructor(model, options) {\n\t\t/** @type {PromiseWithResolvers<void>} */\n\t\tlet resolvers = promise_with_resolvers();\n\t\tthis.ready = resolvers.promise;\n\t\tthis.#signal = options.signal;\n\t\tthis.#signal.throwIfAborted();\n\t\tthis.#signal.addEventListener(\"abort\", () => dispose());\n\t\tAbortSignal.timeout(2000).addEventListener(\"abort\", () => {\n\t\t\tresolvers.reject(new Error(\"[anywidget] Failed to initialize model.\"));\n\t\t});\n\t\tlet dispose = solid.createRoot((dispose) => {\n\t\t\t/** @type {AnyModel<State>} */\n\t\t\t// @ts-expect-error - Types don't sufficiently overlap, so we cast here for type-safe access\n\t\t\tlet typed_model = model;\n\t\t\tlet id = typed_model.get(\"_anywidget_id\");\n\t\t\tlet css = observe(typed_model, \"_css\", { signal: this.#signal });\n\t\t\tlet esm = observe(typed_model, \"_esm\", { signal: this.#signal });\n\t\t\tlet [widget_result, set_widget_result] = solid.createSignal(\n\t\t\t\t/** @type {Result<AnyWidget>} */ ({ status: \"pending\" }),\n\t\t\t);\n\t\t\tthis.#widget_result = widget_result;\n\n\t\t\tsolid.createEffect(\n\t\t\t\tsolid.on(\n\t\t\t\t\tcss,\n\t\t\t\t\t() => console.debug(`[anywidget] css hot updated: ${id}`),\n\t\t\t\t\t{ defer: true },\n\t\t\t\t),\n\t\t\t);\n\t\t\tsolid.createEffect(\n\t\t\t\tsolid.on(\n\t\t\t\t\tesm,\n\t\t\t\t\t() => console.debug(`[anywidget] esm hot updated: ${id}`),\n\t\t\t\t\t{ defer: true },\n\t\t\t\t),\n\t\t\t);\n\t\t\tsolid.createEffect(() => {\n\t\t\t\tload_css(css(), id);\n\t\t\t});\n\t\t\tsolid.createEffect(() => {\n\t\t\t\tlet controller = new AbortController();\n\t\t\t\tsolid.onCleanup(() => controller.abort());\n\t\t\t\tmodel.off(null, null, INITIALIZE_MARKER);\n\t\t\t\tload_widget(esm(), id)\n\t\t\t\t\t.then(async (widget) => {\n\t\t\t\t\t\tif (controller.signal.aborted) {\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tlet cleanup = await widget.initialize?.({\n\t\t\t\t\t\t\tmodel: model_proxy(model, INITIALIZE_MARKER),\n\t\t\t\t\t\t\texperimental: {\n\t\t\t\t\t\t\t\t// @ts-expect-error - bind isn't working\n\t\t\t\t\t\t\t\tinvoke: invoke.bind(null, model),\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t});\n\t\t\t\t\t\tif (controller.signal.aborted) {\n\t\t\t\t\t\t\tsafe_cleanup(cleanup, \"esm update\");\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tcontroller.signal.addEventListener(\"abort\", () =>\n\t\t\t\t\t\t\tsafe_cleanup(cleanup, \"esm update\"),\n\t\t\t\t\t\t);\n\t\t\t\t\t\tset_widget_result({ status: \"ready\", data: widget });\n\t\t\t\t\t\tresolvers.resolve();\n\t\t\t\t\t})\n\t\t\t\t\t.catch((error) => set_widget_result({ status: \"error\", error }));\n\t\t\t});\n\n\t\t\treturn dispose;\n\t\t});\n\t}\n\n\t/**\n\t * @param {base.DOMWidgetView} view\n\t * @param {{ signal: AbortSignal }} options\n\t * @returns {Promise<void>}\n\t */\n\tasync create_view(view, options) {\n\t\tlet model = view.model;\n\t\tlet signal = AbortSignal.any([this.#signal, options.signal]); // either model or view destroyed\n\t\tsignal.throwIfAborted();\n\t\tsignal.addEventListener(\"abort\", () => dispose());\n\t\tlet dispose = solid.createRoot((dispose) => {\n\t\t\tsolid.createEffect(() => {\n\t\t\t\t// Clear all previous event listeners from this hook.\n\t\t\t\tmodel.off(null, null, view);\n\t\t\t\tview.$el.empty();\n\t\t\t\tlet result = this.#widget_result();\n\t\t\t\tif (result.status === \"pending\") {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (result.status === \"error\") {\n\t\t\t\t\tthrow_anywidget_error(result.error);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tlet controller = new AbortController();\n\t\t\t\tsolid.onCleanup(() => controller.abort());\n\t\t\t\tPromise.resolve()\n\t\t\t\t\t.then(async () => {\n\t\t\t\t\t\tlet cleanup = await result.data.render?.({\n\t\t\t\t\t\t\tmodel: model_proxy(model, view),\n\t\t\t\t\t\t\tel: view.el,\n\t\t\t\t\t\t\texperimental: {\n\t\t\t\t\t\t\t\t// @ts-expect-error - bind isn't working\n\t\t\t\t\t\t\t\tinvoke: invoke.bind(null, model),\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t});\n\t\t\t\t\t\tif (controller.signal.aborted) {\n\t\t\t\t\t\t\tsafe_cleanup(cleanup, \"dispose view - already aborted\");\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tcontroller.signal.addEventListener(\"abort\", () =>\n\t\t\t\t\t\t\tsafe_cleanup(cleanup, \"dispose view - aborted\"),\n\t\t\t\t\t\t);\n\t\t\t\t\t})\n\t\t\t\t\t.catch((error) => throw_anywidget_error(error));\n\t\t\t});\n\t\t\treturn () => dispose();\n\t\t});\n\t}\n}\n\n// @ts-expect-error - injected by bundler\nlet version = globalThis.VERSION;\n\n/**\n * @param {base} options\n * @returns {{ AnyModel: typeof base.DOMWidgetModel, AnyView: typeof base.DOMWidgetView }}\n */\nexport default function ({ DOMWidgetModel, DOMWidgetView }) {\n\t/** @type {WeakMap<AnyModel, Runtime>} */\n\tlet RUNTIMES = new WeakMap();\n\n\tclass AnyModel extends DOMWidgetModel {\n\t\tstatic model_name = \"AnyModel\";\n\t\tstatic model_module = \"anywidget\";\n\t\tstatic model_module_version = version;\n\n\t\tstatic view_name = \"AnyView\";\n\t\tstatic view_module = \"anywidget\";\n\t\tstatic view_module_version = version;\n\n\t\t/** @param {Parameters<InstanceType<DOMWidgetModel>[\"initialize\"]>} args */\n\t\tinitialize(...args) {\n\t\t\tsuper.initialize(...args);\n\t\t\tlet controller = new AbortController();\n\t\t\tthis.once(\"destroy\", () => {\n\t\t\t\tcontroller.abort(\"[anywidget] Runtime destroyed.\");\n\t\t\t\tRUNTIMES.delete(this);\n\t\t\t});\n\t\t\tRUNTIMES.set(this, new Runtime(this, { signal: controller.signal }));\n\t\t}\n\n\t\t/** @param {Parameters<InstanceType<DOMWidgetModel>[\"_handle_comm_msg\"]>} msg */\n\t\tasync _handle_comm_msg(...msg) {\n\t\t\tlet runtime = RUNTIMES.get(this);\n\t\t\tawait runtime?.ready;\n\t\t\treturn super._handle_comm_msg(...msg);\n\t\t}\n\n\t\t/**\n\t\t * @param {Record<string, any>} state\n\t\t *\n\t\t * We override to support binary trailets because JSON.parse(JSON.stringify())\n\t\t * does not properly clone binary data (it just returns an empty object).\n\t\t *\n\t\t * https://github.com/jupyter-widgets/ipywidgets/blob/47058a373d2c2b3acf101677b2745e14b76dd74b/packages/base/src/widget.ts#L562-L583\n\t\t */\n\t\tserialize(state) {\n\t\t\tlet serializers =\n\t\t\t\t/** @type {DOMWidgetModel} */ (this.constructor).serializers || {};\n\t\t\tfor (let k of Object.keys(state)) {\n\t\t\t\ttry {\n\t\t\t\t\tlet serialize = serializers[k]?.serialize;\n\t\t\t\t\tif (serialize) {\n\t\t\t\t\t\tstate[k] = serialize(state[k], this);\n\t\t\t\t\t} else if (k === \"layout\" || k === \"style\") {\n\t\t\t\t\t\t// These keys come from ipywidgets, rely on JSON.stringify trick.\n\t\t\t\t\t\tstate[k] = JSON.parse(JSON.stringify(state[k]));\n\t\t\t\t\t} else {\n\t\t\t\t\t\tstate[k] = structuredClone(state[k]);\n\t\t\t\t\t}\n\t\t\t\t\tif (typeof state[k]?.toJSON === \"function\") {\n\t\t\t\t\t\tstate[k] = state[k].toJSON();\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error(\"Error serializing widget state attribute: \", k);\n\t\t\t\t\tthrow e;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn state;\n\t\t}\n\t}\n\n\tclass AnyView extends DOMWidgetView {\n\t\t#controller = new AbortController();\n\t\tasync render() {\n\t\t\tlet runtime = RUNTIMES.get(this.model);\n\t\t\tassert(runtime, \"[anywidget] Runtime not found.\");\n\t\t\tawait runtime.create_view(this, { signal: this.#controller.signal });\n\t\t}\n\t\tremove() {\n\t\t\tthis.#controller.abort(\"[anywidget] View destroyed.\");\n\t\t\tsuper.remove();\n\t\t}\n\t}\n\n\treturn { AnyModel, AnyView };\n}\n", "import * as base from \"@jupyter-widgets/base\";\nimport create from \"./widget.js\";\n\n/**\n * @typedef JupyterLabRegistry\n * @property {(widget: { name: string, version: string, exports: any }) => void} registerWidget\n */\n\nexport default {\n\tid: \"anywidget:plugin\",\n\trequires: [/** @type{unknown} */ (base.IJupyterWidgetRegistry)],\n\tactivate: (\n\t\t/** @type {unknown} */ _app,\n\t\t/** @type {JupyterLabRegistry} */ registry,\n\t) => {\n\t\tlet exports = create(base);\n\t\tregistry.registerWidget({\n\t\t\tname: \"anywidget\",\n\t\t\t// @ts-expect-error Added by bundler\n\t\t\tversion: globalThis.VERSION,\n\t\t\texports,\n\t\t});\n\t},\n\tautoStart: true,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAAqC;AACH;;AAElC;AACA,cAAc,+BAA+B;;AAE7C;AACA;AACA,aAAa,oBAAoB;AACjC;;AAEA;AACA;AACA,qBAAqB;AACrB,iBAAiB;AACjB;;AAEA;AACA;AACA,kBAAkB;AAClB,mBAAmB;AACnB;;AAEA;AACA,WAAW,SAAS;AACpB,WAAW,QAAQ;AACnB,aAAa;AACb;AACA;AACA;AACA;;AAEA;AACA,WAAW,QAAQ;AACnB,aAAa,kBAAkB,OAAO,cAAc,OAAO;AAC3D;AACA;AACA;AACA;;AAEA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,aAAa;AACb;AACA;AACA,YAAY,wBAAwB;AACpC,+CAA+C,aAAa;;AAE5D;AACA;AACA;AACA;AACA,2BAA2B,iBAAiB;AAC5C;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,EAAE;AACF;;AAEA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,aAAa;AACb;AACA;AACA,YAAY,yBAAyB;AACrC,gDAAgD,aAAa;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;;AAEA;AACA,WAAW,oBAAoB;AAC/B,WAAW,QAAQ;AACnB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,WAAW,QAAQ;AACnB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iDAAiD,yBAAyB;AAC1E;AACA;AACA;AACA;;AAEA,YAAY,QAAQ;AACpB;AACA;AACA,sCAAsC,aAAa;;AAEnD;AACA;;AAEA,yBAAyB,WAAW,IAAI;AACxC;;AAEA;AACA;;AAEA,kBAAkB,WAAW,IAAI;AACjC;AACA,iBAAiB;AACjB;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,WAAW,qBAAqB;AAChC,WAAW,SAAS;AACpB,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,WAAW,gCAAgC;AAC3C,WAAW,QAAQ;AACnB;AACA;AACA;AACA;AACA,8DAA8D,KAAK;AACnE;;AAEA;AACA;AACA;AACA,cAAc,SAAS;AACvB,cAAc,GAAG;AACjB;;AAEA;AACA;AACA,cAAc,WAAW;AACzB;;AAEA;AACA;AACA,cAAc,SAAS;AACvB,cAAc,SAAS;AACvB;;AAEA;AACA;AACA,aAAa,8BAA8B;AAC3C;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW,SAAS;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,UAAU,YAAY;AACtB,UAAU,aAAa;AACvB;;AAEA;AACA;AACA,WAAW,qCAAqC;AAChD,WAAW,QAAQ;AACnB,WAAW,KAAK;AAChB,WAAW,eAAe;AAC1B,YAAY;AACZ;AACO,8CAA8C;AACrD;AACA;AACA,UAAU,OAAO;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA,eAAe,+DAA+D;AAC9E,aAAa,YAAY;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,0CAA0C;AAC/C;AACA;AACA;AACA,EAAE;AACF;;AAEA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA,UAAU;AACV;;AAEA;AACA,cAAc,yBAAyB;AACvC,cAAc,kBAAkB;AAChC,WAAW,aAAa;AACxB,WAAW,GAAG;AACd,aAAa,uBAAuB;AACpC,aAAa;AACb;AACA,gCAAgC,QAAQ;AACxC,kBAAkB,0BAAkB;AACpC;AACA,oBAAoB,KAAK;AACzB;AACA,sBAAsB,KAAK;AAC3B,EAAE;AACF;AACA;;AAEA;AACA;AACA,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,oBAAoB;AAClC;;AAEA;AACA,YAAY,mCAAmC;AAC/C;AACA;AACA,YAAY,aAAa;AACzB;AACA,YAAY,eAAe;AAC3B;;AAEA;AACA,YAAY,qBAAqB;AACjC,cAAc,uBAAuB;AACrC;AACA;AACA,aAAa,4BAA4B;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,gBAAgB,wBAAgB;AAChC,cAAc,iBAAiB;AAC/B;AACA;AACA;AACA,4CAA4C,sBAAsB;AAClE,4CAA4C,sBAAsB;AAClE,4CAA4C,0BAAkB;AAC9D,eAAe,mBAAmB,MAAM,mBAAmB;AAC3D;AACA;;AAEA,GAAG,0BAAkB;AACrB,IAAI,QAAQ;AACZ;AACA,yDAAyD,GAAG;AAC5D,OAAO,aAAa;AACpB;AACA;AACA,GAAG,0BAAkB;AACrB,IAAI,QAAQ;AACZ;AACA,yDAAyD,GAAG;AAC5D,OAAO,aAAa;AACpB;AACA;AACA,GAAG,0BAAkB;AACrB;AACA,IAAI;AACJ,GAAG,0BAAkB;AACrB;AACA,IAAI,uBAAe;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,+BAA+B;AACzD;AACA,MAAM;AACN,2CAA2C,wBAAwB;AACnE,IAAI;;AAEJ;AACA,GAAG;AACH;;AAEA;AACA,YAAY,oBAAoB;AAChC,cAAc,uBAAuB;AACrC,cAAc;AACd;AACA;AACA;AACA,gEAAgE;AAChE;AACA;AACA,gBAAgB,wBAAgB;AAChC,GAAG,0BAAkB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,uBAAe;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA;;AAEA;AACA,cAAc,QAAkB;;AAEhC;AACA,WAAW,MAAM;AACjB,eAAe;AACf;AACA,yBAAe,SAAS,WAAC,EAAE,+BAA+B;AAC1D,YAAY,4BAA4B;AACxC;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,cAAc,wDAAwD;AACtE;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ,0CAA0C,2BAA2B;AACrE;;AAEA,cAAc,8DAA8D;AAC5E;AACA;AACA;AACA;AACA;;AAEA;AACA,aAAa,qBAAqB;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,gBAAgB;AAC/B;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,qCAAqC,iCAAiC;AACtE;AACA;AACA;AACA;AACA;AACA;;AAEA,UAAU;AACV;;;AC5jB8C;AACb;;AAEjC;AACA;AACA,cAAc,WAAW,6CAA6C,WAAW;AACjF;;AAEA,6CAAe;AACf;AACA,sBAAsB,SAAS,IAAI,qCAA2B;AAC9D;AACA,aAAa,SAAS;AACtB,aAAa,oBAAoB;AACjC;AACA,gBAAgB,UAAM,CAAC,6BAAI;AAC3B;AACA;AACA;AACA,YAAY,QAAkB;AAC9B;AACA,GAAG;AACH,EAAE;AACF;AACA,CAAC,EAAC"}