{"version": 3, "file": "main.7973f7f4.js", "sources": ["webpack://@anywidget/monorepo/./node_modules/.pnpm/@lukeed+uuid@2.0.1/node_modules/@lukeed/uuid/dist/index.mjs", "webpack://@anywidget/monorepo/./node_modules/.pnpm/solid-js@1.9.5/node_modules/solid-js/dist/solid.js", "webpack://@anywidget/monorepo/./packages/anywidget/src/widget.js", "webpack://@anywidget/monorepo/./packages/anywidget/src/index.js"], "sourcesContent": ["var IDX=256, HEX=[], BUFFER;\nwhile (IDX--) HEX[IDX] = (IDX + 256).toString(16).substring(1);\n\nexport function v4() {\n\tvar i=0, num, out='';\n\n\tif (!BUFFER || ((IDX + 16) > 256)) {\n\t\tBUFFER = Array(i=256);\n\t\twhile (i--) BUFFER[i] = 256 * Math.random() | 0;\n\t\ti = IDX = 0;\n\t}\n\n\tfor (; i < 16; i++) {\n\t\tnum = BUFFER[IDX + i];\n\t\tif (i==6) out += HEX[num & 15 | 64];\n\t\telse if (i==8) out += HEX[num & 63 | 128];\n\t\telse out += HEX[num];\n\n\t\tif (i & 1 && i > 1 && i < 11) out += '-';\n\t}\n\n\tIDX++;\n\treturn out;\n}\n", "let taskIdCounter = 1,\n  isCallbackScheduled = false,\n  isPerformingWork = false,\n  taskQueue = [],\n  currentTask = null,\n  shouldYieldToHost = null,\n  yieldInterval = 5,\n  deadline = 0,\n  maxYieldInterval = 300,\n  scheduleCallback = null,\n  scheduledCallback = null;\nconst maxSigned31BitInt = 1073741823;\nfunction setupScheduler() {\n  const channel = new MessageChannel(),\n    port = channel.port2;\n  scheduleCallback = () => port.postMessage(null);\n  channel.port1.onmessage = () => {\n    if (scheduledCallback !== null) {\n      const currentTime = performance.now();\n      deadline = currentTime + yieldInterval;\n      const hasTimeRemaining = true;\n      try {\n        const hasMoreWork = scheduledCallback(hasTimeRemaining, currentTime);\n        if (!hasMoreWork) {\n          scheduledCallback = null;\n        } else port.postMessage(null);\n      } catch (error) {\n        port.postMessage(null);\n        throw error;\n      }\n    }\n  };\n  if (navigator && navigator.scheduling && navigator.scheduling.isInputPending) {\n    const scheduling = navigator.scheduling;\n    shouldYieldToHost = () => {\n      const currentTime = performance.now();\n      if (currentTime >= deadline) {\n        if (scheduling.isInputPending()) {\n          return true;\n        }\n        return currentTime >= maxYieldInterval;\n      } else {\n        return false;\n      }\n    };\n  } else {\n    shouldYieldToHost = () => performance.now() >= deadline;\n  }\n}\nfunction enqueue(taskQueue, task) {\n  function findIndex() {\n    let m = 0;\n    let n = taskQueue.length - 1;\n    while (m <= n) {\n      const k = (n + m) >> 1;\n      const cmp = task.expirationTime - taskQueue[k].expirationTime;\n      if (cmp > 0) m = k + 1;\n      else if (cmp < 0) n = k - 1;\n      else return k;\n    }\n    return m;\n  }\n  taskQueue.splice(findIndex(), 0, task);\n}\nfunction requestCallback(fn, options) {\n  if (!scheduleCallback) setupScheduler();\n  let startTime = performance.now(),\n    timeout = maxSigned31BitInt;\n  if (options && options.timeout) timeout = options.timeout;\n  const newTask = {\n    id: taskIdCounter++,\n    fn,\n    startTime,\n    expirationTime: startTime + timeout\n  };\n  enqueue(taskQueue, newTask);\n  if (!isCallbackScheduled && !isPerformingWork) {\n    isCallbackScheduled = true;\n    scheduledCallback = flushWork;\n    scheduleCallback();\n  }\n  return newTask;\n}\nfunction cancelCallback(task) {\n  task.fn = null;\n}\nfunction flushWork(hasTimeRemaining, initialTime) {\n  isCallbackScheduled = false;\n  isPerformingWork = true;\n  try {\n    return workLoop(hasTimeRemaining, initialTime);\n  } finally {\n    currentTask = null;\n    isPerformingWork = false;\n  }\n}\nfunction workLoop(hasTimeRemaining, initialTime) {\n  let currentTime = initialTime;\n  currentTask = taskQueue[0] || null;\n  while (currentTask !== null) {\n    if (currentTask.expirationTime > currentTime && (!hasTimeRemaining || shouldYieldToHost())) {\n      break;\n    }\n    const callback = currentTask.fn;\n    if (callback !== null) {\n      currentTask.fn = null;\n      const didUserCallbackTimeout = currentTask.expirationTime <= currentTime;\n      callback(didUserCallbackTimeout);\n      currentTime = performance.now();\n      if (currentTask === taskQueue[0]) {\n        taskQueue.shift();\n      }\n    } else taskQueue.shift();\n    currentTask = taskQueue[0] || null;\n  }\n  return currentTask !== null;\n}\n\nconst sharedConfig = {\n  context: undefined,\n  registry: undefined,\n  effects: undefined,\n  done: false,\n  getContextId() {\n    return getContextId(this.context.count);\n  },\n  getNextContextId() {\n    return getContextId(this.context.count++);\n  }\n};\nfunction getContextId(count) {\n  const num = String(count),\n    len = num.length - 1;\n  return sharedConfig.context.id + (len ? String.fromCharCode(96 + len) : \"\") + num;\n}\nfunction setHydrateContext(context) {\n  sharedConfig.context = context;\n}\nfunction nextHydrateContext() {\n  return {\n    ...sharedConfig.context,\n    id: sharedConfig.getNextContextId(),\n    count: 0\n  };\n}\n\nconst IS_DEV = false;\nconst equalFn = (a, b) => a === b;\nconst $PROXY = Symbol(\"solid-proxy\");\nconst SUPPORTS_PROXY = typeof Proxy === \"function\";\nconst $TRACK = Symbol(\"solid-track\");\nconst $DEVCOMP = Symbol(\"solid-dev-component\");\nconst signalOptions = {\n  equals: equalFn\n};\nlet ERROR = null;\nlet runEffects = runQueue;\nconst STALE = 1;\nconst PENDING = 2;\nconst UNOWNED = {\n  owned: null,\n  cleanups: null,\n  context: null,\n  owner: null\n};\nconst NO_INIT = {};\nvar Owner = null;\nlet Transition = null;\nlet Scheduler = null;\nlet ExternalSourceConfig = null;\nlet Listener = null;\nlet Updates = null;\nlet Effects = null;\nlet ExecCount = 0;\nfunction createRoot(fn, detachedOwner) {\n  const listener = Listener,\n    owner = Owner,\n    unowned = fn.length === 0,\n    current = detachedOwner === undefined ? owner : detachedOwner,\n    root = unowned\n      ? UNOWNED\n      : {\n          owned: null,\n          cleanups: null,\n          context: current ? current.context : null,\n          owner: current\n        },\n    updateFn = unowned ? fn : () => fn(() => untrack(() => cleanNode(root)));\n  Owner = root;\n  Listener = null;\n  try {\n    return runUpdates(updateFn, true);\n  } finally {\n    Listener = listener;\n    Owner = owner;\n  }\n}\nfunction createSignal(value, options) {\n  options = options ? Object.assign({}, signalOptions, options) : signalOptions;\n  const s = {\n    value,\n    observers: null,\n    observerSlots: null,\n    comparator: options.equals || undefined\n  };\n  const setter = value => {\n    if (typeof value === \"function\") {\n      if (Transition && Transition.running && Transition.sources.has(s)) value = value(s.tValue);\n      else value = value(s.value);\n    }\n    return writeSignal(s, value);\n  };\n  return [readSignal.bind(s), setter];\n}\nfunction createComputed(fn, value, options) {\n  const c = createComputation(fn, value, true, STALE);\n  if (Scheduler && Transition && Transition.running) Updates.push(c);\n  else updateComputation(c);\n}\nfunction createRenderEffect(fn, value, options) {\n  const c = createComputation(fn, value, false, STALE);\n  if (Scheduler && Transition && Transition.running) Updates.push(c);\n  else updateComputation(c);\n}\nfunction createEffect(fn, value, options) {\n  runEffects = runUserEffects;\n  const c = createComputation(fn, value, false, STALE),\n    s = SuspenseContext && useContext(SuspenseContext);\n  if (s) c.suspense = s;\n  if (!options || !options.render) c.user = true;\n  Effects ? Effects.push(c) : updateComputation(c);\n}\nfunction createReaction(onInvalidate, options) {\n  let fn;\n  const c = createComputation(\n      () => {\n        fn ? fn() : untrack(onInvalidate);\n        fn = undefined;\n      },\n      undefined,\n      false,\n      0\n    ),\n    s = SuspenseContext && useContext(SuspenseContext);\n  if (s) c.suspense = s;\n  c.user = true;\n  return tracking => {\n    fn = tracking;\n    updateComputation(c);\n  };\n}\nfunction createMemo(fn, value, options) {\n  options = options ? Object.assign({}, signalOptions, options) : signalOptions;\n  const c = createComputation(fn, value, true, 0);\n  c.observers = null;\n  c.observerSlots = null;\n  c.comparator = options.equals || undefined;\n  if (Scheduler && Transition && Transition.running) {\n    c.tState = STALE;\n    Updates.push(c);\n  } else updateComputation(c);\n  return readSignal.bind(c);\n}\nfunction isPromise(v) {\n  return v && typeof v === \"object\" && \"then\" in v;\n}\nfunction createResource(pSource, pFetcher, pOptions) {\n  let source;\n  let fetcher;\n  let options;\n  if (typeof pFetcher === \"function\") {\n    source = pSource;\n    fetcher = pFetcher;\n    options = pOptions || {};\n  } else {\n    source = true;\n    fetcher = pSource;\n    options = pFetcher || {};\n  }\n  let pr = null,\n    initP = NO_INIT,\n    id = null,\n    loadedUnderTransition = false,\n    scheduled = false,\n    resolved = \"initialValue\" in options,\n    dynamic = typeof source === \"function\" && createMemo(source);\n  const contexts = new Set(),\n    [value, setValue] = (options.storage || createSignal)(options.initialValue),\n    [error, setError] = createSignal(undefined),\n    [track, trigger] = createSignal(undefined, {\n      equals: false\n    }),\n    [state, setState] = createSignal(resolved ? \"ready\" : \"unresolved\");\n  if (sharedConfig.context) {\n    id = sharedConfig.getNextContextId();\n    if (options.ssrLoadFrom === \"initial\") initP = options.initialValue;\n    else if (sharedConfig.load && sharedConfig.has(id)) initP = sharedConfig.load(id);\n  }\n  function loadEnd(p, v, error, key) {\n    if (pr === p) {\n      pr = null;\n      key !== undefined && (resolved = true);\n      if ((p === initP || v === initP) && options.onHydrated)\n        queueMicrotask(() =>\n          options.onHydrated(key, {\n            value: v\n          })\n        );\n      initP = NO_INIT;\n      if (Transition && p && loadedUnderTransition) {\n        Transition.promises.delete(p);\n        loadedUnderTransition = false;\n        runUpdates(() => {\n          Transition.running = true;\n          completeLoad(v, error);\n        }, false);\n      } else completeLoad(v, error);\n    }\n    return v;\n  }\n  function completeLoad(v, err) {\n    runUpdates(() => {\n      if (err === undefined) setValue(() => v);\n      setState(err !== undefined ? \"errored\" : resolved ? \"ready\" : \"unresolved\");\n      setError(err);\n      for (const c of contexts.keys()) c.decrement();\n      contexts.clear();\n    }, false);\n  }\n  function read() {\n    const c = SuspenseContext && useContext(SuspenseContext),\n      v = value(),\n      err = error();\n    if (err !== undefined && !pr) throw err;\n    if (Listener && !Listener.user && c) {\n      createComputed(() => {\n        track();\n        if (pr) {\n          if (c.resolved && Transition && loadedUnderTransition) Transition.promises.add(pr);\n          else if (!contexts.has(c)) {\n            c.increment();\n            contexts.add(c);\n          }\n        }\n      });\n    }\n    return v;\n  }\n  function load(refetching = true) {\n    if (refetching !== false && scheduled) return;\n    scheduled = false;\n    const lookup = dynamic ? dynamic() : source;\n    loadedUnderTransition = Transition && Transition.running;\n    if (lookup == null || lookup === false) {\n      loadEnd(pr, untrack(value));\n      return;\n    }\n    if (Transition && pr) Transition.promises.delete(pr);\n    const p =\n      initP !== NO_INIT\n        ? initP\n        : untrack(() =>\n            fetcher(lookup, {\n              value: value(),\n              refetching\n            })\n          );\n    if (!isPromise(p)) {\n      loadEnd(pr, p, undefined, lookup);\n      return p;\n    }\n    pr = p;\n    if (\"value\" in p) {\n      if (p.status === \"success\") loadEnd(pr, p.value, undefined, lookup);\n      else loadEnd(pr, undefined, castError(p.value), lookup);\n      return p;\n    }\n    scheduled = true;\n    queueMicrotask(() => (scheduled = false));\n    runUpdates(() => {\n      setState(resolved ? \"refreshing\" : \"pending\");\n      trigger();\n    }, false);\n    return p.then(\n      v => loadEnd(p, v, undefined, lookup),\n      e => loadEnd(p, undefined, castError(e), lookup)\n    );\n  }\n  Object.defineProperties(read, {\n    state: {\n      get: () => state()\n    },\n    error: {\n      get: () => error()\n    },\n    loading: {\n      get() {\n        const s = state();\n        return s === \"pending\" || s === \"refreshing\";\n      }\n    },\n    latest: {\n      get() {\n        if (!resolved) return read();\n        const err = error();\n        if (err && !pr) throw err;\n        return value();\n      }\n    }\n  });\n  if (dynamic) createComputed(() => load(false));\n  else load(false);\n  return [\n    read,\n    {\n      refetch: load,\n      mutate: setValue\n    }\n  ];\n}\nfunction createDeferred(source, options) {\n  let t,\n    timeout = options ? options.timeoutMs : undefined;\n  const node = createComputation(\n    () => {\n      if (!t || !t.fn)\n        t = requestCallback(\n          () => setDeferred(() => node.value),\n          timeout !== undefined\n            ? {\n                timeout\n              }\n            : undefined\n        );\n      return source();\n    },\n    undefined,\n    true\n  );\n  const [deferred, setDeferred] = createSignal(\n    Transition && Transition.running && Transition.sources.has(node) ? node.tValue : node.value,\n    options\n  );\n  updateComputation(node);\n  setDeferred(() =>\n    Transition && Transition.running && Transition.sources.has(node) ? node.tValue : node.value\n  );\n  return deferred;\n}\nfunction createSelector(source, fn = equalFn, options) {\n  const subs = new Map();\n  const node = createComputation(\n    p => {\n      const v = source();\n      for (const [key, val] of subs.entries())\n        if (fn(key, v) !== fn(key, p)) {\n          for (const c of val.values()) {\n            c.state = STALE;\n            if (c.pure) Updates.push(c);\n            else Effects.push(c);\n          }\n        }\n      return v;\n    },\n    undefined,\n    true,\n    STALE\n  );\n  updateComputation(node);\n  return key => {\n    const listener = Listener;\n    if (listener) {\n      let l;\n      if ((l = subs.get(key))) l.add(listener);\n      else subs.set(key, (l = new Set([listener])));\n      onCleanup(() => {\n        l.delete(listener);\n        !l.size && subs.delete(key);\n      });\n    }\n    return fn(\n      key,\n      Transition && Transition.running && Transition.sources.has(node) ? node.tValue : node.value\n    );\n  };\n}\nfunction batch(fn) {\n  return runUpdates(fn, false);\n}\nfunction untrack(fn) {\n  if (!ExternalSourceConfig && Listener === null) return fn();\n  const listener = Listener;\n  Listener = null;\n  try {\n    if (ExternalSourceConfig) return ExternalSourceConfig.untrack(fn);\n    return fn();\n  } finally {\n    Listener = listener;\n  }\n}\nfunction on(deps, fn, options) {\n  const isArray = Array.isArray(deps);\n  let prevInput;\n  let defer = options && options.defer;\n  return prevValue => {\n    let input;\n    if (isArray) {\n      input = Array(deps.length);\n      for (let i = 0; i < deps.length; i++) input[i] = deps[i]();\n    } else input = deps();\n    if (defer) {\n      defer = false;\n      return prevValue;\n    }\n    const result = untrack(() => fn(input, prevInput, prevValue));\n    prevInput = input;\n    return result;\n  };\n}\nfunction onMount(fn) {\n  createEffect(() => untrack(fn));\n}\nfunction onCleanup(fn) {\n  if (Owner === null);\n  else if (Owner.cleanups === null) Owner.cleanups = [fn];\n  else Owner.cleanups.push(fn);\n  return fn;\n}\nfunction catchError(fn, handler) {\n  ERROR || (ERROR = Symbol(\"error\"));\n  Owner = createComputation(undefined, undefined, true);\n  Owner.context = {\n    ...Owner.context,\n    [ERROR]: [handler]\n  };\n  if (Transition && Transition.running) Transition.sources.add(Owner);\n  try {\n    return fn();\n  } catch (err) {\n    handleError(err);\n  } finally {\n    Owner = Owner.owner;\n  }\n}\nfunction getListener() {\n  return Listener;\n}\nfunction getOwner() {\n  return Owner;\n}\nfunction runWithOwner(o, fn) {\n  const prev = Owner;\n  const prevListener = Listener;\n  Owner = o;\n  Listener = null;\n  try {\n    return runUpdates(fn, true);\n  } catch (err) {\n    handleError(err);\n  } finally {\n    Owner = prev;\n    Listener = prevListener;\n  }\n}\nfunction enableScheduling(scheduler = requestCallback) {\n  Scheduler = scheduler;\n}\nfunction startTransition(fn) {\n  if (Transition && Transition.running) {\n    fn();\n    return Transition.done;\n  }\n  const l = Listener;\n  const o = Owner;\n  return Promise.resolve().then(() => {\n    Listener = l;\n    Owner = o;\n    let t;\n    if (Scheduler || SuspenseContext) {\n      t =\n        Transition ||\n        (Transition = {\n          sources: new Set(),\n          effects: [],\n          promises: new Set(),\n          disposed: new Set(),\n          queue: new Set(),\n          running: true\n        });\n      t.done || (t.done = new Promise(res => (t.resolve = res)));\n      t.running = true;\n    }\n    runUpdates(fn, false);\n    Listener = Owner = null;\n    return t ? t.done : undefined;\n  });\n}\nconst [transPending, setTransPending] = /*@__PURE__*/ createSignal(false);\nfunction useTransition() {\n  return [transPending, startTransition];\n}\nfunction resumeEffects(e) {\n  Effects.push.apply(Effects, e);\n  e.length = 0;\n}\nfunction createContext(defaultValue, options) {\n  const id = Symbol(\"context\");\n  return {\n    id,\n    Provider: createProvider(id),\n    defaultValue\n  };\n}\nfunction useContext(context) {\n  let value;\n  return Owner && Owner.context && (value = Owner.context[context.id]) !== undefined\n    ? value\n    : context.defaultValue;\n}\nfunction children(fn) {\n  const children = createMemo(fn);\n  const memo = createMemo(() => resolveChildren(children()));\n  memo.toArray = () => {\n    const c = memo();\n    return Array.isArray(c) ? c : c != null ? [c] : [];\n  };\n  return memo;\n}\nlet SuspenseContext;\nfunction getSuspenseContext() {\n  return SuspenseContext || (SuspenseContext = createContext());\n}\nfunction enableExternalSource(factory, untrack = fn => fn()) {\n  if (ExternalSourceConfig) {\n    const { factory: oldFactory, untrack: oldUntrack } = ExternalSourceConfig;\n    ExternalSourceConfig = {\n      factory: (fn, trigger) => {\n        const oldSource = oldFactory(fn, trigger);\n        const source = factory(x => oldSource.track(x), trigger);\n        return {\n          track: x => source.track(x),\n          dispose() {\n            source.dispose();\n            oldSource.dispose();\n          }\n        };\n      },\n      untrack: fn => oldUntrack(() => untrack(fn))\n    };\n  } else {\n    ExternalSourceConfig = {\n      factory,\n      untrack\n    };\n  }\n}\nfunction readSignal() {\n  const runningTransition = Transition && Transition.running;\n  if (this.sources && (runningTransition ? this.tState : this.state)) {\n    if ((runningTransition ? this.tState : this.state) === STALE) updateComputation(this);\n    else {\n      const updates = Updates;\n      Updates = null;\n      runUpdates(() => lookUpstream(this), false);\n      Updates = updates;\n    }\n  }\n  if (Listener) {\n    const sSlot = this.observers ? this.observers.length : 0;\n    if (!Listener.sources) {\n      Listener.sources = [this];\n      Listener.sourceSlots = [sSlot];\n    } else {\n      Listener.sources.push(this);\n      Listener.sourceSlots.push(sSlot);\n    }\n    if (!this.observers) {\n      this.observers = [Listener];\n      this.observerSlots = [Listener.sources.length - 1];\n    } else {\n      this.observers.push(Listener);\n      this.observerSlots.push(Listener.sources.length - 1);\n    }\n  }\n  if (runningTransition && Transition.sources.has(this)) return this.tValue;\n  return this.value;\n}\nfunction writeSignal(node, value, isComp) {\n  let current =\n    Transition && Transition.running && Transition.sources.has(node) ? node.tValue : node.value;\n  if (!node.comparator || !node.comparator(current, value)) {\n    if (Transition) {\n      const TransitionRunning = Transition.running;\n      if (TransitionRunning || (!isComp && Transition.sources.has(node))) {\n        Transition.sources.add(node);\n        node.tValue = value;\n      }\n      if (!TransitionRunning) node.value = value;\n    } else node.value = value;\n    if (node.observers && node.observers.length) {\n      runUpdates(() => {\n        for (let i = 0; i < node.observers.length; i += 1) {\n          const o = node.observers[i];\n          const TransitionRunning = Transition && Transition.running;\n          if (TransitionRunning && Transition.disposed.has(o)) continue;\n          if (TransitionRunning ? !o.tState : !o.state) {\n            if (o.pure) Updates.push(o);\n            else Effects.push(o);\n            if (o.observers) markDownstream(o);\n          }\n          if (!TransitionRunning) o.state = STALE;\n          else o.tState = STALE;\n        }\n        if (Updates.length > 10e5) {\n          Updates = [];\n          if (IS_DEV);\n          throw new Error();\n        }\n      }, false);\n    }\n  }\n  return value;\n}\nfunction updateComputation(node) {\n  if (!node.fn) return;\n  cleanNode(node);\n  const time = ExecCount;\n  runComputation(\n    node,\n    Transition && Transition.running && Transition.sources.has(node) ? node.tValue : node.value,\n    time\n  );\n  if (Transition && !Transition.running && Transition.sources.has(node)) {\n    queueMicrotask(() => {\n      runUpdates(() => {\n        Transition && (Transition.running = true);\n        Listener = Owner = node;\n        runComputation(node, node.tValue, time);\n        Listener = Owner = null;\n      }, false);\n    });\n  }\n}\nfunction runComputation(node, value, time) {\n  let nextValue;\n  const owner = Owner,\n    listener = Listener;\n  Listener = Owner = node;\n  try {\n    nextValue = node.fn(value);\n  } catch (err) {\n    if (node.pure) {\n      if (Transition && Transition.running) {\n        node.tState = STALE;\n        node.tOwned && node.tOwned.forEach(cleanNode);\n        node.tOwned = undefined;\n      } else {\n        node.state = STALE;\n        node.owned && node.owned.forEach(cleanNode);\n        node.owned = null;\n      }\n    }\n    node.updatedAt = time + 1;\n    return handleError(err);\n  } finally {\n    Listener = listener;\n    Owner = owner;\n  }\n  if (!node.updatedAt || node.updatedAt <= time) {\n    if (node.updatedAt != null && \"observers\" in node) {\n      writeSignal(node, nextValue, true);\n    } else if (Transition && Transition.running && node.pure) {\n      Transition.sources.add(node);\n      node.tValue = nextValue;\n    } else node.value = nextValue;\n    node.updatedAt = time;\n  }\n}\nfunction createComputation(fn, init, pure, state = STALE, options) {\n  const c = {\n    fn,\n    state: state,\n    updatedAt: null,\n    owned: null,\n    sources: null,\n    sourceSlots: null,\n    cleanups: null,\n    value: init,\n    owner: Owner,\n    context: Owner ? Owner.context : null,\n    pure\n  };\n  if (Transition && Transition.running) {\n    c.state = 0;\n    c.tState = state;\n  }\n  if (Owner === null);\n  else if (Owner !== UNOWNED) {\n    if (Transition && Transition.running && Owner.pure) {\n      if (!Owner.tOwned) Owner.tOwned = [c];\n      else Owner.tOwned.push(c);\n    } else {\n      if (!Owner.owned) Owner.owned = [c];\n      else Owner.owned.push(c);\n    }\n  }\n  if (ExternalSourceConfig && c.fn) {\n    const [track, trigger] = createSignal(undefined, {\n      equals: false\n    });\n    const ordinary = ExternalSourceConfig.factory(c.fn, trigger);\n    onCleanup(() => ordinary.dispose());\n    const triggerInTransition = () => startTransition(trigger).then(() => inTransition.dispose());\n    const inTransition = ExternalSourceConfig.factory(c.fn, triggerInTransition);\n    c.fn = x => {\n      track();\n      return Transition && Transition.running ? inTransition.track(x) : ordinary.track(x);\n    };\n  }\n  return c;\n}\nfunction runTop(node) {\n  const runningTransition = Transition && Transition.running;\n  if ((runningTransition ? node.tState : node.state) === 0) return;\n  if ((runningTransition ? node.tState : node.state) === PENDING) return lookUpstream(node);\n  if (node.suspense && untrack(node.suspense.inFallback)) return node.suspense.effects.push(node);\n  const ancestors = [node];\n  while ((node = node.owner) && (!node.updatedAt || node.updatedAt < ExecCount)) {\n    if (runningTransition && Transition.disposed.has(node)) return;\n    if (runningTransition ? node.tState : node.state) ancestors.push(node);\n  }\n  for (let i = ancestors.length - 1; i >= 0; i--) {\n    node = ancestors[i];\n    if (runningTransition) {\n      let top = node,\n        prev = ancestors[i + 1];\n      while ((top = top.owner) && top !== prev) {\n        if (Transition.disposed.has(top)) return;\n      }\n    }\n    if ((runningTransition ? node.tState : node.state) === STALE) {\n      updateComputation(node);\n    } else if ((runningTransition ? node.tState : node.state) === PENDING) {\n      const updates = Updates;\n      Updates = null;\n      runUpdates(() => lookUpstream(node, ancestors[0]), false);\n      Updates = updates;\n    }\n  }\n}\nfunction runUpdates(fn, init) {\n  if (Updates) return fn();\n  let wait = false;\n  if (!init) Updates = [];\n  if (Effects) wait = true;\n  else Effects = [];\n  ExecCount++;\n  try {\n    const res = fn();\n    completeUpdates(wait);\n    return res;\n  } catch (err) {\n    if (!wait) Effects = null;\n    Updates = null;\n    handleError(err);\n  }\n}\nfunction completeUpdates(wait) {\n  if (Updates) {\n    if (Scheduler && Transition && Transition.running) scheduleQueue(Updates);\n    else runQueue(Updates);\n    Updates = null;\n  }\n  if (wait) return;\n  let res;\n  if (Transition) {\n    if (!Transition.promises.size && !Transition.queue.size) {\n      const sources = Transition.sources;\n      const disposed = Transition.disposed;\n      Effects.push.apply(Effects, Transition.effects);\n      res = Transition.resolve;\n      for (const e of Effects) {\n        \"tState\" in e && (e.state = e.tState);\n        delete e.tState;\n      }\n      Transition = null;\n      runUpdates(() => {\n        for (const d of disposed) cleanNode(d);\n        for (const v of sources) {\n          v.value = v.tValue;\n          if (v.owned) {\n            for (let i = 0, len = v.owned.length; i < len; i++) cleanNode(v.owned[i]);\n          }\n          if (v.tOwned) v.owned = v.tOwned;\n          delete v.tValue;\n          delete v.tOwned;\n          v.tState = 0;\n        }\n        setTransPending(false);\n      }, false);\n    } else if (Transition.running) {\n      Transition.running = false;\n      Transition.effects.push.apply(Transition.effects, Effects);\n      Effects = null;\n      setTransPending(true);\n      return;\n    }\n  }\n  const e = Effects;\n  Effects = null;\n  if (e.length) runUpdates(() => runEffects(e), false);\n  if (res) res();\n}\nfunction runQueue(queue) {\n  for (let i = 0; i < queue.length; i++) runTop(queue[i]);\n}\nfunction scheduleQueue(queue) {\n  for (let i = 0; i < queue.length; i++) {\n    const item = queue[i];\n    const tasks = Transition.queue;\n    if (!tasks.has(item)) {\n      tasks.add(item);\n      Scheduler(() => {\n        tasks.delete(item);\n        runUpdates(() => {\n          Transition.running = true;\n          runTop(item);\n        }, false);\n        Transition && (Transition.running = false);\n      });\n    }\n  }\n}\nfunction runUserEffects(queue) {\n  let i,\n    userLength = 0;\n  for (i = 0; i < queue.length; i++) {\n    const e = queue[i];\n    if (!e.user) runTop(e);\n    else queue[userLength++] = e;\n  }\n  if (sharedConfig.context) {\n    if (sharedConfig.count) {\n      sharedConfig.effects || (sharedConfig.effects = []);\n      sharedConfig.effects.push(...queue.slice(0, userLength));\n      return;\n    }\n    setHydrateContext();\n  }\n  if (sharedConfig.effects && (sharedConfig.done || !sharedConfig.count)) {\n    queue = [...sharedConfig.effects, ...queue];\n    userLength += sharedConfig.effects.length;\n    delete sharedConfig.effects;\n  }\n  for (i = 0; i < userLength; i++) runTop(queue[i]);\n}\nfunction lookUpstream(node, ignore) {\n  const runningTransition = Transition && Transition.running;\n  if (runningTransition) node.tState = 0;\n  else node.state = 0;\n  for (let i = 0; i < node.sources.length; i += 1) {\n    const source = node.sources[i];\n    if (source.sources) {\n      const state = runningTransition ? source.tState : source.state;\n      if (state === STALE) {\n        if (source !== ignore && (!source.updatedAt || source.updatedAt < ExecCount))\n          runTop(source);\n      } else if (state === PENDING) lookUpstream(source, ignore);\n    }\n  }\n}\nfunction markDownstream(node) {\n  const runningTransition = Transition && Transition.running;\n  for (let i = 0; i < node.observers.length; i += 1) {\n    const o = node.observers[i];\n    if (runningTransition ? !o.tState : !o.state) {\n      if (runningTransition) o.tState = PENDING;\n      else o.state = PENDING;\n      if (o.pure) Updates.push(o);\n      else Effects.push(o);\n      o.observers && markDownstream(o);\n    }\n  }\n}\nfunction cleanNode(node) {\n  let i;\n  if (node.sources) {\n    while (node.sources.length) {\n      const source = node.sources.pop(),\n        index = node.sourceSlots.pop(),\n        obs = source.observers;\n      if (obs && obs.length) {\n        const n = obs.pop(),\n          s = source.observerSlots.pop();\n        if (index < obs.length) {\n          n.sourceSlots[s] = index;\n          obs[index] = n;\n          source.observerSlots[index] = s;\n        }\n      }\n    }\n  }\n  if (node.tOwned) {\n    for (i = node.tOwned.length - 1; i >= 0; i--) cleanNode(node.tOwned[i]);\n    delete node.tOwned;\n  }\n  if (Transition && Transition.running && node.pure) {\n    reset(node, true);\n  } else if (node.owned) {\n    for (i = node.owned.length - 1; i >= 0; i--) cleanNode(node.owned[i]);\n    node.owned = null;\n  }\n  if (node.cleanups) {\n    for (i = node.cleanups.length - 1; i >= 0; i--) node.cleanups[i]();\n    node.cleanups = null;\n  }\n  if (Transition && Transition.running) node.tState = 0;\n  else node.state = 0;\n}\nfunction reset(node, top) {\n  if (!top) {\n    node.tState = 0;\n    Transition.disposed.add(node);\n  }\n  if (node.owned) {\n    for (let i = 0; i < node.owned.length; i++) reset(node.owned[i]);\n  }\n}\nfunction castError(err) {\n  if (err instanceof Error) return err;\n  return new Error(typeof err === \"string\" ? err : \"Unknown error\", {\n    cause: err\n  });\n}\nfunction runErrors(err, fns, owner) {\n  try {\n    for (const f of fns) f(err);\n  } catch (e) {\n    handleError(e, (owner && owner.owner) || null);\n  }\n}\nfunction handleError(err, owner = Owner) {\n  const fns = ERROR && owner && owner.context && owner.context[ERROR];\n  const error = castError(err);\n  if (!fns) throw error;\n  if (Effects)\n    Effects.push({\n      fn() {\n        runErrors(error, fns, owner);\n      },\n      state: STALE\n    });\n  else runErrors(error, fns, owner);\n}\nfunction resolveChildren(children) {\n  if (typeof children === \"function\" && !children.length) return resolveChildren(children());\n  if (Array.isArray(children)) {\n    const results = [];\n    for (let i = 0; i < children.length; i++) {\n      const result = resolveChildren(children[i]);\n      Array.isArray(result) ? results.push.apply(results, result) : results.push(result);\n    }\n    return results;\n  }\n  return children;\n}\nfunction createProvider(id, options) {\n  return function provider(props) {\n    let res;\n    createRenderEffect(\n      () =>\n        (res = untrack(() => {\n          Owner.context = {\n            ...Owner.context,\n            [id]: props.value\n          };\n          return children(() => props.children);\n        })),\n      undefined\n    );\n    return res;\n  };\n}\nfunction onError(fn) {\n  ERROR || (ERROR = Symbol(\"error\"));\n  if (Owner === null);\n  else if (Owner.context === null || !Owner.context[ERROR]) {\n    Owner.context = {\n      ...Owner.context,\n      [ERROR]: [fn]\n    };\n    mutateContext(Owner, ERROR, [fn]);\n  } else Owner.context[ERROR].push(fn);\n}\nfunction mutateContext(o, key, value) {\n  if (o.owned) {\n    for (let i = 0; i < o.owned.length; i++) {\n      if (o.owned[i].context === o.context) mutateContext(o.owned[i], key, value);\n      if (!o.owned[i].context) {\n        o.owned[i].context = o.context;\n        mutateContext(o.owned[i], key, value);\n      } else if (!o.owned[i].context[key]) {\n        o.owned[i].context[key] = value;\n        mutateContext(o.owned[i], key, value);\n      }\n    }\n  }\n}\n\nfunction observable(input) {\n  return {\n    subscribe(observer) {\n      if (!(observer instanceof Object) || observer == null) {\n        throw new TypeError(\"Expected the observer to be an object.\");\n      }\n      const handler =\n        typeof observer === \"function\" ? observer : observer.next && observer.next.bind(observer);\n      if (!handler) {\n        return {\n          unsubscribe() {}\n        };\n      }\n      const dispose = createRoot(disposer => {\n        createEffect(() => {\n          const v = input();\n          untrack(() => handler(v));\n        });\n        return disposer;\n      });\n      if (getOwner()) onCleanup(dispose);\n      return {\n        unsubscribe() {\n          dispose();\n        }\n      };\n    },\n    [Symbol.observable || \"@@observable\"]() {\n      return this;\n    }\n  };\n}\nfunction from(producer, initalValue = undefined) {\n  const [s, set] = createSignal(initalValue, {\n    equals: false\n  });\n  if (\"subscribe\" in producer) {\n    const unsub = producer.subscribe(v => set(() => v));\n    onCleanup(() => (\"unsubscribe\" in unsub ? unsub.unsubscribe() : unsub()));\n  } else {\n    const clean = producer(set);\n    onCleanup(clean);\n  }\n  return s;\n}\n\nconst FALLBACK = Symbol(\"fallback\");\nfunction dispose(d) {\n  for (let i = 0; i < d.length; i++) d[i]();\n}\nfunction mapArray(list, mapFn, options = {}) {\n  let items = [],\n    mapped = [],\n    disposers = [],\n    len = 0,\n    indexes = mapFn.length > 1 ? [] : null;\n  onCleanup(() => dispose(disposers));\n  return () => {\n    let newItems = list() || [],\n      newLen = newItems.length,\n      i,\n      j;\n    newItems[$TRACK];\n    return untrack(() => {\n      let newIndices, newIndicesNext, temp, tempdisposers, tempIndexes, start, end, newEnd, item;\n      if (newLen === 0) {\n        if (len !== 0) {\n          dispose(disposers);\n          disposers = [];\n          items = [];\n          mapped = [];\n          len = 0;\n          indexes && (indexes = []);\n        }\n        if (options.fallback) {\n          items = [FALLBACK];\n          mapped[0] = createRoot(disposer => {\n            disposers[0] = disposer;\n            return options.fallback();\n          });\n          len = 1;\n        }\n      } else if (len === 0) {\n        mapped = new Array(newLen);\n        for (j = 0; j < newLen; j++) {\n          items[j] = newItems[j];\n          mapped[j] = createRoot(mapper);\n        }\n        len = newLen;\n      } else {\n        temp = new Array(newLen);\n        tempdisposers = new Array(newLen);\n        indexes && (tempIndexes = new Array(newLen));\n        for (\n          start = 0, end = Math.min(len, newLen);\n          start < end && items[start] === newItems[start];\n          start++\n        );\n        for (\n          end = len - 1, newEnd = newLen - 1;\n          end >= start && newEnd >= start && items[end] === newItems[newEnd];\n          end--, newEnd--\n        ) {\n          temp[newEnd] = mapped[end];\n          tempdisposers[newEnd] = disposers[end];\n          indexes && (tempIndexes[newEnd] = indexes[end]);\n        }\n        newIndices = new Map();\n        newIndicesNext = new Array(newEnd + 1);\n        for (j = newEnd; j >= start; j--) {\n          item = newItems[j];\n          i = newIndices.get(item);\n          newIndicesNext[j] = i === undefined ? -1 : i;\n          newIndices.set(item, j);\n        }\n        for (i = start; i <= end; i++) {\n          item = items[i];\n          j = newIndices.get(item);\n          if (j !== undefined && j !== -1) {\n            temp[j] = mapped[i];\n            tempdisposers[j] = disposers[i];\n            indexes && (tempIndexes[j] = indexes[i]);\n            j = newIndicesNext[j];\n            newIndices.set(item, j);\n          } else disposers[i]();\n        }\n        for (j = start; j < newLen; j++) {\n          if (j in temp) {\n            mapped[j] = temp[j];\n            disposers[j] = tempdisposers[j];\n            if (indexes) {\n              indexes[j] = tempIndexes[j];\n              indexes[j](j);\n            }\n          } else mapped[j] = createRoot(mapper);\n        }\n        mapped = mapped.slice(0, (len = newLen));\n        items = newItems.slice(0);\n      }\n      return mapped;\n    });\n    function mapper(disposer) {\n      disposers[j] = disposer;\n      if (indexes) {\n        const [s, set] = createSignal(j);\n        indexes[j] = set;\n        return mapFn(newItems[j], s);\n      }\n      return mapFn(newItems[j]);\n    }\n  };\n}\nfunction indexArray(list, mapFn, options = {}) {\n  let items = [],\n    mapped = [],\n    disposers = [],\n    signals = [],\n    len = 0,\n    i;\n  onCleanup(() => dispose(disposers));\n  return () => {\n    const newItems = list() || [],\n      newLen = newItems.length;\n    newItems[$TRACK];\n    return untrack(() => {\n      if (newLen === 0) {\n        if (len !== 0) {\n          dispose(disposers);\n          disposers = [];\n          items = [];\n          mapped = [];\n          len = 0;\n          signals = [];\n        }\n        if (options.fallback) {\n          items = [FALLBACK];\n          mapped[0] = createRoot(disposer => {\n            disposers[0] = disposer;\n            return options.fallback();\n          });\n          len = 1;\n        }\n        return mapped;\n      }\n      if (items[0] === FALLBACK) {\n        disposers[0]();\n        disposers = [];\n        items = [];\n        mapped = [];\n        len = 0;\n      }\n      for (i = 0; i < newLen; i++) {\n        if (i < items.length && items[i] !== newItems[i]) {\n          signals[i](() => newItems[i]);\n        } else if (i >= items.length) {\n          mapped[i] = createRoot(mapper);\n        }\n      }\n      for (; i < items.length; i++) {\n        disposers[i]();\n      }\n      len = signals.length = disposers.length = newLen;\n      items = newItems.slice(0);\n      return (mapped = mapped.slice(0, len));\n    });\n    function mapper(disposer) {\n      disposers[i] = disposer;\n      const [s, set] = createSignal(newItems[i]);\n      signals[i] = set;\n      return mapFn(s, i);\n    }\n  };\n}\n\nlet hydrationEnabled = false;\nfunction enableHydration() {\n  hydrationEnabled = true;\n}\nfunction createComponent(Comp, props) {\n  if (hydrationEnabled) {\n    if (sharedConfig.context) {\n      const c = sharedConfig.context;\n      setHydrateContext(nextHydrateContext());\n      const r = untrack(() => Comp(props || {}));\n      setHydrateContext(c);\n      return r;\n    }\n  }\n  return untrack(() => Comp(props || {}));\n}\nfunction trueFn() {\n  return true;\n}\nconst propTraps = {\n  get(_, property, receiver) {\n    if (property === $PROXY) return receiver;\n    return _.get(property);\n  },\n  has(_, property) {\n    if (property === $PROXY) return true;\n    return _.has(property);\n  },\n  set: trueFn,\n  deleteProperty: trueFn,\n  getOwnPropertyDescriptor(_, property) {\n    return {\n      configurable: true,\n      enumerable: true,\n      get() {\n        return _.get(property);\n      },\n      set: trueFn,\n      deleteProperty: trueFn\n    };\n  },\n  ownKeys(_) {\n    return _.keys();\n  }\n};\nfunction resolveSource(s) {\n  return !(s = typeof s === \"function\" ? s() : s) ? {} : s;\n}\nfunction resolveSources() {\n  for (let i = 0, length = this.length; i < length; ++i) {\n    const v = this[i]();\n    if (v !== undefined) return v;\n  }\n}\nfunction mergeProps(...sources) {\n  let proxy = false;\n  for (let i = 0; i < sources.length; i++) {\n    const s = sources[i];\n    proxy = proxy || (!!s && $PROXY in s);\n    sources[i] = typeof s === \"function\" ? ((proxy = true), createMemo(s)) : s;\n  }\n  if (SUPPORTS_PROXY && proxy) {\n    return new Proxy(\n      {\n        get(property) {\n          for (let i = sources.length - 1; i >= 0; i--) {\n            const v = resolveSource(sources[i])[property];\n            if (v !== undefined) return v;\n          }\n        },\n        has(property) {\n          for (let i = sources.length - 1; i >= 0; i--) {\n            if (property in resolveSource(sources[i])) return true;\n          }\n          return false;\n        },\n        keys() {\n          const keys = [];\n          for (let i = 0; i < sources.length; i++)\n            keys.push(...Object.keys(resolveSource(sources[i])));\n          return [...new Set(keys)];\n        }\n      },\n      propTraps\n    );\n  }\n  const sourcesMap = {};\n  const defined = Object.create(null);\n  for (let i = sources.length - 1; i >= 0; i--) {\n    const source = sources[i];\n    if (!source) continue;\n    const sourceKeys = Object.getOwnPropertyNames(source);\n    for (let i = sourceKeys.length - 1; i >= 0; i--) {\n      const key = sourceKeys[i];\n      if (key === \"__proto__\" || key === \"constructor\") continue;\n      const desc = Object.getOwnPropertyDescriptor(source, key);\n      if (!defined[key]) {\n        defined[key] = desc.get\n          ? {\n              enumerable: true,\n              configurable: true,\n              get: resolveSources.bind((sourcesMap[key] = [desc.get.bind(source)]))\n            }\n          : desc.value !== undefined\n          ? desc\n          : undefined;\n      } else {\n        const sources = sourcesMap[key];\n        if (sources) {\n          if (desc.get) sources.push(desc.get.bind(source));\n          else if (desc.value !== undefined) sources.push(() => desc.value);\n        }\n      }\n    }\n  }\n  const target = {};\n  const definedKeys = Object.keys(defined);\n  for (let i = definedKeys.length - 1; i >= 0; i--) {\n    const key = definedKeys[i],\n      desc = defined[key];\n    if (desc && desc.get) Object.defineProperty(target, key, desc);\n    else target[key] = desc ? desc.value : undefined;\n  }\n  return target;\n}\nfunction splitProps(props, ...keys) {\n  if (SUPPORTS_PROXY && $PROXY in props) {\n    const blocked = new Set(keys.length > 1 ? keys.flat() : keys[0]);\n    const res = keys.map(k => {\n      return new Proxy(\n        {\n          get(property) {\n            return k.includes(property) ? props[property] : undefined;\n          },\n          has(property) {\n            return k.includes(property) && property in props;\n          },\n          keys() {\n            return k.filter(property => property in props);\n          }\n        },\n        propTraps\n      );\n    });\n    res.push(\n      new Proxy(\n        {\n          get(property) {\n            return blocked.has(property) ? undefined : props[property];\n          },\n          has(property) {\n            return blocked.has(property) ? false : property in props;\n          },\n          keys() {\n            return Object.keys(props).filter(k => !blocked.has(k));\n          }\n        },\n        propTraps\n      )\n    );\n    return res;\n  }\n  const otherObject = {};\n  const objects = keys.map(() => ({}));\n  for (const propName of Object.getOwnPropertyNames(props)) {\n    const desc = Object.getOwnPropertyDescriptor(props, propName);\n    const isDefaultDesc =\n      !desc.get && !desc.set && desc.enumerable && desc.writable && desc.configurable;\n    let blocked = false;\n    let objectIndex = 0;\n    for (const k of keys) {\n      if (k.includes(propName)) {\n        blocked = true;\n        isDefaultDesc\n          ? (objects[objectIndex][propName] = desc.value)\n          : Object.defineProperty(objects[objectIndex], propName, desc);\n      }\n      ++objectIndex;\n    }\n    if (!blocked) {\n      isDefaultDesc\n        ? (otherObject[propName] = desc.value)\n        : Object.defineProperty(otherObject, propName, desc);\n    }\n  }\n  return [...objects, otherObject];\n}\nfunction lazy(fn) {\n  let comp;\n  let p;\n  const wrap = props => {\n    const ctx = sharedConfig.context;\n    if (ctx) {\n      const [s, set] = createSignal();\n      sharedConfig.count || (sharedConfig.count = 0);\n      sharedConfig.count++;\n      (p || (p = fn())).then(mod => {\n        !sharedConfig.done && setHydrateContext(ctx);\n        sharedConfig.count--;\n        set(() => mod.default);\n        setHydrateContext();\n      });\n      comp = s;\n    } else if (!comp) {\n      const [s] = createResource(() => (p || (p = fn())).then(mod => mod.default));\n      comp = s;\n    }\n    let Comp;\n    return createMemo(() =>\n      (Comp = comp())\n        ? untrack(() => {\n            if (IS_DEV);\n            if (!ctx || sharedConfig.done) return Comp(props);\n            const c = sharedConfig.context;\n            setHydrateContext(ctx);\n            const r = Comp(props);\n            setHydrateContext(c);\n            return r;\n          })\n        : \"\"\n    );\n  };\n  wrap.preload = () => p || ((p = fn()).then(mod => (comp = () => mod.default)), p);\n  return wrap;\n}\nlet counter = 0;\nfunction createUniqueId() {\n  const ctx = sharedConfig.context;\n  return ctx ? sharedConfig.getNextContextId() : `cl-${counter++}`;\n}\n\nconst narrowedError = name => `Stale read from <${name}>.`;\nfunction For(props) {\n  const fallback = \"fallback\" in props && {\n    fallback: () => props.fallback\n  };\n  return createMemo(mapArray(() => props.each, props.children, fallback || undefined));\n}\nfunction Index(props) {\n  const fallback = \"fallback\" in props && {\n    fallback: () => props.fallback\n  };\n  return createMemo(indexArray(() => props.each, props.children, fallback || undefined));\n}\nfunction Show(props) {\n  const keyed = props.keyed;\n  const conditionValue = createMemo(() => props.when, undefined, undefined);\n  const condition = keyed\n    ? conditionValue\n    : createMemo(conditionValue, undefined, {\n        equals: (a, b) => !a === !b\n      });\n  return createMemo(\n    () => {\n      const c = condition();\n      if (c) {\n        const child = props.children;\n        const fn = typeof child === \"function\" && child.length > 0;\n        return fn\n          ? untrack(() =>\n              child(\n                keyed\n                  ? c\n                  : () => {\n                      if (!untrack(condition)) throw narrowedError(\"Show\");\n                      return conditionValue();\n                    }\n              )\n            )\n          : child;\n      }\n      return props.fallback;\n    },\n    undefined,\n    undefined\n  );\n}\nfunction Switch(props) {\n  const chs = children(() => props.children);\n  const switchFunc = createMemo(() => {\n    const ch = chs();\n    const mps = Array.isArray(ch) ? ch : [ch];\n    let func = () => undefined;\n    for (let i = 0; i < mps.length; i++) {\n      const index = i;\n      const mp = mps[i];\n      const prevFunc = func;\n      const conditionValue = createMemo(\n        () => (prevFunc() ? undefined : mp.when),\n        undefined,\n        undefined\n      );\n      const condition = mp.keyed\n        ? conditionValue\n        : createMemo(conditionValue, undefined, {\n            equals: (a, b) => !a === !b\n          });\n      func = () => prevFunc() || (condition() ? [index, conditionValue, mp] : undefined);\n    }\n    return func;\n  });\n  return createMemo(\n    () => {\n      const sel = switchFunc()();\n      if (!sel) return props.fallback;\n      const [index, conditionValue, mp] = sel;\n      const child = mp.children;\n      const fn = typeof child === \"function\" && child.length > 0;\n      return fn\n        ? untrack(() =>\n            child(\n              mp.keyed\n                ? conditionValue()\n                : () => {\n                    if (untrack(switchFunc)()?.[0] !== index) throw narrowedError(\"Match\");\n                    return conditionValue();\n                  }\n            )\n          )\n        : child;\n    },\n    undefined,\n    undefined\n  );\n}\nfunction Match(props) {\n  return props;\n}\nlet Errors;\nfunction resetErrorBoundaries() {\n  Errors && [...Errors].forEach(fn => fn());\n}\nfunction ErrorBoundary(props) {\n  let err;\n  if (sharedConfig.context && sharedConfig.load)\n    err = sharedConfig.load(sharedConfig.getContextId());\n  const [errored, setErrored] = createSignal(err, undefined);\n  Errors || (Errors = new Set());\n  Errors.add(setErrored);\n  onCleanup(() => Errors.delete(setErrored));\n  return createMemo(\n    () => {\n      let e;\n      if ((e = errored())) {\n        const f = props.fallback;\n        return typeof f === \"function\" && f.length ? untrack(() => f(e, () => setErrored())) : f;\n      }\n      return catchError(() => props.children, setErrored);\n    },\n    undefined,\n    undefined\n  );\n}\n\nconst suspenseListEquals = (a, b) =>\n  a.showContent === b.showContent && a.showFallback === b.showFallback;\nconst SuspenseListContext = /* #__PURE__ */ createContext();\nfunction SuspenseList(props) {\n  let [wrapper, setWrapper] = createSignal(() => ({\n      inFallback: false\n    })),\n    show;\n  const listContext = useContext(SuspenseListContext);\n  const [registry, setRegistry] = createSignal([]);\n  if (listContext) {\n    show = listContext.register(createMemo(() => wrapper()().inFallback));\n  }\n  const resolved = createMemo(\n    prev => {\n      const reveal = props.revealOrder,\n        tail = props.tail,\n        { showContent = true, showFallback = true } = show ? show() : {},\n        reg = registry(),\n        reverse = reveal === \"backwards\";\n      if (reveal === \"together\") {\n        const all = reg.every(inFallback => !inFallback());\n        const res = reg.map(() => ({\n          showContent: all && showContent,\n          showFallback\n        }));\n        res.inFallback = !all;\n        return res;\n      }\n      let stop = false;\n      let inFallback = prev.inFallback;\n      const res = [];\n      for (let i = 0, len = reg.length; i < len; i++) {\n        const n = reverse ? len - i - 1 : i,\n          s = reg[n]();\n        if (!stop && !s) {\n          res[n] = {\n            showContent,\n            showFallback\n          };\n        } else {\n          const next = !stop;\n          if (next) inFallback = true;\n          res[n] = {\n            showContent: next,\n            showFallback: !tail || (next && tail === \"collapsed\") ? showFallback : false\n          };\n          stop = true;\n        }\n      }\n      if (!stop) inFallback = false;\n      res.inFallback = inFallback;\n      return res;\n    },\n    {\n      inFallback: false\n    }\n  );\n  setWrapper(() => resolved);\n  return createComponent(SuspenseListContext.Provider, {\n    value: {\n      register: inFallback => {\n        let index;\n        setRegistry(registry => {\n          index = registry.length;\n          return [...registry, inFallback];\n        });\n        return createMemo(() => resolved()[index], undefined, {\n          equals: suspenseListEquals\n        });\n      }\n    },\n    get children() {\n      return props.children;\n    }\n  });\n}\nfunction Suspense(props) {\n  let counter = 0,\n    show,\n    ctx,\n    p,\n    flicker,\n    error;\n  const [inFallback, setFallback] = createSignal(false),\n    SuspenseContext = getSuspenseContext(),\n    store = {\n      increment: () => {\n        if (++counter === 1) setFallback(true);\n      },\n      decrement: () => {\n        if (--counter === 0) setFallback(false);\n      },\n      inFallback,\n      effects: [],\n      resolved: false\n    },\n    owner = getOwner();\n  if (sharedConfig.context && sharedConfig.load) {\n    const key = sharedConfig.getContextId();\n    let ref = sharedConfig.load(key);\n    if (ref) {\n      if (typeof ref !== \"object\" || ref.status !== \"success\") p = ref;\n      else sharedConfig.gather(key);\n    }\n    if (p && p !== \"$$f\") {\n      const [s, set] = createSignal(undefined, {\n        equals: false\n      });\n      flicker = s;\n      p.then(\n        () => {\n          if (sharedConfig.done) return set();\n          sharedConfig.gather(key);\n          setHydrateContext(ctx);\n          set();\n          setHydrateContext();\n        },\n        err => {\n          error = err;\n          set();\n        }\n      );\n    }\n  }\n  const listContext = useContext(SuspenseListContext);\n  if (listContext) show = listContext.register(store.inFallback);\n  let dispose;\n  onCleanup(() => dispose && dispose());\n  return createComponent(SuspenseContext.Provider, {\n    value: store,\n    get children() {\n      return createMemo(() => {\n        if (error) throw error;\n        ctx = sharedConfig.context;\n        if (flicker) {\n          flicker();\n          return (flicker = undefined);\n        }\n        if (ctx && p === \"$$f\") setHydrateContext();\n        const rendered = createMemo(() => props.children);\n        return createMemo(prev => {\n          const inFallback = store.inFallback(),\n            { showContent = true, showFallback = true } = show ? show() : {};\n          if ((!inFallback || (p && p !== \"$$f\")) && showContent) {\n            store.resolved = true;\n            dispose && dispose();\n            dispose = ctx = p = undefined;\n            resumeEffects(store.effects);\n            return rendered();\n          }\n          if (!showFallback) return;\n          if (dispose) return prev;\n          return createRoot(disposer => {\n            dispose = disposer;\n            if (ctx) {\n              setHydrateContext({\n                id: ctx.id + \"F\",\n                count: 0\n              });\n              ctx = undefined;\n            }\n            return props.fallback;\n          }, owner);\n        });\n      });\n    }\n  });\n}\n\nconst DEV = undefined;\n\nexport {\n  $DEVCOMP,\n  $PROXY,\n  $TRACK,\n  DEV,\n  ErrorBoundary,\n  For,\n  Index,\n  Match,\n  Show,\n  Suspense,\n  SuspenseList,\n  Switch,\n  batch,\n  cancelCallback,\n  catchError,\n  children,\n  createComponent,\n  createComputed,\n  createContext,\n  createDeferred,\n  createEffect,\n  createMemo,\n  createReaction,\n  createRenderEffect,\n  createResource,\n  createRoot,\n  createSelector,\n  createSignal,\n  createUniqueId,\n  enableExternalSource,\n  enableHydration,\n  enableScheduling,\n  equalFn,\n  from,\n  getListener,\n  getOwner,\n  indexArray,\n  lazy,\n  mapArray,\n  mergeProps,\n  observable,\n  on,\n  onCleanup,\n  onError,\n  onMount,\n  requestCallback,\n  resetErrorBoundaries,\n  runWithOwner,\n  sharedConfig,\n  splitProps,\n  startTransition,\n  untrack,\n  useContext,\n  useTransition\n};\n", "import * as uuid from \"@lukeed/uuid\";\nimport * as solid from \"solid-js\";\n\n/** @import * as base from \"@jupyter-widgets/base\" */\n/** @import { Initialize, Render, AnyModel } from \"@anywidget/types\" */\n\n/**\n * @template T\n * @typedef {T | PromiseLike<T>} Awaitable\n */\n\n/**\n * @typedef AnyWidget\n * @prop initialize {Initialize}\n * @prop render {Render}\n */\n\n/**\n *  @typedef AnyWidgetModule\n *  @prop render {Render=}\n *  @prop default {AnyWidget | (() => AnyWidget | Promise<AnyWidget>)=}\n */\n\n/**\n * @param {unknown} condition\n * @param {string} message\n * @returns {asserts condition}\n */\nfunction assert(condition, message) {\n\tif (!condition) throw new Error(message);\n}\n\n/**\n * @param {string} str\n * @returns {str is \"https://${string}\" | \"http://${string}\"}\n */\nfunction is_href(str) {\n\treturn str.startsWith(\"http://\") || str.startsWith(\"https://\");\n}\n\n/**\n * @param {string} href\n * @param {string} anywidget_id\n * @returns {Promise<void>}\n */\nasync function load_css_href(href, anywidget_id) {\n\t/** @type {HTMLLinkElement | null} */\n\tlet prev = document.querySelector(`link[id='${anywidget_id}']`);\n\n\t// Adapted from https://github.com/vitejs/vite/blob/d59e1acc2efc0307488364e9f2fad528ec57f204/packages/vite/src/client/client.ts#L185-L201\n\t// Swaps out old styles with new, but avoids flash of unstyled content.\n\t// No need to await the load since we already have styles applied.\n\tif (prev) {\n\t\tlet newLink = /** @type {HTMLLinkElement} */ (prev.cloneNode());\n\t\tnewLink.href = href;\n\t\tnewLink.addEventListener(\"load\", () => prev?.remove());\n\t\tnewLink.addEventListener(\"error\", () => prev?.remove());\n\t\tprev.after(newLink);\n\t\treturn;\n\t}\n\n\treturn new Promise((resolve) => {\n\t\tlet link = Object.assign(document.createElement(\"link\"), {\n\t\t\trel: \"stylesheet\",\n\t\t\thref,\n\t\t\tonload: resolve,\n\t\t});\n\t\tdocument.head.appendChild(link);\n\t});\n}\n\n/**\n * @param {string} css_text\n * @param {string} anywidget_id\n * @returns {void}\n */\nfunction load_css_text(css_text, anywidget_id) {\n\t/** @type {HTMLStyleElement | null} */\n\tlet prev = document.querySelector(`style[id='${anywidget_id}']`);\n\tif (prev) {\n\t\t// replace instead of creating a new DOM node\n\t\tprev.textContent = css_text;\n\t\treturn;\n\t}\n\tlet style = Object.assign(document.createElement(\"style\"), {\n\t\tid: anywidget_id,\n\t\ttype: \"text/css\",\n\t});\n\tstyle.appendChild(document.createTextNode(css_text));\n\tdocument.head.appendChild(style);\n}\n\n/**\n * @param {string | undefined} css\n * @param {string} anywidget_id\n * @returns {Promise<void>}\n */\nasync function load_css(css, anywidget_id) {\n\tif (!css || !anywidget_id) return;\n\tif (is_href(css)) return load_css_href(css, anywidget_id);\n\treturn load_css_text(css, anywidget_id);\n}\n\n/**\n * @param {string} esm\n * @returns {Promise<AnyWidgetModule>}\n */\nasync function load_esm(esm) {\n\tif (is_href(esm)) {\n\t\treturn await import(/* webpackIgnore: true */ /* @vite-ignore */ esm);\n\t}\n\tlet url = URL.createObjectURL(new Blob([esm], { type: \"text/javascript\" }));\n\tlet mod = await import(/* webpackIgnore: true */ /* @vite-ignore */ url);\n\tURL.revokeObjectURL(url);\n\treturn mod;\n}\n\n/** @param {string} anywidget_id */\nfunction warn_render_deprecation(anywidget_id) {\n\tconsole.warn(`\\\n[anywidget] Deprecation Warning for ${anywidget_id}: Direct export of a 'render' will likely be deprecated in the future. To migrate ...\n\nRemove the 'export' keyword from 'render'\n-----------------------------------------\n\nexport function render({ model, el }) { ... }\n^^^^^^\n\nCreate a default export that returns an object with 'render'\n------------------------------------------------------------\n\nfunction render({ model, el }) { ... }\n         ^^^^^^\nexport default { render }\n                 ^^^^^^\n\nPin to anywidget>=0.9.0 in your pyproject.toml\n----------------------------------------------\n\ndependencies = [\"anywidget>=0.9.0\"]\n\nTo learn more, please see: https://github.com/manzt/anywidget/pull/395.\n`);\n}\n\n/**\n * @param {string} esm\n * @param {string} anywidget_id\n * @returns {Promise<AnyWidget>}\n */\nasync function load_widget(esm, anywidget_id) {\n\tlet mod = await load_esm(esm);\n\tif (mod.render) {\n\t\twarn_render_deprecation(anywidget_id);\n\t\treturn {\n\t\t\tasync initialize() {},\n\t\t\trender: mod.render,\n\t\t};\n\t}\n\tassert(\n\t\tmod.default,\n\t\t`[anywidget] module must export a default function or object.`,\n\t);\n\tlet widget =\n\t\ttypeof mod.default === \"function\" ? await mod.default() : mod.default;\n\treturn widget;\n}\n\n/**\n * This is a trick so that we can cleanup event listeners added\n * by the user-defined function.\n */\nlet INITIALIZE_MARKER = Symbol(\"anywidget.initialize\");\n\n/**\n * @param {base.DOMWidgetModel} model\n * @param {unknown} context\n * @return {import(\"@anywidget/types\").AnyModel}\n *\n * Prunes the view down to the minimum context necessary.\n *\n * Calls to `model.get` and `model.set` automatically add the\n * `context`, so we can gracefully unsubscribe from events\n * added by user-defined hooks.\n */\nfunction model_proxy(model, context) {\n\treturn {\n\t\tget: model.get.bind(model),\n\t\tset: model.set.bind(model),\n\t\tsave_changes: model.save_changes.bind(model),\n\t\tsend: model.send.bind(model),\n\t\t// @ts-expect-error\n\t\ton(name, callback) {\n\t\t\tmodel.on(name, callback, context);\n\t\t},\n\t\toff(name, callback) {\n\t\t\tmodel.off(name, callback, context);\n\t\t},\n\t\t// @ts-expect-error - the widget_manager type is wider than what\n\t\t// we want to expose to developers.\n\t\t// In a future version, we will expose a more limited API but\n\t\t// that can wait for a minor version bump.\n\t\twidget_manager: model.widget_manager,\n\t};\n}\n\n/**\n * @param {void | (() => Awaitable<void>)} fn\n * @param {string} kind\n */\nasync function safe_cleanup(fn, kind) {\n\treturn Promise.resolve()\n\t\t.then(() => fn?.())\n\t\t.catch((e) => console.warn(`[anywidget] error cleaning up ${kind}.`, e));\n}\n\n/**\n * @template T\n * @typedef Ready\n * @property {\"ready\"} status\n * @property {T} data\n */\n\n/**\n * @typedef Pending\n * @property {\"pending\"} status\n */\n\n/**\n * @typedef Errored\n * @property {\"error\"} status\n * @property {unknown} error\n */\n\n/**\n * @template T\n * @typedef {Pending | Ready<T> | Errored} Result\n */\n\n/**\n * Cleans up the stack trace at anywidget boundary.\n * You can fully inspect the entire stack trace in the console interactively,\n * but the initial error message is cleaned up to be more user-friendly.\n *\n * @param {unknown} source\n */\nfunction throw_anywidget_error(source) {\n\tif (!(source instanceof Error)) {\n\t\t// Don't know what to do with this.\n\t\tthrow source;\n\t}\n\tlet lines = source.stack?.split(\"\\n\") ?? [];\n\tlet anywidget_index = lines.findIndex((line) => line.includes(\"anywidget\"));\n\tlet clean_stack =\n\t\tanywidget_index === -1 ? lines : lines.slice(0, anywidget_index + 1);\n\tsource.stack = clean_stack.join(\"\\n\");\n\tconsole.error(source);\n\tthrow source;\n}\n\n/**\n * @typedef InvokeOptions\n * @prop {DataView[]} [buffers]\n * @prop {AbortSignal} [signal]\n */\n\n/**\n * @template T\n * @param {import(\"@anywidget/types\").AnyModel} model\n * @param {string} name\n * @param {any} [msg]\n * @param {InvokeOptions} [options]\n * @return {Promise<[T, DataView[]]>}\n */\nexport function invoke(model, name, msg, options = {}) {\n\t// crypto.randomUUID() is not available in non-secure contexts (i.e., http://)\n\t// so we use simple (non-secure) polyfill.\n\tlet id = uuid.v4();\n\tlet signal = options.signal ?? AbortSignal.timeout(3000);\n\n\treturn new Promise((resolve, reject) => {\n\t\tif (signal.aborted) {\n\t\t\treject(signal.reason);\n\t\t}\n\t\tsignal.addEventListener(\"abort\", () => {\n\t\t\tmodel.off(\"msg:custom\", handler);\n\t\t\treject(signal.reason);\n\t\t});\n\n\t\t/**\n\t\t * @param {{ id: string, kind: \"anywidget-command-response\", response: T }} msg\n\t\t * @param {DataView[]} buffers\n\t\t */\n\t\tfunction handler(msg, buffers) {\n\t\t\tif (!(msg.id === id)) return;\n\t\t\tresolve([msg.response, buffers]);\n\t\t\tmodel.off(\"msg:custom\", handler);\n\t\t}\n\t\tmodel.on(\"msg:custom\", handler);\n\t\tmodel.send(\n\t\t\t{ id, kind: \"anywidget-command\", name, msg },\n\t\t\tundefined,\n\t\t\toptions.buffers ?? [],\n\t\t);\n\t});\n}\n\n/**\n * Polyfill for {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/withResolvers Promise.withResolvers}\n *\n * Trevor(2025-03-14): Should be able to remove once more stable across browsers.\n *\n * @template T\n * @returns {PromiseWithResolvers<T>}\n */\nfunction promise_with_resolvers() {\n\tlet resolve;\n\tlet reject;\n\tlet promise = new Promise((res, rej) => {\n\t\tresolve = res;\n\t\treject = rej;\n\t});\n\t// @ts-expect-error - We know these types are ok\n\treturn { promise, resolve, reject };\n}\n\n/**\n * @template {Record<string, unknown>} T\n * @template {keyof T & string} K\n * @param {AnyModel<T>} model\n * @param {K} name\n * @param {{ signal?: AbortSignal}} options\n * @returns {solid.Accessor<T[K]>}\n */\nfunction observe(model, name, { signal }) {\n\tlet [get, set] = solid.createSignal(model.get(name));\n\tlet update = () => set(() => model.get(name));\n\tmodel.on(`change:${name}`, update);\n\tsignal?.addEventListener(\"abort\", () => {\n\t\tmodel.off(`change:${name}`, update);\n\t});\n\treturn get;\n}\n\n/**\n * @typedef State\n * @property {string} _esm\n * @property {string} _anywidget_id\n * @property {string | undefined} _css\n */\n\nclass Runtime {\n\t/** @type {solid.Accessor<Result<AnyWidget>>} */\n\t// @ts-expect-error - Set synchronously in constructor.\n\t#widget_result;\n\t/** @type {AbortSignal} */\n\t#signal;\n\t/** @type {Promise<void>} */\n\tready;\n\n\t/**\n\t * @param {base.DOMWidgetModel} model\n\t * @param {{ signal: AbortSignal }} options\n\t */\n\tconstructor(model, options) {\n\t\t/** @type {PromiseWithResolvers<void>} */\n\t\tlet resolvers = promise_with_resolvers();\n\t\tthis.ready = resolvers.promise;\n\t\tthis.#signal = options.signal;\n\t\tthis.#signal.throwIfAborted();\n\t\tthis.#signal.addEventListener(\"abort\", () => dispose());\n\t\tAbortSignal.timeout(2000).addEventListener(\"abort\", () => {\n\t\t\tresolvers.reject(new Error(\"[anywidget] Failed to initialize model.\"));\n\t\t});\n\t\tlet dispose = solid.createRoot((dispose) => {\n\t\t\t/** @type {AnyModel<State>} */\n\t\t\t// @ts-expect-error - Types don't sufficiently overlap, so we cast here for type-safe access\n\t\t\tlet typed_model = model;\n\t\t\tlet id = typed_model.get(\"_anywidget_id\");\n\t\t\tlet css = observe(typed_model, \"_css\", { signal: this.#signal });\n\t\t\tlet esm = observe(typed_model, \"_esm\", { signal: this.#signal });\n\t\t\tlet [widget_result, set_widget_result] = solid.createSignal(\n\t\t\t\t/** @type {Result<AnyWidget>} */ ({ status: \"pending\" }),\n\t\t\t);\n\t\t\tthis.#widget_result = widget_result;\n\n\t\t\tsolid.createEffect(\n\t\t\t\tsolid.on(\n\t\t\t\t\tcss,\n\t\t\t\t\t() => console.debug(`[anywidget] css hot updated: ${id}`),\n\t\t\t\t\t{ defer: true },\n\t\t\t\t),\n\t\t\t);\n\t\t\tsolid.createEffect(\n\t\t\t\tsolid.on(\n\t\t\t\t\tesm,\n\t\t\t\t\t() => console.debug(`[anywidget] esm hot updated: ${id}`),\n\t\t\t\t\t{ defer: true },\n\t\t\t\t),\n\t\t\t);\n\t\t\tsolid.createEffect(() => {\n\t\t\t\tload_css(css(), id);\n\t\t\t});\n\t\t\tsolid.createEffect(() => {\n\t\t\t\tlet controller = new AbortController();\n\t\t\t\tsolid.onCleanup(() => controller.abort());\n\t\t\t\tmodel.off(null, null, INITIALIZE_MARKER);\n\t\t\t\tload_widget(esm(), id)\n\t\t\t\t\t.then(async (widget) => {\n\t\t\t\t\t\tif (controller.signal.aborted) {\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tlet cleanup = await widget.initialize?.({\n\t\t\t\t\t\t\tmodel: model_proxy(model, INITIALIZE_MARKER),\n\t\t\t\t\t\t\texperimental: {\n\t\t\t\t\t\t\t\t// @ts-expect-error - bind isn't working\n\t\t\t\t\t\t\t\tinvoke: invoke.bind(null, model),\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t});\n\t\t\t\t\t\tif (controller.signal.aborted) {\n\t\t\t\t\t\t\tsafe_cleanup(cleanup, \"esm update\");\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tcontroller.signal.addEventListener(\"abort\", () =>\n\t\t\t\t\t\t\tsafe_cleanup(cleanup, \"esm update\"),\n\t\t\t\t\t\t);\n\t\t\t\t\t\tset_widget_result({ status: \"ready\", data: widget });\n\t\t\t\t\t\tresolvers.resolve();\n\t\t\t\t\t})\n\t\t\t\t\t.catch((error) => set_widget_result({ status: \"error\", error }));\n\t\t\t});\n\n\t\t\treturn dispose;\n\t\t});\n\t}\n\n\t/**\n\t * @param {base.DOMWidgetView} view\n\t * @param {{ signal: AbortSignal }} options\n\t * @returns {Promise<void>}\n\t */\n\tasync create_view(view, options) {\n\t\tlet model = view.model;\n\t\tlet signal = AbortSignal.any([this.#signal, options.signal]); // either model or view destroyed\n\t\tsignal.throwIfAborted();\n\t\tsignal.addEventListener(\"abort\", () => dispose());\n\t\tlet dispose = solid.createRoot((dispose) => {\n\t\t\tsolid.createEffect(() => {\n\t\t\t\t// Clear all previous event listeners from this hook.\n\t\t\t\tmodel.off(null, null, view);\n\t\t\t\tview.$el.empty();\n\t\t\t\tlet result = this.#widget_result();\n\t\t\t\tif (result.status === \"pending\") {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tif (result.status === \"error\") {\n\t\t\t\t\tthrow_anywidget_error(result.error);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tlet controller = new AbortController();\n\t\t\t\tsolid.onCleanup(() => controller.abort());\n\t\t\t\tPromise.resolve()\n\t\t\t\t\t.then(async () => {\n\t\t\t\t\t\tlet cleanup = await result.data.render?.({\n\t\t\t\t\t\t\tmodel: model_proxy(model, view),\n\t\t\t\t\t\t\tel: view.el,\n\t\t\t\t\t\t\texperimental: {\n\t\t\t\t\t\t\t\t// @ts-expect-error - bind isn't working\n\t\t\t\t\t\t\t\tinvoke: invoke.bind(null, model),\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t});\n\t\t\t\t\t\tif (controller.signal.aborted) {\n\t\t\t\t\t\t\tsafe_cleanup(cleanup, \"dispose view - already aborted\");\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tcontroller.signal.addEventListener(\"abort\", () =>\n\t\t\t\t\t\t\tsafe_cleanup(cleanup, \"dispose view - aborted\"),\n\t\t\t\t\t\t);\n\t\t\t\t\t})\n\t\t\t\t\t.catch((error) => throw_anywidget_error(error));\n\t\t\t});\n\t\t\treturn () => dispose();\n\t\t});\n\t}\n}\n\n// @ts-expect-error - injected by bundler\nlet version = globalThis.VERSION;\n\n/**\n * @param {base} options\n * @returns {{ AnyModel: typeof base.DOMWidgetModel, AnyView: typeof base.DOMWidgetView }}\n */\nexport default function ({ DOMWidgetModel, DOMWidgetView }) {\n\t/** @type {WeakMap<AnyModel, Runtime>} */\n\tlet RUNTIMES = new WeakMap();\n\n\tclass AnyModel extends DOMWidgetModel {\n\t\tstatic model_name = \"AnyModel\";\n\t\tstatic model_module = \"anywidget\";\n\t\tstatic model_module_version = version;\n\n\t\tstatic view_name = \"AnyView\";\n\t\tstatic view_module = \"anywidget\";\n\t\tstatic view_module_version = version;\n\n\t\t/** @param {Parameters<InstanceType<DOMWidgetModel>[\"initialize\"]>} args */\n\t\tinitialize(...args) {\n\t\t\tsuper.initialize(...args);\n\t\t\tlet controller = new AbortController();\n\t\t\tthis.once(\"destroy\", () => {\n\t\t\t\tcontroller.abort(\"[anywidget] Runtime destroyed.\");\n\t\t\t\tRUNTIMES.delete(this);\n\t\t\t});\n\t\t\tRUNTIMES.set(this, new Runtime(this, { signal: controller.signal }));\n\t\t}\n\n\t\t/** @param {Parameters<InstanceType<DOMWidgetModel>[\"_handle_comm_msg\"]>} msg */\n\t\tasync _handle_comm_msg(...msg) {\n\t\t\tlet runtime = RUNTIMES.get(this);\n\t\t\tawait runtime?.ready;\n\t\t\treturn super._handle_comm_msg(...msg);\n\t\t}\n\n\t\t/**\n\t\t * @param {Record<string, any>} state\n\t\t *\n\t\t * We override to support binary trailets because JSON.parse(JSON.stringify())\n\t\t * does not properly clone binary data (it just returns an empty object).\n\t\t *\n\t\t * https://github.com/jupyter-widgets/ipywidgets/blob/47058a373d2c2b3acf101677b2745e14b76dd74b/packages/base/src/widget.ts#L562-L583\n\t\t */\n\t\tserialize(state) {\n\t\t\tlet serializers =\n\t\t\t\t/** @type {DOMWidgetModel} */ (this.constructor).serializers || {};\n\t\t\tfor (let k of Object.keys(state)) {\n\t\t\t\ttry {\n\t\t\t\t\tlet serialize = serializers[k]?.serialize;\n\t\t\t\t\tif (serialize) {\n\t\t\t\t\t\tstate[k] = serialize(state[k], this);\n\t\t\t\t\t} else if (k === \"layout\" || k === \"style\") {\n\t\t\t\t\t\t// These keys come from ipywidgets, rely on JSON.stringify trick.\n\t\t\t\t\t\tstate[k] = JSON.parse(JSON.stringify(state[k]));\n\t\t\t\t\t} else {\n\t\t\t\t\t\tstate[k] = structuredClone(state[k]);\n\t\t\t\t\t}\n\t\t\t\t\tif (typeof state[k]?.toJSON === \"function\") {\n\t\t\t\t\t\tstate[k] = state[k].toJSON();\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error(\"Error serializing widget state attribute: \", k);\n\t\t\t\t\tthrow e;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn state;\n\t\t}\n\t}\n\n\tclass AnyView extends DOMWidgetView {\n\t\t#controller = new AbortController();\n\t\tasync render() {\n\t\t\tlet runtime = RUNTIMES.get(this.model);\n\t\t\tassert(runtime, \"[anywidget] Runtime not found.\");\n\t\t\tawait runtime.create_view(this, { signal: this.#controller.signal });\n\t\t}\n\t\tremove() {\n\t\t\tthis.#controller.abort(\"[anywidget] View destroyed.\");\n\t\t\tsuper.remove();\n\t\t}\n\t}\n\n\treturn { AnyModel, AnyView };\n}\n", "import create from \"./widget.js\";\n// @ts-expect-error -- define is a global provided by the notebook runtime.\ndefine([\"@jupyter-widgets/base\"], create);\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;AAEO;AACP;;AAEA;AACA;AACA;AACA;AACA;;AAEA,QAAQ,QAAQ;AAChB;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;;ACvBA;AACA;AACA;AACA,EAAE,eAAS,GAAG,kDAAE;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,eAAS;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,eAAS;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,eAAS;AACnC,QAAQ,eAAS;AACjB;AACA,MAAM,KAAK,eAAS;AACpB,kBAAkB,eAAS;AAC3B;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,uBAAuB,2EAA2B;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,kDAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,6CAA6C,aAAO;AACpD;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,sCAAsC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,qBAAe,eAAe,qBAAe;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,aAAO;AAC3B;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,QAAQ,qBAAe,eAAe,qBAAe;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,cAAc,qBAAe,eAAe,qBAAe;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,aAAO;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,aAAO;AACjB;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,aAAO;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,iBAAiB;AACvC,MAAM;AACN;AACA;AACA;AACA;AACA,mBAAmB,aAAO;AAC1B;AACA;AACA;AACA;AACA;AACA,qBAAqB,aAAO;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,qBAAe;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,cAAQ;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,qBAAe;AACnB;AACA,SAAS,qBAAe,KAAK,qBAAe;AAC5C;AACA;AACA;AACA,YAAY,2CAA2C;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,wBAAwB,2BAA2B;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,aAAO;AAC9B;AACA;AACA;AACA;AACA;AACA,qCAAqC,QAAQ;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkD,SAAS;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,kBAAkB;AACpC;AACA;AACA,kBAAkB,kBAAkB;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,kBAAkB;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,gBAAgB;AAC9B;AACA;AACA;AACA;AACA;AACA,kBAAkB,yBAAyB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA,kBAAkB,2BAA2B;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,QAAQ;AAC7C;AACA;AACA;AACA,IAAI,WAAK;AACT,IAAI;AACJ,oCAAoC,QAAQ;AAC5C;AACA;AACA;AACA,uCAAuC,QAAQ;AAC/C;AACA;AACA;AACA;AACA;AACA,SAAS,WAAK;AACd;AACA;AACA;AACA;AACA;AACA,oBAAoB,uBAAuB,KAAK,WAAK;AACrD;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,qBAAqB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,aAAO;AACtB;AACA;AACA;AACA;AACA,iBAAiB,cAAQ;AACzB,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA,oBAAoB,oBAAoB;AACxC;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,aAAO;AACjB,SAAS;AACT;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;;AAEA;AACA,SAAS,aAAO;AAChB,kBAAkB,cAAc;AAChC;AACA,2CAA2C;AAC3C;AACA;AACA;AACA;AACA;AACA,kBAAkB,aAAO;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,aAAO;AAClB;AACA;AACA;AACA,UAAU,aAAO;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA,QAAQ;AACR;AACA,oBAAoB,YAAY;AAChC;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,YAAY;AACrC;AACA;AACA;AACA;AACA;AACA,wBAAwB,UAAU;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA,wBAAwB,YAAY;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,aAAO;AACzB;AACA;AACA;AACA;AACA,WAAW,aAAO;AAClB;AACA;AACA,UAAU,aAAO;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,YAAY;AAC9B;AACA;AACA,UAAU;AACV;AACA;AACA;AACA,aAAa,kBAAkB;AAC/B;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,aAAO,uBAAuB;AAC9C;AACA;AACA;AACA;AACA,SAAS,aAAO,uBAAuB;AACvC;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,CAAC;AACD;AACA,uDAAuD;AACvD;AACA;AACA,wCAAwC,YAAY;AACpD;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,oBAAoB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,QAAQ;AACnD;AACA;AACA;AACA,SAAS;AACT;AACA,2CAA2C,QAAQ;AACnD;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,0BAA0B,oBAAoB;AAC9C;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,mCAAmC,QAAQ;AAC3C;AACA;AACA;AACA,wCAAwC,QAAQ;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC,QAAQ;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,aAAO;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,aAAO;AACX;AACA;AACA,uDAAuD,aAAO,GAAG;AACjE;;AAEA,kDAAkD,KAAK;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,aAAO;AACnB;AACA;AACA;AACA;AACA,2BAA2B,aAAO;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,cAAc,cAAQ;AACtB;AACA;AACA;AACA;AACA,oBAAoB,gBAAgB;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,aAAO;AACjB;AACA;AACA;AACA;AACA,wBAAwB,aAAO;AAC/B;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qDAAqD,aAAO;AAC5D;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA,4CAA4C,+DAAe;AAC3D;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,0CAA0C,oBAAoB;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC,SAAS;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA,KAAK;AACL;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,0CAA0C;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA,WAAW;AACX,SAAS;AACT,OAAO;AACP;AACA,GAAG;AACH;;AAEA,YAAY,yDAAS;;AAyDnB;;;ACh3DmC;AACH;;AAElC;AACA,cAAc,+BAA+B;;AAE7C;AACA;AACA,aAAa,oBAAoB;AACjC;;AAEA;AACA;AACA,qBAAqB;AACrB,iBAAiB;AACjB;;AAEA;AACA;AACA,kBAAkB;AAClB,mBAAmB;AACnB;;AAEA;AACA,WAAW,SAAS;AACpB,WAAW,QAAQ;AACnB,aAAa;AACb;AACA;AACA;AACA;;AAEA;AACA,WAAW,QAAQ;AACnB,aAAa,kBAAkB,OAAO,cAAc,OAAO;AAC3D;AACA;AACA;AACA;;AAEA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,aAAa;AACb;AACA;AACA,YAAY,wBAAwB;AACpC,+CAA+C,aAAa;;AAE5D;AACA;AACA;AACA;AACA,2BAA2B,iBAAiB;AAC5C;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,EAAE;AACF;;AAEA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,aAAa;AACb;AACA;AACA,YAAY,yBAAyB;AACrC,gDAAgD,aAAa;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA;AACA;;AAEA;AACA,WAAW,oBAAoB;AAC/B,WAAW,QAAQ;AACnB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,WAAW,QAAQ;AACnB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA,iDAAiD,yBAAyB;AAC1E;AACA;AACA;AACA;;AAEA,YAAY,QAAQ;AACpB;AACA;AACA,sCAAsC,aAAa;;AAEnD;AACA;;AAEA,yBAAyB,WAAW,IAAI;AACxC;;AAEA;AACA;;AAEA,kBAAkB,WAAW,IAAI;AACjC;AACA,iBAAiB;AACjB;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,WAAW,qBAAqB;AAChC,WAAW,SAAS;AACpB,YAAY;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,WAAW,gCAAgC;AAC3C,WAAW,QAAQ;AACnB;AACA;AACA;AACA;AACA,8DAA8D,KAAK;AACnE;;AAEA;AACA;AACA;AACA,cAAc,SAAS;AACvB,cAAc,GAAG;AACjB;;AAEA;AACA;AACA,cAAc,WAAW;AACzB;;AAEA;AACA;AACA,cAAc,SAAS;AACvB,cAAc,SAAS;AACvB;;AAEA;AACA;AACA,aAAa,8BAA8B;AAC3C;;AAEA;AACA;AACA;AACA;AACA;AACA,WAAW,SAAS;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,UAAU,YAAY;AACtB,UAAU,aAAa;AACvB;;AAEA;AACA;AACA,WAAW,qCAAqC;AAChD,WAAW,QAAQ;AACnB,WAAW,KAAK;AAChB,WAAW,eAAe;AAC1B,YAAY;AACZ;AACO,8CAA8C;AACrD;AACA;AACA,UAAU,EAAO;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA,eAAe,+DAA+D;AAC9E,aAAa,YAAY;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,0CAA0C;AAC/C;AACA;AACA;AACA,EAAE;AACF;;AAEA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF;AACA,UAAU;AACV;;AAEA;AACA,cAAc,yBAAyB;AACvC,cAAc,kBAAkB;AAChC,WAAW,aAAa;AACxB,WAAW,GAAG;AACd,aAAa,uBAAuB;AACpC,aAAa;AACb;AACA,gCAAgC,QAAQ;AACxC,kBAAkB,YAAkB;AACpC;AACA,oBAAoB,KAAK;AACzB;AACA,sBAAsB,KAAK;AAC3B,EAAE;AACF;AACA;;AAEA;AACA;AACA,cAAc,QAAQ;AACtB,cAAc,QAAQ;AACtB,cAAc,oBAAoB;AAClC;;AAEA;AACA,YAAY,mCAAmC;AAC/C;AACA;AACA,YAAY,aAAa;AACzB;AACA,YAAY,eAAe;AAC3B;;AAEA;AACA,YAAY,qBAAqB;AACjC,cAAc,uBAAuB;AACrC;AACA;AACA,aAAa,4BAA4B;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,gBAAgB,UAAgB;AAChC,cAAc,iBAAiB;AAC/B;AACA;AACA;AACA,4CAA4C,sBAAsB;AAClE,4CAA4C,sBAAsB;AAClE,4CAA4C,YAAkB;AAC9D,eAAe,mBAAmB,MAAM,mBAAmB;AAC3D;AACA;;AAEA,GAAG,YAAkB;AACrB,IAAI,EAAQ;AACZ;AACA,yDAAyD,GAAG;AAC5D,OAAO,aAAa;AACpB;AACA;AACA,GAAG,YAAkB;AACrB,IAAI,EAAQ;AACZ;AACA,yDAAyD,GAAG;AAC5D,OAAO,aAAa;AACpB;AACA;AACA,GAAG,YAAkB;AACrB;AACA,IAAI;AACJ,GAAG,YAAkB;AACrB;AACA,IAAI,SAAe;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,+BAA+B;AACzD;AACA,MAAM;AACN,2CAA2C,wBAAwB;AACnE,IAAI;;AAEJ;AACA,GAAG;AACH;;AAEA;AACA,YAAY,oBAAoB;AAChC,cAAc,uBAAuB;AACrC,cAAc;AACd;AACA;AACA;AACA,gEAAgE;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,IAAI;AACJ;AACA,GAAG;AACH;AACA;;AAEA;AACA,cAAc,QAAkB;;AAEhC;AACA,WAAW,MAAM;AACjB,eAAe;AACf;AACA,yBAAe,SAAS,WAAC,EAAE,+BAA+B;AAC1D,YAAY,4BAA4B;AACxC;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,cAAc,wDAAwD;AACtE;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ,0CAA0C,2BAA2B;AACrE;;AAEA,cAAc,8DAA8D;AAC5E;AACA;AACA;AACA;AACA;;AAEA;AACA,aAAa,qBAAqB;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,gBAAgB;AAC/B;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,qCAAqC,iCAAiC;AACtE;AACA;AACA;AACA;AACA;AACA;;AAEA,UAAU;AACV;;;AC5jBiC;AACjC;AACA"}