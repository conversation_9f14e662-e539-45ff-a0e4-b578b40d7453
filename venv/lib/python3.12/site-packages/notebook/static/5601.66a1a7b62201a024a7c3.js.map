{"version": 3, "file": "5601.66a1a7b62201a024a7c3.js?v=66a1a7b62201a024a7c3", "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACmD;AACQ;AACH;AACwC;AACvD;AACzC;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,4DAAO,EAAE,kEAAgB;AACxC;AACA;AACA,gBAAgB,WAAW;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB,oDAAoD,MAAM;AAC1D,aAAa;AACb,SAAS;AACT,0BAA0B,mCAAmC;AAC7D,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,kEAAgB;AAC/B,eAAe,8EAAmB;AAClC;AACA;AACA,wBAAwB,6DAAU;AAClC,2GAA2G,oFAAyB;AACpI;AACA,2BAA2B,uDAAI;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,yDAAM;AAC9B;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,iEAAe,OAAO,EAAC", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../packages/terminal-extension/lib/index.js"], "sourcesContent": ["// Copyright (c) Jupyter Development Team.\n// Distributed under the terms of the Modified BSD License.\nimport { IRouter, } from '@jupyterlab/application';\nimport { PageConfig, URLExt } from '@jupyterlab/coreutils';\nimport { ITerminalTracker } from '@jupyterlab/terminal';\nimport { INotebookPathOpener, defaultNotebookPathOpener, } from '@jupyter-notebook/application';\nimport { find } from '@lumino/algorithm';\n/**\n * A plugin to open terminals in a new tab\n */\nconst opener = {\n    id: '@jupyter-notebook/terminal-extension:opener',\n    description: 'A plugin to open terminals in a new tab.',\n    requires: [IRouter, ITerminalTracker],\n    autoStart: true,\n    activate: (app, router, tracker) => {\n        const { commands } = app;\n        const terminalPattern = new RegExp('/terminals/(.*)');\n        const command = 'router:terminal';\n        commands.addCommand(command, {\n            execute: (args) => {\n                const parsed = args;\n                const matches = parsed.path.match(terminalPattern);\n                if (!matches) {\n                    return;\n                }\n                const [, name] = matches;\n                if (!name) {\n                    return;\n                }\n                tracker.widgetAdded.connect((send, terminal) => {\n                    terminal.content.setOption('closeOnExit', false);\n                });\n                commands.execute('terminal:open', { name });\n            },\n        });\n        router.register({ command, pattern: terminalPattern });\n    },\n};\n/**\n * Open terminals in a new tab.\n */\nconst redirect = {\n    id: '@jupyter-notebook/terminal-extension:redirect',\n    description: 'Open terminals in a new tab.',\n    requires: [ITerminalTracker],\n    optional: [INotebookPathOpener],\n    autoStart: true,\n    activate: (app, tracker, notebookPathOpener) => {\n        const baseUrl = PageConfig.getBaseUrl();\n        const opener = notebookPathOpener !== null && notebookPathOpener !== void 0 ? notebookPathOpener : defaultNotebookPathOpener;\n        tracker.widgetAdded.connect((send, terminal) => {\n            const widget = find(app.shell.widgets('main'), (w) => w.id === terminal.id);\n            if (widget) {\n                // bail if the terminal is already added to the main area\n                return;\n            }\n            const name = terminal.content.session.name;\n            opener.open({\n                prefix: URLExt.join(baseUrl, 'terminals'),\n                path: name,\n                target: '_blank',\n            });\n            // dispose the widget since it is not used on this page\n            terminal.dispose();\n        });\n    },\n};\n/**\n * Export the plugins as default.\n */\nconst plugins = [opener, redirect];\nexport default plugins;\n"], "names": [], "sourceRoot": ""}