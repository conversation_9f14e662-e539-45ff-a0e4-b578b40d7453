# src/__init__.py
"""
Source modules for the ANN Basics notebook series.

This package contains all the Python modules that support the Jupyter notebooks:
- notebook_framework: Core framework with utilities and verification functions
- progress_*: Progress tracking and visualization system
- hint_system: Interactive hints and feedback system
- nb1_1_tasks: Task definitions and implementations
- neural_network_visualizers: Visualization utilities
- numpy_neuron_widget: Interactive neuron demonstrations
- theory_modules: Educational theory content
- And other supporting modules
"""
