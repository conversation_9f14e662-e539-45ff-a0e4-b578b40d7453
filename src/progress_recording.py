#!/usr/bin/env python3
"""
Progress Recording System - Handles updating the course_progress.json file
Calls verification first, then updates JSON if verification passes
"""

import json
from pathlib import Path
import datetime
from .progress_verification import verify_task


# Task mappings for progress tracking
TASK_PROGRESS_MAP = {
    'numpy_neuron': ('coding', 'task1_numpy_neuron'),
    'neuron_demo': ('labs', 'task1b_neuron_demo'),
    'interactive_widget': ('labs', 'task2_interactive_lab'),
    'numpy_layer': ('coding', 'task3_numpy_layer'),
    'layer_demo': ('labs', 'task3b_layer_demo')
}

# Define notebook requirements
NOTEBOOK_REQUIREMENTS = {
    'NB1.1': {
        'coding': 2,  # 🍒 2 coding challenges (NumPy Neuron + Layer)
        'quizzes': 0,  # 🍓 0 quizzes
        'labs': 3,    # 🍊 3 labs (Neuron demo + Interactive widget + Layer demo)
    },
    'NB1.2': {
        'coding': 2,  # 🍒 PyTorch implementation tasks
        'quizzes': 1,  # 🍓 AutoGrad quiz
        'labs': 1,    # 🍊 Training visualization
    },
    'NB1.3': {
        'coding': 3,  # 🍒 ML pipeline tasks
        'quizzes': 1,  # 🍓 Theory quiz
        'labs': 2,    # 🍊 Performance optimization + MLOps
    }
}


def _recalculate_all_coins(data):
    """Recalculate coin_earned status for all notebooks based on current completion state"""
    if 'notebooks' not in data:
        return

    for notebook, nb_data in data['notebooks'].items():
        if notebook not in NOTEBOOK_REQUIREMENTS:
            continue

        requirements = NOTEBOOK_REQUIREMENTS[notebook]
        coding_complete = len(nb_data.get('coding', [])) >= requirements['coding']
        quiz_complete = len(nb_data.get('quizzes', [])) >= requirements['quizzes']
        lab_complete = len(nb_data.get('labs', [])) >= requirements['labs']

        # Always recalculate coin_earned status
        if coding_complete and quiz_complete and lab_complete:
            nb_data['coin_earned'] = True
        else:
            nb_data['coin_earned'] = False


def _ensure_json_exists(progress_file="course_progress.json"):
    """Ensure the JSON file exists with proper structure"""
    progress_file = Path(progress_file)

    if not progress_file.exists():
        # Create new file with default structure
        # Note: total_coins is calculated dynamically, no longer stored
        default_data = {
            'notebooks': {},
            'start_date': datetime.datetime.now().isoformat(),
            'last_activity': datetime.datetime.now().isoformat()
        }
        with open(progress_file, 'w') as f:
            json.dump(default_data, f, indent=2)
        return default_data

    # Load existing file
    try:
        with open(progress_file, 'r') as f:
            data = json.load(f)

        # Migrate old format if needed
        if 'total_coins' in data and 'notebooks' not in data:
            # Convert old format
            data = {
                'notebooks': {},
                'total_coins': 0,
                'start_date': data.get('start_date', datetime.datetime.now().isoformat()),
                'last_activity': datetime.datetime.now().isoformat()
            }
            with open(progress_file, 'w') as f:
                json.dump(data, f, indent=2)

        # Recalculate coin statuses in case of manual edits
        _recalculate_all_coins(data)

        # Save recalculated data back to file
        with open(progress_file, 'w') as f:
            json.dump(data, f, indent=2)

        return data

    except (json.JSONDecodeError, IOError):
        # File exists but is corrupted, create new
        # Note: total_coins is calculated dynamically, no longer stored
        default_data = {
            'notebooks': {},
            'start_date': datetime.datetime.now().isoformat(),
            'last_activity': datetime.datetime.now().isoformat()
        }
        with open(progress_file, 'w') as f:
            json.dump(default_data, f, indent=2)
        return default_data


def _update_json_file(notebook, fruit_type, activity_name, progress_file="course_progress.json"):
    """Update the JSON file with new progress"""
    # Load current data
    with open(progress_file, 'r') as f:
        data = json.load(f)

    # Initialize notebook data if needed
    if notebook not in data['notebooks']:
        data['notebooks'][notebook] = {
            'coding': [],
            'quizzes': [],
            'labs': [],
            'coin_earned': False
        }

    nb_data = data['notebooks'][notebook]

    # Add activity if not already present
    if activity_name not in nb_data[fruit_type]:
        nb_data[fruit_type].append(activity_name)

        # Check if notebook is complete and award coin
        requirements = NOTEBOOK_REQUIREMENTS[notebook]
        coding_complete = len(nb_data['coding']) >= requirements['coding']
        quiz_complete = len(nb_data['quizzes']) >= requirements['quizzes']
        lab_complete = len(nb_data['labs']) >= requirements['labs']

        # Always recalculate coin_earned status based on current completion state
        if coding_complete and quiz_complete and lab_complete:
            nb_data['coin_earned'] = True
        else:
            nb_data['coin_earned'] = False
        # Note: total_coins is now calculated dynamically in progress_visualization.py

    # Update timestamps
    data['last_activity'] = datetime.datetime.now().isoformat()

    # Save updated data
    with open(progress_file, 'w') as f:
        json.dump(data, f, indent=2)

    return True


def update_progress(task_name, implementation=None, notebook='NB1.1', progress_file="course_progress.json"):
    """
    Update progress for a task

    Args:
        task_name: Name of the task (e.g., 'numpy_neuron', 'layer_demo')
        implementation: The class/object to verify (None for non-coding tasks)
        notebook: Which notebook this task belongs to
        progress_file: Path to the JSON file

    Returns:
        (success: bool, message: str)
    """
    # Step 1: Check if task is valid
    if task_name not in TASK_PROGRESS_MAP:
        return False, f"Unknown task: {task_name}"

    fruit_type, activity_name = TASK_PROGRESS_MAP[task_name]

    # Step 2: Verify implementation if needed (for coding tasks)
    if implementation is not None:
        # This is a coding task that needs verification
        from .progress_verification import verify_task as _verify  # pylint: disable=import-outside-toplevel
        success, message, _ = _verify(task_name, implementation)  # error_context unused

        if not success:
            return False, f"Verification failed: {message}"

    # Step 3: Ensure JSON file exists
    _ensure_json_exists(progress_file)

    # Step 4: Update the JSON file
    _update_json_file(notebook, fruit_type, activity_name, progress_file)

    # Step 5: Return success
    return True, "Progress updated successfully"


def mark_lab_complete(task_name, notebook='NB1.1', progress_file="course_progress.json"):
    """
    Mark a lab/demo task as complete without verification

    Args:
        task_name: Name of the lab task
        notebook: Which notebook this task belongs to
        progress_file: Path to the JSON file

    Returns:
        (success: bool, message: str)
    """
    # Labs don't need verification, just mark as complete
    return update_progress(task_name, implementation=None, notebook=notebook, progress_file=progress_file)


def get_progress_data(progress_file="course_progress.json"):
    """
    Get the current progress data from JSON file

    Returns:
        Dictionary with progress data, or None if file doesn't exist
    """
    progress_file = Path(progress_file)

    if not progress_file.exists():
        return None

    try:
        with open(progress_file, 'r') as f:
            data = json.load(f)

        # Recalculate coin statuses in case of manual edits
        _recalculate_all_coins(data)

        # Save recalculated data back to file
        with open(progress_file, 'w') as f:
            json.dump(data, f, indent=2)

        return data
    except (json.JSONDecodeError, IOError):
        return None
