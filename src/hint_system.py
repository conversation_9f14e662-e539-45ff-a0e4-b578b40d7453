#!/usr/bin/env python3
"""
Hint System - Completely independent from progress tracking
Provides context-aware hints, AI prompts, and solutions for tasks
"""

# Import statements moved to function level to avoid import errors
# when ipywidgets is not available

# Store current error context for help system
_current_error_context = None

# Context-aware hints from the original system
CONTEXT_HINTS = {
    'numpy_neuron': {
        'missing_weights': "💡 <strong>Weights hint</strong>: Add <code>self.weights = np.random.randn(num_inputs)</code> in your <code>__init__</code> method",
        'missing_bias': "💡 <strong>Bias hint</strong>: Add <code>self.bias = np.random.randn()</code> in your <code>__init__</code> method",
        'wrong_weights_shape': "💡 <strong>Shape hint</strong>: Weights should be 1D array with <code>np.random.randn(num_inputs)</code>",
        'wrong_bias_type': "💡 <strong>Bias hint</strong>: Bias should be a scalar - use <code>np.random.randn()</code> with no arguments",
        'missing_forward': "💡 <strong>Structure hint</strong>: Add a <code>forward(self, inputs)</code> method to compute the output",
        'missing_sigmoid': "💡 <strong>Sigmoid hint</strong>: Your <code>activation(self, z)</code> method needs to return something - replace <code>pass</code> with: <code>1 / (1 + np.exp(-z))</code>",
        'missing_activation': "💡 <strong>Sigmoid hint</strong>: Your <code>activation(self, z)</code> method needs to return something - replace <code>pass</code> with the sigmoid formula",
        'activation_wrong_type': "💡 <strong>Type hint</strong>: Your <code>activation</code> method should return a number (float), not a string or other type",
        'wrong_output_type': "💡 <strong>Output hint</strong>: Forward method should return a single number (scalar). Combine inputs & weights, add bias, then apply sigmoid",
        'forward_not_computing': "💡 <strong>Forward hint</strong>: Your forward method is returning a constant. It needs to: 1) compute <code>z = np.dot(inputs, self.weights) + self.bias</code>, then 2) <code>return self.activation(z)</code>",
        'forward_error': "💡 <strong>Math hint</strong>: Forward pass should be <code>z = np.dot(inputs, self.weights) + self.bias</code>, then apply sigmoid",
        'math_error': "💡 <strong>Sigmoid hint</strong>: Apply sigmoid activation: <code>1 / (1 + np.exp(-z))</code>",
        'init_error': "💡 <strong>Init hint</strong>: Your <code>__init__</code> method has an error - check the method signature and implementation"
    },
    'numpy_layer': {
        'missing_weights': "💡 <strong>Weights hint</strong>: Add <code>self.weights = np.random.randn(num_inputs, num_neurons)</code> in your <code>__init__</code> method",
        'missing_biases': "💡 <strong>Biases hint</strong>: Add <code>self.biases = np.random.randn(num_neurons)</code> in your <code>__init__</code> method",
        'wrong_weights_shape': "💡 <strong>Shape hint</strong>: Weights should be 2D array with shape (num_inputs, num_neurons) - each <strong>column</strong> is one neuron's weights",
        'wrong_biases_shape': "💡 <strong>Biases hint</strong>: Biases should be 1D array with <code>np.random.randn(num_neurons)</code>",
        'missing_forward': "💡 <strong>Structure hint</strong>: Add a <code>forward(self, inputs)</code> method to compute the output",
        'forward_not_implemented': "💡 <strong>Implementation hint</strong>: Replace <code>pass</code> in your forward method with: <code>z = np.dot(inputs, self.weights) + self.biases</code>, then apply sigmoid",
        'wrong_output_shape': "💡 <strong>Output hint</strong>: Forward method should return shape (num_neurons,) - one output per neuron",
        'forward_error': "💡 <strong>Math hint</strong>: Forward pass should be <code>z = np.dot(inputs, self.weights) + self.biases</code>, then apply sigmoid activation",
        'math_error': "💡 <strong>Sigmoid hint</strong>: Apply sigmoid activation: <code>1 / (1 + np.exp(-z))</code>",
        'init_error': "💡 <strong>Structure hint</strong>: Your class needs <code>__init__(self, num_inputs, num_neurons)</code> and <code>forward(self, inputs)</code> methods"
    }
}

# AI prompts for each task (for copy-paste-ready solutions)
AI_PROMPTS = {
    'numpy_neuron': "🤖 <strong>AI prompt for solution/help</strong>: 'I need to implement a NumpyNeuron class in Python using NumPy. It should have __init__(self, num_inputs) that initializes random weights and bias, and a forward(self, inputs) method that computes the weighted sum plus bias, then applies self.activation: 1/(1+exp(-z)). Please provide the complete working code with proper comments.'",
    'numpy_layer': "🤖 <strong>AI prompt for solution/help</strong>: 'I need to implement a NumpyLayer class using NumPy matrix operations with column-based weight matrix. It should have __init__(self, num_inputs, num_neurons) that initializes self.weights with shape (num_inputs, num_neurons) where each COLUMN represents one neuron's weights, using np.random.randn(num_inputs, num_neurons). Also initialize self.biases with shape (num_neurons,) using np.random.randn(num_neurons). The forward(self, inputs) method should compute z = np.dot(inputs, self.weights) + self.biases (note: inputs comes first in dot product), then apply activation: 1/(1+np.exp(-z)). Please provide the complete working code with proper comments explaining the column-based approach.'"
}

# Solution hints for tasks
SOLUTIONS = {
    'numpy_neuron': """
class NumpyNeuron:
    def __init__(self, num_inputs):
        self.weights = np.random.randn(num_inputs)
        self.bias = np.random.randn()

    def activation(self, z):
        return 1 / (1 + np.exp(-z))  # Sigmoid

    def forward(self, inputs):
        z = np.dot(inputs, self.weights) + self.bias
        return self.activation(z)
""".strip(),

    'numpy_layer': """
class NumpyLayer:
    def __init__(self, num_inputs, num_neurons):
        self.weights = np.random.randn(num_inputs, num_neurons)  # Each column = one neuron
        self.biases = np.random.randn(num_neurons)

    def activation(self, z):
        return 1 / (1 + np.exp(-z))  # Sigmoid (element-wise)

    def forward(self, inputs):
        z = np.dot(inputs, self.weights) + self.biases
        return self.activation(z)
""".strip(),
}


def set_error_context(context):
    """Set the current error context for context-aware hints"""
    global _current_error_context
    _current_error_context = context


def get_error_context():
    """Get the current error context"""
    global _current_error_context
    return _current_error_context


def clear_error_context():
    """Clear the current error context"""
    global _current_error_context
    _current_error_context = None


def show_help_options(task_name):
    """Show help options (hints, AI prompt, solution) in a clean layout"""
    try:
        import ipywidgets as widgets  # type: ignore
        from IPython.display import display, HTML  # type: ignore

        # Capture the current error context at button creation time
        captured_error_context = get_error_context()

        # Create buttons
        hint_out = widgets.Output()
        ai_out = widgets.Output()
        solution_out = widgets.Output()

        hint_button = widgets.Button(description="💡 Next step", button_style='info', layout=widgets.Layout(width='100px'))
        ai_button = widgets.Button(description="🤖 AI Prompt", button_style='warning', layout=widgets.Layout(width='100px'))
        solution_button = widgets.Button(description="🔍 Solution", button_style='success', layout=widgets.Layout(width='100px'))

        # Track last clicked button for toggle behavior
        last_clicked_button = None  # type: ignore

        def clear_all_outputs():
            """Clear all output widgets"""
            hint_out.clear_output()
            ai_out.clear_output()
            solution_out.clear_output()

        def show_hint(b):
            nonlocal last_clicked_button
            # Check if this button was clicked last (toggle behavior)
            if last_clicked_button == 'hint':
                clear_all_outputs()
                last_clicked_button = None
                return

            # Clear everything and show hint content
            clear_all_outputs()
            last_clicked_button = 'hint'

            with hint_out:
                # Use context-aware hinting with captured context
                if captured_error_context and task_name in CONTEXT_HINTS and captured_error_context in CONTEXT_HINTS[task_name]:
                    hint = CONTEXT_HINTS[task_name][captured_error_context]
                else:
                    # Default hint if no context
                    hint = "💡 <strong>Structure hint</strong>: Your class needs <code>__init__(self, num_inputs)</code> and <code>forward(self, inputs)</code> methods"

                # Enhanced text selection CSS
                display(HTML(f'<div style="padding: 10px; background-color: #e3f2fd; border-left: 4px solid #2196f3; margin: 5px 0; border-radius: 4px; user-select: text; -webkit-user-select: text; -moz-user-select: text; -webkit-touch-callout: text; -khtml-user-select: text; cursor: text;" ondragstart="return false;" tabindex="0">{hint}</div>'))

        def show_ai_prompt(b):
            nonlocal last_clicked_button
            # Check if this button was clicked last (toggle behavior)
            if last_clicked_button == 'ai':
                clear_all_outputs()
                last_clicked_button = None
                return

            # Clear everything and show AI prompt content
            clear_all_outputs()
            last_clicked_button = 'ai'

            with ai_out:
                ai_prompt = AI_PROMPTS.get(task_name, "Ask your AI for help with this task")

                # Enhanced text selection CSS
                display(HTML(f'<div style="padding: 12px; background-color: #fff3e0; border-left: 4px solid #ff9800; border-radius: 4px; user-select: text; -webkit-user-select: text; -moz-user-select: text; -webkit-touch-callout: text; -khtml-user-select: text; cursor: text;" ondragstart="return false;" tabindex="0"><div style="user-select: text; cursor: text;">{ai_prompt}</div></div>'))

        def show_solution(b):
            nonlocal last_clicked_button
            # Check if this button was clicked last (toggle behavior)
            if last_clicked_button == 'solution':
                clear_all_outputs()
                last_clicked_button = None
                return

            # Clear everything and show solution content
            clear_all_outputs()
            last_clicked_button = 'solution'

            with solution_out:
                solution_code = SOLUTIONS.get(task_name, "No solution available")
                display(HTML(f'''
                <div style="padding: 15px; background-color: #e8f5e8; border-left: 4px solid #4caf50; border-radius: 4px; user-select: text; -webkit-user-select: text; -moz-user-select: text; -webkit-touch-callout: text; -khtml-user-select: text; cursor: text;" ondragstart="return false;" tabindex="0">
                    <strong>💡 Complete Solution:</strong>
                    <pre style="background-color: #ffffff; padding: 12px; margin: 10px 0; border-radius: 4px; overflow-x: auto; white-space: pre-wrap; user-select: text; cursor: text; border: 1px solid #c8e6c9; -webkit-touch-callout: text; -khtml-user-select: text;" ondragstart="return false;" tabindex="0"><code style="background-color: transparent; user-select: text;">{solution_code}</code></pre>
                </div>
                '''))

        hint_button.on_click(show_hint)
        ai_button.on_click(show_ai_prompt)
        solution_button.on_click(show_solution)

        button_box = widgets.HBox([hint_button, ai_button, solution_button], layout=widgets.Layout(margin='10px 0'))
        output_area = widgets.VBox([hint_out, ai_out, solution_out])

        display(widgets.VBox([button_box, output_area]))

    except ImportError:
        # Fallback for environments without ipywidgets

        # Show context-aware hint
        global _current_error_context
        if _current_error_context and task_name in CONTEXT_HINTS and _current_error_context in CONTEXT_HINTS[task_name]:
            hint = CONTEXT_HINTS[task_name][_current_error_context]
            # Convert HTML tags to plain text for console output
            hint_text = hint.replace('<strong>', '').replace('</strong>', '').replace('<code>', '`').replace('</code>', '`')
            print(f"\n{hint_text}")
        else:
            print("\n💡 Structure hint: Your class needs `__init__(self, num_inputs)` and `forward(self, inputs)` methods")

        # Show other help options
        ai_prompt = AI_PROMPTS.get(task_name, "Ask your AI for help with this task")
        print(f"\n{ai_prompt}")
        print(f"\n🔍 For complete solution: Run get_solution('{task_name}')")


def get_ai_prompt(task_name):
    """Get AI prompt for specific task"""
    return AI_PROMPTS.get(task_name, f"No AI prompt available for task: {task_name}")


def get_solution(task_name):
    """Get solution code for specific task"""
    return SOLUTIONS.get(task_name, f"No solution available for task: {task_name}")
