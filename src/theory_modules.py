# theory_modules.py
"""
Theory Mini-Modules Framework for AI Course
Provides reusable theory content with interactive quizzes.
"""

import ipywidgets as widgets
from IPython.display import display, HTML, clear_output
import random
from pathlib import Path

class TheoryModule:
    """Base class for theory modules with optional quiz"""

    def __init__(self, title, content_file=None, quiz_questions=None):
        self.title = title
        self.content_file = content_file
        self.quiz_questions = quiz_questions or []
        self.quiz_score = 0
        self.quiz_completed = False

    def show_content(self):
        """Display the theory content"""
        if self.content_file and Path(self.content_file).exists():
            with open(self.content_file, 'r') as f:
                content = f.read()
            display(HTML(f"""
            <div style="border: 2px solid #4CAF50; border-radius: 8px; padding: 20px; margin: 10px 0; background-color: #f9f9f9;">
                <h2 style="color: #2E7D32; margin-top: 0;">📚 {self.title}</h2>
                <div style="line-height: 1.6;">
                    {self._markdown_to_html(content)}
                </div>
            </div>
            """))
        else:
            print(f"📚 {self.title}: Content file not found")

    def _markdown_to_html(self, markdown_text):
        """Simple markdown to HTML conversion"""
        html = markdown_text
        # Convert headers
        html = html.replace('\n# ', '\n<h1>').replace('\n## ', '\n<h2>').replace('\n### ', '\n<h3>')
        html = html.replace('<h1>', '<h1>').replace('<h2>', '<h2>').replace('<h3>', '<h3>')
        # Convert bold
        html = html.replace('**', '<strong>').replace('**', '</strong>')
        # Convert paragraphs
        html = html.replace('\n\n', '</p><p>').replace('\n', '<br>')
        html = f'<p>{html}</p>'
        return html

    def show_quiz(self):
        """Display interactive quiz if available"""
        if not self.quiz_questions:
            print("No quiz available for this module")
            return

        quiz_container = widgets.VBox()
        self.current_question = 0
        self.quiz_score = 0

        def show_question(question_idx):
            if question_idx >= len(self.quiz_questions):
                self._show_quiz_results(quiz_container)
                return

            question = self.quiz_questions[question_idx]

            question_widget = widgets.HTML(f"""
            <div style="background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;">
                <h3>Question {question_idx + 1}/{len(self.quiz_questions)}</h3>
                <p><strong>{question['question']}</strong></p>
            </div>
            """)

            # Create radio buttons for answers
            options = [(opt, i) for i, opt in enumerate(question['options'])]
            answer_widget = widgets.RadioButtons(
                options=options,
                description='Select:',
                disabled=False
            )

            submit_button = widgets.Button(
                description='Submit Answer',
                button_style='info'
            )

            result_widget = widgets.Output()

            def check_answer(button):
                with result_widget:
                    clear_output()
                    selected_idx = answer_widget.value
                    correct_idx = question['correct']

                    if selected_idx == correct_idx:
                        self.quiz_score += 1
                        display(HTML('<p style="color: green;">✅ Correct!</p>'))
                    else:
                        display(HTML(f'<p style="color: red;">❌ Incorrect. The correct answer was: {question["options"][correct_idx]}</p>'))

                    if 'explanation' in question:
                        display(HTML(f'<p><em>Explanation: {question["explanation"]}</em></p>'))

                    # Show next question after delay
                    widgets.jslink((submit_button, 'clicks'), (widgets.Button(description='Next'), 'clicks'))
                    next_button = widgets.Button(description='Next Question', button_style='success')
                    next_button.on_click(lambda b: show_question(question_idx + 1))
                    display(next_button)

            submit_button.on_click(check_answer)

            quiz_container.children = [
                question_widget,
                answer_widget,
                submit_button,
                result_widget
            ]

        show_question(0)
        display(quiz_container)

    def _show_quiz_results(self, container):
        """Show final quiz results"""
        percentage = (self.quiz_score / len(self.quiz_questions)) * 100
        self.quiz_completed = True

        result_html = f"""
        <div style="background: #e8f5e8; padding: 20px; border-radius: 10px; text-align: center; margin: 20px 0;">
            <h2>🎉 Quiz Complete!</h2>
            <p><strong>Score: {self.quiz_score}/{len(self.quiz_questions)} ({percentage:.1f}%)</strong></p>
            {self._get_performance_message(percentage)}
        </div>
        """

        container.children = [widgets.HTML(result_html)]

    def _get_performance_message(self, percentage):
        """Get encouraging message based on performance"""
        if percentage >= 90:
            return "<p style='color: #2E7D32;'>🌟 Excellent! You've mastered this concept!</p>"
        elif percentage >= 70:
            return "<p style='color: #1976D2;'>👍 Good job! You understand the main points.</p>"
        elif percentage >= 50:
            return "<p style='color: #F57C00;'>📖 Consider reviewing the material and trying again.</p>"
        else:
            return "<p style='color: #D32F2F;'>🔄 Please review the content and retake the quiz.</p>"

    def show_complete_module(self):
        """Show content followed by quiz"""
        self.show_content()
        if self.quiz_questions:
            print("\n" + "="*50)
            print("Ready to test your understanding? 🧠")
            self.show_quiz()

# Pre-defined theory modules
def get_cross_entropy_module():
    """Cross-entropy vs MSE theory module"""
    quiz_questions = [
        {
            'question': 'Why is MSE problematic for classification?',
            'options': [
                'It only works with binary classification',
                'It penalizes all imperfections equally, leading to mediocre compromises',
                'It requires more computational power',
                'It cannot handle multi-class problems'
            ],
            'correct': 1,
            'explanation': 'MSE treats all errors equally, causing models to make safe compromises rather than confident decisions.'
        },
        {
            'question': 'What does cross-entropy focus on?',
            'options': [
                'Making all wrong predictions exactly 0',
                'Minimizing the distance between predictions and targets',
                'How confident the model is in the correct answer',
                'Balancing all output probabilities'
            ],
            'correct': 2,
            'explanation': 'Cross-entropy only cares about the confidence in the correct class, leading to decisive predictions.'
        },
        {
            'question': 'When should you use cross-entropy?',
            'options': [
                'For regression problems',
                'When predicting continuous values',
                'For classification where you want confident decisions',
                'Only for binary classification'
            ],
            'correct': 2,
            'explanation': 'Cross-entropy is ideal for classification tasks where you want the model to make confident, decisive predictions.'
        }
    ]

    return TheoryModule(
        title="Cross-Entropy vs MSE in Classification",
        content_file="docs/cross-entropy-vs-mse-explanation.md",
        quiz_questions=quiz_questions
    )

def show_theory_module(module_name):
    """Show a specific theory module"""
    modules = {
        'cross_entropy': get_cross_entropy_module,
    }

    if module_name in modules:
        module = modules[module_name]()
        module.show_complete_module()
        return module
    else:
        print(f"Theory module '{module_name}' not found.")
        print(f"Available modules: {list(modules.keys())}")
        return None

# Convenience function
def cross_entropy_theory():
    """Quick access to cross-entropy theory module"""
    return show_theory_module('cross_entropy')
