#!/usr/bin/env python3
# autograd_quiz_widget.py
"""
Interactive Quiz Widget for Understanding Automatic Differentiation
"""

from ipywidgets import VBox, HBox, HTML, Button, Dropdown, Layout, Output
from IPython.display import display, clear_output

def create_autograd_quiz():
    """
    Create an interactive quiz widget to test understanding of automatic differentiation concepts.

    Returns:
        None (displays the interactive widget)
    """

    # Quiz questions and answers
    questions = [
        {
            "question": "In a neural network with layers [784, 128, 64, 10], what does each number represent?",
            "options": [
                "A) The number of weights in each layer",
                "B) The number of neurons/features in each layer",
                "C) The learning rates for each layer",
                "D) The activation functions for each layer"
            ],
            "correct": "B",
            "explanation": "Each number represents the number of neurons/features: 784 inputs → 128 neurons → 64 neurons → 10 outputs (classes)"
        },
        {
            "question": "What is the main purpose of backpropagation in neural network training?",
            "options": [
                "A) To make predictions on new data",
                "B) To compute how much each weight contributed to the error",
                "C) To initialize weights randomly",
                "D) To speed up forward pass computation"
            ],
            "correct": "B",
            "explanation": "Backpropagation calculates gradients that tell us which direction to adjust each weight to reduce the loss."
        },
        {
            "question": "Why do we need gradients during training?",
            "options": [
                "A) To know which direction to adjust weights to improve performance",
                "B) To calculate the final prediction accuracy",
                "C) To determine the optimal network architecture",
                "D) To speed up the forward pass"
            ],
            "correct": "A",
            "explanation": "Gradients point in the direction of steepest increase in loss, so we move weights in the opposite direction."
        },
        {
            "question": "When you implement a forward() method in PyTorch, what happens automatically?",
            "options": [
                "A) PyTorch optimizes your code for speed",
                "B) PyTorch creates the corresponding gradient computation for backpropagation",
                "C) PyTorch initializes all weights to optimal values",
                "D) PyTorch converts your model to run on GPU"
            ],
            "correct": "B",
            "explanation": "PyTorch analyzes your forward operations and automatically builds the computational graph needed for computing gradients."
        }
    ]

    # Create dropdown widgets for each question
    dropdowns = []
    for i, q in enumerate(questions):
        dropdown = Dropdown(
            options=[("Select an answer...", "")] + [(opt, opt[0]) for opt in q["options"]],
            value="",
            description=f"Q{i+1}:",
            layout=Layout(width='500px'),
            style={'description_width': '40px'}
        )
        dropdowns.append(dropdown)

    # Create check answers button
    check_button = Button(
        description="🎯 Check My Answers",
        button_style='info',
        layout=Layout(width='200px', height='40px')
    )

    # Create reset button
    reset_button = Button(
        description="🔄 Reset Quiz",
        button_style='warning',
        layout=Layout(width='150px', height='40px')
    )

    # Create output widget for results
    results_output = Output()

    # Track widget state
    widget_state = {"is_hidden": False, "final_score_html": ""}

    # Create hide button (initially hidden)
    hide_button = Button(
        description="🙈 Hide Quiz",
        button_style='success',
        layout=Layout(width='150px', height='40px', visibility='hidden')
    )

    # Create second hide button for bottom placement (initially hidden)
    hide_button_bottom = Button(
        description="🙈 Hide Quiz",
        button_style='success',
        layout=Layout(width='150px', height='40px', visibility='hidden')
    )

    def check_answers(button):
        """Check answers and display results"""
        with results_output:
            clear_output(wait=True)

            # Check if all questions are answered
            unanswered = [i+1 for i, dropdown in enumerate(dropdowns) if dropdown.value == ""]
            if unanswered:
                display(HTML(f"""
                <div style='background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 10px 0;'>
                    <strong>⚠️ Please answer all questions!</strong><br>
                    Missing answers for question(s): {', '.join(map(str, unanswered))}
                </div>
                """))
                return

            # Grade the quiz
            correct_count = 0
            results_html = "<div style='font-family: Arial, sans-serif;'>"

            for i, (question, dropdown) in enumerate(zip(questions, dropdowns)):
                is_correct = dropdown.value == question["correct"]
                if is_correct:
                    correct_count += 1

                # Color coding for results
                color = "#d4edda" if is_correct else "#f8d7da"
                border_color = "#c3e6cb" if is_correct else "#f5c6cb"
                icon = "✅" if is_correct else "❌"

                # Build answer display - only show correct answer if user got it wrong
                answer_display = f"<strong>Your answer:</strong> {dropdown.value}) {[opt for opt in question['options'] if opt.startswith(dropdown.value)][0][3:]}<br>"
                if not is_correct:
                    answer_display += f"<strong>Correct answer:</strong> {question['correct']}) {[opt for opt in question['options'] if opt.startswith(question['correct'])][0][3:]}<br>"

                results_html += f"""
                <div style='background-color: {color}; border: 1px solid {border_color}; border-radius: 5px; padding: 10px; margin: 8px 0;'>
                    <strong>Question {i+1}: {icon}</strong><br>
                    <small style='color: #666;'>{question['question']}</small><br>
                    {answer_display}
                    <em>{question['explanation']}</em>
                </div>
                """

            # Overall score
            score_percentage = (correct_count / len(questions)) * 100
            if score_percentage == 100:
                score_color = "#d4edda"
                score_border = "#c3e6cb"
                score_message = "🎉 Perfect! You're ready for PyTorch implementation!"
            elif score_percentage >= 75:
                score_color = "#fff3cd"
                score_border = "#ffeaa7"
                score_message = "👍 Good job! Review the incorrect answers and you'll be ready."
            else:
                score_color = "#f8d7da"
                score_border = "#f5c6cb"
                score_message = "📚 Consider reviewing the theory section before proceeding."

            final_score_html = f"""
            <div style='background-color: {score_color}; border: 1px solid {score_border}; border-radius: 5px; padding: 15px; margin: 15px 0; text-align: center;'>
                <strong style='font-size: 18px;'>Final Score: {correct_count}/{len(questions)} ({score_percentage:.0f}%)</strong><br>
                <em>{score_message}</em>
            </div>
            """

            results_html += final_score_html + "</div>"
            widget_state["final_score_html"] = final_score_html

            # Show hide buttons only if perfect score
            if score_percentage == 100:
                hide_button.layout.visibility = 'visible'
                hide_button_bottom.layout.visibility = 'visible'
            else:
                hide_button.layout.visibility = 'hidden'
                hide_button_bottom.layout.visibility = 'hidden'

            display(HTML(results_html))

    def reset_quiz(button):
        """Reset all dropdown selections"""
        for dropdown in dropdowns:
            dropdown.value = ""
        hide_button.layout.visibility = 'hidden'
        hide_button_bottom.layout.visibility = 'hidden'
        widget_state["is_hidden"] = False
        widget_state["final_score_html"] = ""
        # Show all quiz content
        for widget in question_widgets:
            widget.layout.display = 'block'
        button_container.layout.display = 'block'
        with results_output:
            clear_output()

    def hide_quiz(button):
        """Hide quiz content, show only final score"""
        if not widget_state["is_hidden"]:
            # Hide quiz content
            for widget in question_widgets:
                widget.layout.display = 'none'
            button_container.layout.display = 'none'
            hide_button.description = "👁️ Show Quiz"
            hide_button_bottom.description = "👁️ Show Quiz"
            widget_state["is_hidden"] = True

            # Show only final score
            with results_output:
                clear_output(wait=True)
                display(HTML(f"<div style='font-family: Arial, sans-serif;'>{widget_state['final_score_html']}</div>"))
        else:
            # Show quiz content
            for widget in question_widgets:
                widget.layout.display = 'block'
            button_container.layout.display = 'block'
            hide_button.description = "🙈 Hide Quiz"
            hide_button_bottom.description = "🙈 Hide Quiz"
            widget_state["is_hidden"] = False

            # Re-run check answers to show full results
            check_answers(None)

    # Connect button callbacks
    check_button.on_click(check_answers)
    reset_button.on_click(reset_quiz)
    hide_button.on_click(hide_quiz)
    hide_button_bottom.on_click(hide_quiz)

    # Create question widgets
    question_widgets = []
    for i, (question, dropdown) in enumerate(zip(questions, dropdowns)):
        question_html = HTML(f"""
        <div style='background-color: #f8f9fa; border-left: 4px solid #007bff; padding: 15px; margin: 10px 0; border-radius: 0 5px 5px 0;'>
            <strong>Question {i+1}: {question['question']}</strong>
        </div>
        """)
        question_widget = VBox([question_html, dropdown], layout=Layout(margin='0 0 15px 0'))
        question_widgets.append(question_widget)

    # Create button container
    button_container = HBox([check_button, reset_button, hide_button], layout=Layout(justify_content='center', margin='20px 0'))

    # Create bottom button container for the hide button placement
    bottom_button_container = HBox([hide_button_bottom], layout=Layout(justify_content='center', margin='10px 0'))

    # Create main container
    quiz_container = VBox([
        HTML("<h4>Test your understanding before moving to implementation:</h4>")
    ] + question_widgets + [button_container, results_output, bottom_button_container],
    layout=Layout(
        border='2px solid #007bff',
        border_radius='8px',
        padding='20px',
        margin='10px 0'
    ))

    # Display the quiz
    display(quiz_container)
