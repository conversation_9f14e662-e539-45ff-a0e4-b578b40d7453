# Cross-Entropy vs. MSE in Classification

## What Are We Solving?

Your model outputs probabilities after softmax:
- Cat: 0.7, <PERSON>: 0.2, <PERSON>: 0.1

The true answer is "<PERSON>" [1, 0, 0]. How do we tell the model it was wrong?

The obvious approach: measure the distance between prediction and target — that's MSE.
So why don't we use it?

## Two Ways to Measure "Wrong"

**MSE: The Perfectionist**
- Wants the correct class at 1.0 AND all others at exactly 0.0
- If one wrong answer is 0.01, it still complains — "not perfect!"
- Penalizes every imperfection, even irrelevant ones
- Results in "safe compromises" — mediocre predictions that lower all errors slightly instead of confidently picking the right class

It's like grading a multiple-choice quiz and deducting points because you almost thought another answer looked good.

**Cross-Entropy: The Focused Coach**
- Only asks: "How confident were you in the correct answer?"
- Doesn't care about the other classes
- Gives a sharp signal: boost the correct class, and through softmax, the others go down automatically

Like a coach saying: "I don't care what you almost picked — just commit to the right answer."

## Why This Matters

MSE creates a cluttered optimization landscape full of weak, irrelevant gradients. The model gets pulled in all directions.

Cross-entropy creates a clear learning path — steep gradient when unsure, flat only when confident and correct. It trains the model to make sharp decisions, not anxious compromises.

The result? MSE builds mediocre predictions that seem acceptable but miss the sharp decisions classification actually needs. Cross-entropy builds a decisive expert that owns its answers.

## The Bottom Line

In classification, only the winner matters. Cross-entropy trains exactly for that.

**When to use what:**
- Use MSE when you care about every output value being accurate  
  *Use case: Predicting continuous values (regression)*
- Use cross-entropy when you want the model to pick one answer and commit to it  
  *Use case: Classifying into categories*

When applied to classification: MSE builds a model that hedges its bets, while cross-entropy builds a decisive classifier.