"""
Helper functions for Notebook 1.3: Professional ML Pipeline
This file contains all the utility functions used in NB1.3.
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix
import time
import sys
import json
from datetime import datetime
from pathlib import Path


def verify_data_loading(train_loader, val_loader, test_loader):
    """Verify that data loaders are correctly set up"""
    # Check that loaders exist and have correct attributes
    assert hasattr(train_loader, 'dataset'), "train_loader must have a dataset attribute"
    assert hasattr(val_loader, 'dataset'), "val_loader must have a dataset attribute"
    assert hasattr(test_loader, 'dataset'), "test_loader must have a dataset attribute"

    # Get dataset sizes
    train_size = len(train_loader.dataset)
    val_size = len(val_loader.dataset)
    test_size = len(test_loader.dataset)

    # Check expected sizes
    assert train_size == 50000, f"Expected 50,000 training samples, got {train_size}"
    assert val_size == 10000, f"Expected 10,000 validation samples, got {val_size}"
    assert test_size == 10000, f"Expected 10,000 test samples, got {test_size}"

    # Check batch size
    batch_size = train_loader.batch_size
    assert batch_size == 128, f"Expected batch_size=128, got {batch_size}"

    # Check shuffle settings
    assert train_loader.sampler.__class__.__name__ in ['RandomSampler', 'SubsetRandomSampler'], \
        "Training loader should shuffle data"
    assert val_loader.sampler.__class__.__name__ in ['SequentialSampler', 'SubsetRandomSampler'], \
        "Validation loader should not shuffle data"

    # Calculate number of batches
    num_train_batches = len(train_loader)
    num_val_batches = len(val_loader)
    num_test_batches = len(test_loader)

    print("✓ Data loaders created successfully!")
    print(f"  Training set: {train_size:,} samples")
    print(f"  Validation set: {val_size:,} samples")
    print(f"  Test set: {test_size:,} samples")
    print(f"  Batch size: {batch_size}")
    print(f"  Number of batches - Train: {num_train_batches}, Val: {num_val_batches}, Test: {num_test_batches}")


def format_time(seconds):
    """Format time in seconds to HH:MM:SS format"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    seconds = int(seconds % 60)
    return f"{hours:02d}:{minutes:02d}:{seconds:02d}"


def save_model_checkpoint(model, optimizer, epoch, val_loss, val_acc, path):
    """Save a model checkpoint with all necessary information"""
    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'val_loss': val_loss,
        'val_acc': val_acc,
        'timestamp': datetime.now().isoformat()
    }
    torch.save(checkpoint, path)
    print(f"  ✓ Checkpoint saved: {path}")


def load_model_checkpoint(model, optimizer, path):
    """Load a model checkpoint and return the metadata"""
    checkpoint = torch.load(path)
    model.load_state_dict(checkpoint['model_state_dict'])
    if optimizer is not None:
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
    return {
        'epoch': checkpoint['epoch'],
        'val_loss': checkpoint['val_loss'],
        'val_acc': checkpoint['val_acc'],
        'timestamp': checkpoint.get('timestamp', 'Unknown')
    }


def plot_training_history(train_losses, val_losses, train_accs, val_accs, best_epoch=None, save_path=None):
    """Plot training and validation curves"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

    epochs = range(1, len(train_losses) + 1)

    # Plot losses
    ax1.plot(epochs, train_losses, 'b-', label='Training Loss', linewidth=2)
    ax1.plot(epochs, val_losses, 'r-', label='Validation Loss', linewidth=2)
    if best_epoch is not None:
        ax1.axvline(x=best_epoch, color='g', linestyle='--', alpha=0.7, label=f'Best Epoch ({best_epoch})')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.set_title('Training and Validation Loss')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # Plot accuracies
    ax2.plot(epochs, train_accs, 'b-', label='Training Accuracy', linewidth=2)
    ax2.plot(epochs, val_accs, 'r-', label='Validation Accuracy', linewidth=2)
    if best_epoch is not None:
        ax2.axvline(x=best_epoch, color='g', linestyle='--', alpha=0.7, label=f'Best Epoch ({best_epoch})')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Accuracy (%)')
    ax2.set_title('Training and Validation Accuracy')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    plt.suptitle('Training History', fontsize=16)
    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"  ✓ Training curves saved to: {save_path}")

    plt.show()


def plot_confusion_matrix(y_true, y_pred, class_names=None, save_path=None):
    """Plot a confusion matrix with percentages"""
    cm = confusion_matrix(y_true, y_pred)

    # Convert to percentages
    cm_percent = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis] * 100

    plt.figure(figsize=(10, 8))
    sns.heatmap(cm_percent, annot=True, fmt='.1f', cmap='Blues',
                xticklabels=class_names or range(10),
                yticklabels=class_names or range(10))
    plt.xlabel('Predicted Label')
    plt.ylabel('True Label')
    plt.title('Confusion Matrix (Percentages)')

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"  ✓ Confusion matrix saved to: {save_path}")

    plt.show()

    return cm


def display_misclassified_examples(model, test_loader, device, num_examples=12):
    """Display a grid of misclassified examples"""
    model.eval()
    misclassified = []

    with torch.no_grad():
        for images, labels in test_loader:
            images, labels = images.to(device), labels.to(device)
            outputs = model(images)
            _, predicted = torch.max(outputs, 1)
            probs = torch.softmax(outputs, dim=1)

            # Find misclassified examples
            mask = predicted != labels
            if mask.any():
                for idx in torch.where(mask)[0]:
                    misclassified.append({
                        'image': images[idx].cpu(),
                        'true_label': labels[idx].cpu().item(),
                        'pred_label': predicted[idx].cpu().item(),
                        'confidence': probs[idx, predicted[idx]].cpu().item()
                    })

                    if len(misclassified) >= num_examples:
                        break

            if len(misclassified) >= num_examples:
                break

    # Display the misclassified examples
    if misclassified:
        rows = int(np.ceil(num_examples / 4))
        fig, axes = plt.subplots(rows, 4, figsize=(12, 3*rows))
        axes = axes.flatten()

        for i, example in enumerate(misclassified[:num_examples]):
            img = example['image'].squeeze()
            axes[i].imshow(img, cmap='gray')
            axes[i].set_title(f"True: {example['true_label']}, Pred: {example['pred_label']}\n"
                            f"Conf: {example['confidence']:.2f}")
            axes[i].axis('off')

        # Hide empty subplots
        for i in range(len(misclassified), len(axes)):
            axes[i].axis('off')

        plt.suptitle('Misclassified Examples', fontsize=16)
        plt.tight_layout()
        plt.show()
    else:
        print("No misclassified examples found!")


def create_experiment_summary(config, training_history, test_results, output_dir):
    """Create a comprehensive experiment summary"""
    summary = {
        "configuration": config,
        "training_history": {
            "final_train_loss": training_history['train_losses'][-1],
            "final_val_loss": training_history['val_losses'][-1],
            "final_train_acc": training_history['train_accuracies'][-1],
            "final_val_acc": training_history['val_accuracies'][-1],
            "best_epoch": training_history['best_epoch'],
            "best_val_acc": training_history['best_val_acc'],
            "total_epochs": len(training_history['train_losses']),
            "training_time": training_history.get('total_time', 'Not recorded')
        },
        "test_results": test_results,
        "environment": {
            "python_version": sys.version.split()[0],
            "pytorch_version": torch.__version__,
            "cuda_available": torch.cuda.is_available(),
            "device_used": config['device']
        }
    }

    # Save as JSON
    json_path = output_dir / "experiment_summary.json"
    with open(json_path, 'w') as f:
        json.dump(summary, f, indent=2)

    # Create human-readable report
    report_path = output_dir / "experiment_report.txt"
    with open(report_path, 'w') as f:
        f.write("="*60 + "\n")
        f.write("EXPERIMENT SUMMARY REPORT\n")
        f.write("="*60 + "\n\n")

        f.write(f"Timestamp: {config['timestamp']}\n")
        f.write(f"Model: {config['model_name']}\n\n")

        f.write("CONFIGURATION:\n")
        f.write("-"*30 + "\n")
        for key, value in config.items():
            f.write(f"  {key}: {value}\n")

        f.write("\nTRAINING RESULTS:\n")
        f.write("-"*30 + "\n")
        f.write(f"  Final Training Loss: {summary['training_history']['final_train_loss']:.4f}\n")
        f.write(f"  Final Training Accuracy: {summary['training_history']['final_train_acc']:.2f}%\n")
        f.write(f"  Final Validation Loss: {summary['training_history']['final_val_loss']:.4f}\n")
        f.write(f"  Final Validation Accuracy: {summary['training_history']['final_val_acc']:.2f}%\n")
        f.write(f"  Best Validation Accuracy: {summary['training_history']['best_val_acc']:.2f}% (Epoch {summary['training_history']['best_epoch']})\n")
        f.write(f"  Total Training Time: {summary['training_history']['training_time']}\n")

        f.write("\nTEST RESULTS:\n")
        f.write("-"*30 + "\n")
        f.write(f"  Test Accuracy: {test_results['test_accuracy']:.2f}%\n")
        f.write(f"  Test Loss: {test_results['test_loss']:.4f}\n")

        f.write("\nENVIRONMENT:\n")
        f.write("-"*30 + "\n")
        for key, value in summary['environment'].items():
            f.write(f"  {key}: {value}\n")

    print(f"\n✓ Experiment summary saved to:")
    print(f"  - {json_path}")
    print(f"  - {report_path}")

    return summary


def create_reproducibility_report(output_dir):
    """Create a reproducibility checklist and report"""
    report = {
        "random_seeds": {
            "numpy_seed_set": hasattr(np.random, 'get_state'),
            "torch_seed_set": torch.initial_seed() is not None,
            "torch_deterministic": torch.backends.cudnn.deterministic if torch.cuda.is_available() else "N/A",
            "torch_benchmark": not torch.backends.cudnn.benchmark if torch.cuda.is_available() else "N/A"
        },
        "environment": {
            "python_version": sys.version.split()[0],
            "pytorch_version": torch.__version__,
            "numpy_version": np.__version__,
            "cuda_available": torch.cuda.is_available(),
            "cuda_version": "Check with torch.version.cuda" if torch.cuda.is_available() else "N/A",
            "cudnn_version": torch.backends.cudnn.version() if torch.cuda.is_available() else "N/A"
        },
        "data_configuration": {
            "train_size": 50000,
            "val_size": 10000,
            "test_size": 10000,
            "batch_size": 128,
            "data_normalization": "Mean: 0.1307, Std: 0.3081"
        },
        "files_saved": {
            "configuration": "config.json",
            "model_checkpoint": "best_model.pth",
            "experiment_summary": "experiment_summary.json",
            "experiment_report": "experiment_report.txt",
            "training_curves": "training_curves.png",
            "confusion_matrix": "confusion_matrix.png"
        }
    }

    # Save reproducibility report
    repro_path = output_dir / "reproducibility_report.json"
    with open(repro_path, 'w') as f:
        json.dump(report, f, indent=2)

    # Print summary
    print("\n" + "="*60)
    print("REPRODUCIBILITY CHECKLIST")
    print("="*60)

    # Check critical items
    checks = []
    checks.append(("Random seeds set", report["random_seeds"]["numpy_seed_set"] and report["random_seeds"]["torch_seed_set"]))
    checks.append(("Configuration saved", (output_dir / "config.json").exists()))
    checks.append(("Model checkpoint saved", (output_dir / "best_model.pth").exists()))
    checks.append(("Experiment tracked", (output_dir / "experiment_summary.json").exists()))

    all_passed = True
    for check_name, passed in checks:
        status = "✓" if passed else "✗"
        print(f"  {status} {check_name}")
        if not passed:
            all_passed = False

    print("\n" + "-"*60)
    print(f"Overall Status: {'✓ REPRODUCIBLE' if all_passed else '✗ MISSING REQUIREMENTS'}")
    print("-"*60)

    print(f"\nReproducibility report saved to: {repro_path}")

    return report, all_passed
