"""
Notebook Framework for AI Course - Generic Utilities
This file contains generic verification and utility functions for all notebooks.
Notebook-specific logic is in separate task files (e.g., nb1_1_tasks.py).
"""

DEBUG = True

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader, random_split
import torchvision
import torchvision.transforms as transforms
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix
import time
import os
from datetime import datetime
import warnings
import ipywidgets as widgets
from IPython.display import display, HTML, clear_output

from .numpy_neuron_widget import create_interactive_neuron_demo
from .autograd_demo_widget import NeuralNetworkVisualizer
from .autograd_quiz_widget import create_autograd_quiz
from .neural_network_visualizers import TrainingNetworkVisualizer

# Import from new modular architecture
from .progress_recording import update_progress as _update_progress, mark_lab_complete
from .progress_verification import verify_task
from .progress_visualization import (
    show_progress_dashboard,
    show_state_based_feedback,
    get_notebook_progress_simple as _get_notebook_progress_simple,
    get_notebook_progress_display,
    check_notebook_complete
)
from .hint_system import show_help_options, get_solution, get_ai_prompt

# Wrapper functions for backward compatibility
def verify_numpy_neuron(NumpyNeuron):
    """Verify NumpyNeuron with integrated progress and hints"""
    # Use update_progress which does verification internally
    prog_success, prog_message = _update_progress('numpy_neuron', NumpyNeuron, 'NB1.1')

    if prog_success:
        print("✅ NumpyNeuron implementation is correct!")
        show_state_based_feedback('NB1.1', 'numpy_neuron')
        return True
    else:
        # Extract the verification failure message
        if "Verification failed:" in prog_message:
            message = prog_message.split("Verification failed: ", 1)[1]
        else:
            message = prog_message
        print(f"❌ NumpyNeuron verification failed: {message}")
        show_help_options('numpy_neuron')
        return False

def verify_numpy_layer(NumpyLayer):
    """Verify NumpyLayer with integrated progress and hints"""
    # Use update_progress which does verification internally
    prog_success, prog_message = _update_progress('numpy_layer', NumpyLayer, 'NB1.1')

    if prog_success:
        print("✅ NumpyLayer implementation is correct!")
        print("   Successfully vectorized computation for multiple neurons")
        show_state_based_feedback('NB1.1', 'numpy_layer')
        return True
    else:
        # Extract the verification failure message
        if "Verification failed:" in prog_message:
            message = prog_message.split("Verification failed: ", 1)[1]
        else:
            message = prog_message
        print(f"❌ NumpyLayer verification failed: {message}")
        show_help_options('numpy_layer')
        return False

def update_progress(task_name):
    """Update progress for lab/demo tasks (backward compatibility)"""
    # For non-coding tasks, use mark_lab_complete
    if task_name in ['neuron_demo', 'interactive_widget', 'layer_demo']:
        success, _ = mark_lab_complete(task_name, 'NB1.1')
        if success:
            show_state_based_feedback('NB1.1', task_name)
    else:
        # For other tasks, print a simple message
        print(f"✅ {task_name.replace('_', ' ').title()} completed!")

def show_progress():
    """Show progress dashboard (wrapper for new system)"""
    show_progress_dashboard()

def get_notebook_progress(notebook):
    """Get notebook progress with coin (wrapper for new system)"""
    return get_notebook_progress_display(notebook)

def get_notebook_progress_simple(notebook):
    """Get simplified notebook progress (wrapper for new system)"""
    return _get_notebook_progress_simple(notebook)

# Legacy compatibility - these were never actually used but are in __all__
def unlock_fruit(notebook, fruit_type, activity_name):
    """Legacy function - now handled internally by update_progress"""
    # Log a warning if this is ever called
    warnings.warn(
        f"unlock_fruit() is deprecated. Use update_progress() instead. "
        f"Called with: notebook={notebook}, fruit_type={fruit_type}, activity_name={activity_name}",
        DeprecationWarning,
        stacklevel=2
    )

def get_nb1_1_progress_simple():
    """Get NB1.1 progress in simplified format (backward compatibility)"""
    return get_notebook_progress_simple('NB1.1')

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')
warnings.filterwarnings('ignore', category=UserWarning)

# Global help level setting
HELP_LEVEL = "hints"  # Options: "hints", "prompts", "solutions"

# Dummy classes to prevent NameError crashes - these will be overridden by student implementations
class NumpyNeuron:
    pass

class NumpyLayer:
    pass

class TorchPerceptron:
    pass

def create_hint_button(hint_text):
    """Create an interactive hint button"""
    hint_button = widgets.Button(
        description="💡 Hint",
        button_style='info',
        tooltip='Click for implementation hint'
    )

    hint_output = widgets.Output()

    def on_click(b):
        with hint_output:
            clear_output()
            print("💡 Implementation Hint:")
            print(hint_text)

    hint_button.on_click(on_click)
    return widgets.VBox([hint_button, hint_output])

def create_solution_button(task_name):
    """Create an interactive solution button"""
    solution_button = widgets.Button(
        description="🔍 Solution",
        button_style='warning',
        tooltip='Click for complete solution'
    )

    solution_output = widgets.Output()

    def on_click(b):
        with solution_output:
            clear_output()
            get_solution(task_name)

    solution_button.on_click(on_click)
    return widgets.VBox([solution_button, solution_output])

def create_ai_prompt_button(task_name):
    """Create an interactive AI prompt button"""
    ai_button = widgets.Button(
        description="🤖 Ask AI",
        button_style='success',
        tooltip='Get AI prompt for this task'
    )

    ai_output = widgets.Output()

    def on_click(b):
        with ai_output:
            clear_output()
            try:
                prompt = get_ai_prompt(task_name)
                print("🤖 Copy this prompt to ask your AI:")
                print("-" * 50)
                print(prompt)
                print("-" * 50)
            except ImportError:
                print(f"🤖 Ask your AI to help you implement {task_name.replace('_', ' ')}")

    ai_button.on_click(on_click)
    return widgets.VBox([ai_button, ai_output])

def set_help_level(level):
    """Set global help level: 'hints', 'prompts', or 'solutions'"""
    global HELP_LEVEL
    if level in ['hints', 'prompts', 'solutions']:
        HELP_LEVEL = level  # type: ignore
        print(f"🎯 Help level set to: {level}")
    else:
        print("❌ Invalid help level. Use: 'hints', 'prompts', or 'solutions'")

def show_solution_button(task_name):
    """Display a solution button (for Jupyter widgets if available)"""
    try:
        button_html = f"""
        <button onclick="
            var code = `get_solution('{task_name}')`;
            var kernel = IPython.notebook.kernel;
            kernel.execute(code);
        " style="
            background-color: #ff9800;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        ">🔍 Show Solution</button>
        """
        display(HTML(button_html))
    except:
        print(f"💡 Run: get_solution('{task_name}') to see the solution")

def setup_device():
    """Setup PyTorch device (CPU/GPU/MPS)"""
    if torch.cuda.is_available():
        device = torch.device('cuda')
        device_name = torch.cuda.get_device_name(0)
        print(f"🚀 Using CUDA GPU: {device_name}")
    elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
        device = torch.device('mps')
        print("🖥️  Using Apple Metal Performance Shaders (MPS)")
    else:
        device = torch.device('cpu')
        print("💻 Using CPU")

    return device

def set_random_seeds(seed=42):
    """Set random seeds for reproducibility"""
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)
    print(f"🎲 Random seed set to {seed}")

# Generic verification helpers
def _check_torch_model(TorchPerceptron):
    """Generic PyTorch model verification"""
    try:
        model = TorchPerceptron([784, 128, 10])
        test_input = torch.randn(1, 784)
        output = model(test_input)

        if output.shape != (1, 10):
            return False

        # Check gradients
        loss = output.sum()
        loss.backward()
        return all(p.grad is not None for p in model.parameters())
    except:
        return False

def verify_torch_perceptron(TorchPerceptron):
    """Verify PyTorch Perceptron implementation"""
    if _check_torch_model(TorchPerceptron):
        print("✅ TorchPerceptron implementation is correct!")
        print("   Model architecture, forward pass, and gradients all working")
        update_progress('pytorch_model')
        return True
    else:
        print("❌ TorchPerceptron verification failed")
        print("💡 Hints:")
        print("   - Make sure to inherit from nn.Module")
        print("   - Call super().__init__() in constructor")
        print("   - Use nn.Linear for layers")
        print("   - Apply ReLU activation between layers")
        print("   - Don't apply activation to final layer")
        return False

def verify_data_loading(train_loader, val_loader, test_loader):
    """Verify data loading implementation"""
    try:
        # Check that loaders are DataLoader instances
        if not all(isinstance(loader, DataLoader) for loader in [train_loader, val_loader, test_loader]):
            print("❌ All loaders must be DataLoader instances")
            return False

        # Test getting a batch from each loader
        train_batch = next(iter(train_loader))
        val_batch = next(iter(val_loader))
        test_batch = next(iter(test_loader))

        # Check batch structure
        for batch, name in [(train_batch, 'train'), (val_batch, 'val'), (test_batch, 'test')]:
            if len(batch) != 2:
                print(f"❌ {name} batch should have 2 elements (data, targets)")
                return False
            data, targets = batch
            if data.dim() != 2:  # Should be (batch_size, 784)
                print(f"❌ {name} data should be 2D tensor (batch_size, 784)")
                return False
            if targets.dim() != 1:  # Should be (batch_size,)
                print(f"❌ {name} targets should be 1D tensor")
                return False

        print("✅ Data loading implementation is correct!")
        print(f"   Train: {len(train_loader.dataset)} samples")
        print(f"   Val: {len(val_loader.dataset)} samples")
        print(f"   Test: {len(test_loader.dataset)} samples")
        update_progress('data_loading')
        return True

    except Exception as e:
        print(f"❌ Data loading verification failed: {e}")
        print("💡 Hints:")
        print("   - Use torchvision.datasets.MNIST")
        print("   - Apply transforms.ToTensor() and transforms.Normalize((0.1307,), (0.3081,))")
        print("   - Use torch.utils.data.random_split for train/val split")
        print("   - Flatten images to 784 dimensions")
        return False

def verify_training(history, model=None):
    """Verify training loop implementation"""
    try:
        # Check history structure
        required_keys = ['train_loss', 'train_acc', 'val_loss', 'val_acc']
        if not all(key in history for key in required_keys):
            print(f"❌ History missing required keys: {required_keys}")
            return False

        # Check that we have some training data
        if len(history['train_loss']) == 0:
            print("❌ No training data recorded")
            return False

        # Check that losses generally decrease
        train_losses = history['train_loss']
        if len(train_losses) > 1 and train_losses[-1] > train_losses[0] * 1.5:
            print("⚠️  Training loss doesn't seem to be decreasing properly")

        print("✅ Training loop implementation is correct!")
        print(f"   Epochs completed: {len(history['train_loss'])}")
        print(f"   Final train loss: {history['train_loss'][-1]:.4f}")
        print(f"   Final train accuracy: {history['train_acc'][-1]:.4f}")
        print(f"   Final val loss: {history['val_loss'][-1]:.4f}")
        print(f"   Final val accuracy: {history['val_acc'][-1]:.4f}")
        update_progress('training_loop')
        return True

    except Exception as e:
        print(f"❌ Training verification failed: {e}")
        print("💡 Hints:")
        print("   - Return a dictionary with train_loss, train_acc, val_loss, val_acc lists")
        print("   - Use model.train() for training, model.eval() for validation")
        print("   - Don't forget torch.no_grad() for validation")
        return False

def verify_evaluation(test_acc, test_loss, conf_matrix):
    """Verify evaluation implementation"""
    try:
        # Check basic types and ranges
        if not (0 <= test_acc <= 1):
            print(f"❌ Test accuracy should be between 0 and 1, got {test_acc}")
            return False

        if test_loss < 0:
            print(f"❌ Test loss should be positive, got {test_loss}")
            return False

        if conf_matrix.shape != (10, 10):
            print(f"❌ Confusion matrix should be 10x10, got {conf_matrix.shape}")
            return False

        print("✅ Evaluation implementation is correct!")
        print(f"   Test accuracy: {test_acc:.4f}")
        print(f"   Test loss: {test_loss:.4f}")
        print(f"   Confusion matrix computed for 10 classes")
        update_progress('evaluation')
        return True

    except Exception as e:
        print(f"❌ Evaluation verification failed: {e}")
        print("💡 Hints:")
        print("   - Use model.eval() and torch.no_grad()")
        print("   - Calculate accuracy as correct predictions / total predictions")
        print("   - Use sklearn.metrics.confusion_matrix")
        return False

def plot_activation_functions():
    """Plot common activation functions for reference"""
    x = np.linspace(-5, 5, 100)

    plt.figure(figsize=(12, 4))

    plt.subplot(1, 3, 1)
    plt.plot(x, 1 / (1 + np.exp(-x)), 'b-', label='Sigmoid')
    plt.title('Sigmoid')
    plt.grid(True, alpha=0.3)
    plt.xlabel('Input')
    plt.ylabel('Output')

    plt.subplot(1, 3, 2)
    plt.plot(x, np.maximum(0, x), 'r-', label='ReLU')
    plt.title('ReLU')
    plt.grid(True, alpha=0.3)
    plt.xlabel('Input')
    plt.ylabel('Output')

    plt.subplot(1, 3, 3)
    plt.plot(x, np.tanh(x), 'g-', label='Tanh')
    plt.title('Tanh')
    plt.grid(True, alpha=0.3)
    plt.xlabel('Input')
    plt.ylabel('Output')

    plt.tight_layout()
    plt.show()

def show_interactive_neuron_demo(NumpyNeuron):
    """Show interactive neuron demo"""
    print("🎨 Interactive neuron demo:")
    # This would show the widget - implementation depends on the specific widget

def plot_sample_predictions(model, test_loader, device, num_samples=5):
    """Plot sample predictions from the model"""
    model.eval()

    # Get a batch of test data
    data_iter = iter(test_loader)
    images, labels = next(data_iter)
    images, labels = images.to(device), labels.to(device)

    # Get predictions
    with torch.no_grad():
        outputs = model(images)
        _, predicted = torch.max(outputs, 1)

    # Plot samples
    fig, axes = plt.subplots(1, num_samples, figsize=(12, 3))
    for i in range(num_samples):
        ax = axes[i]

        # Reshape image back to 28x28
        img = images[i].cpu().view(28, 28)
        ax.imshow(img, cmap='gray')

        true_label = labels[i].item()
        pred_label = predicted[i].item()

        color = 'green' if true_label == pred_label else 'red'
        ax.set_title(f'True: {true_label}\nPred: {pred_label}', color=color)
        ax.axis('off')

    plt.tight_layout()
    plt.show()

def get_data_loader_settings(device):
    """Get recommended data loader settings"""
    settings = {
        'batch_size': 64,
        'shuffle': True,
        'num_workers': 2 if device.type == 'cpu' else 4,
        'pin_memory': device.type != 'cpu'
    }

    print("📊 Recommended DataLoader settings:")
    for key, value in settings.items():
        print(f"   {key}: {value}")

    return settings

# Initialize device and random seeds on import
device = setup_device()
set_random_seeds(42)

# Make device and common imports available globally
__all__ = [
    'np', 'torch', 'nn', 'F', 'plt', 'sns', 'device',
    'verify_numpy_neuron', 'verify_numpy_layer', 'verify_torch_perceptron',
    'verify_data_loading', 'verify_training', 'verify_evaluation', 'update_progress',
    'get_solution', 'get_notebook_progress_simple', 'show_progress', 'plot_activation_functions',
    'create_interactive_neuron_demo', 'NeuralNetworkVisualizer', 'create_autograd_quiz',
    'TrainingNetworkVisualizer', 'plot_sample_predictions', 'get_data_loader_settings', 'set_help_level',
    'check_notebook_complete'
]
