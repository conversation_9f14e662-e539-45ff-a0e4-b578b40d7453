#!/usr/bin/env python3
# neural_network_visualizers.py
"""
Modular neural network visualization components for PyTorch models.

This module provides three main visualization classes:
1. NetworkVisualizationCore - Core network diagram visualization
2. InteractiveNetworkVisualizer - Slider-based interactive mode
3. TrainingNetworkVisualizer - Training mode with plots and loss tracking
"""

# Constants
MAX_SAMPLES_PROCESSED = 50000

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from ipywidgets import (VBox, HBox, HTML, Output, Button, FloatSlider, Layout,
                        GridBox, SelectMultiple, IntText, Label)
from IPython.display import display, clear_output
import torch.nn.functional as F


class NetworkVisualizationCore:
    """
    Core visualization component for neural network diagrams.
    Handles the network structure visualization (neurons, weights, activations).
    """

    def __init__(self, model, palette="blue-red"):
        """
        Initialize the core visualizer with a PyTorch model.

        Args:
            model: PyTorch nn.Module (expected to have nn.Sequential structure)
            palette: Color scheme "blue-red" or "red-green"
        """
        self.model = model
        self.palette = palette
        self.supported_layers = []
        self.layer_info = []
        self.setup_colors()
        self.analyze_model()
        self._fig_widget = None
        self._all_layer_metadata = []

    def setup_colors(self):
        """Set up color scheme based on palette"""
        self.COLOR_NEGATIVE = 'rgb(0,0,255)' if self.palette == "blue-red" else 'rgb(255,0,0)'
        self.COLOR_POSITIVE = 'rgb(255,0,0)' if self.palette == "blue-red" else 'rgb(0,255,0)'
        self.COLOR_NEUTRAL = 'rgb(128,128,128)'

    def analyze_model(self):
        """Analyze the PyTorch model structure and extract layer information"""
        self.layer_info = []
        self.supported_layers = []

        # Check if model has 'layers' attribute (TorchPerceptron structure)
        if hasattr(self.model, 'layers') and isinstance(self.model.layers, nn.Sequential):
            sequential = self.model.layers

            # Extract Linear layers and their sizes
            linear_layers = []
            for i, layer in enumerate(sequential):
                if isinstance(layer, nn.Linear):
                    linear_layers.append({
                        'type': 'Linear',
                        'index': i,
                        'in_features': layer.in_features,
                        'out_features': layer.out_features,
                        'weights': layer.weight.data.clone(),
                        'bias': layer.bias.data.clone() if layer.bias is not None else None
                    })
                elif isinstance(layer, (nn.ReLU, nn.Tanh, nn.Sigmoid)):
                    # Activation layers are expected and supported
                    pass
                else:
                    # Unsupported layer type
                    self.layer_info.append({
                        'type': 'Unsupported',
                        'layer_type': type(layer).__name__,
                        'index': i
                    })
                    return

            self.supported_layers = linear_layers

            # Create layer info for visualization (including input layer)
            if linear_layers:
                # Add input layer as first "layer" for visualization
                first_layer = linear_layers[0]
                self.layer_info.append({
                    'type': 'Input',
                    'neurons': first_layer['in_features'],
                    'weights': None,
                    'bias': None,
                    'input_size': first_layer['in_features']
                })

                # Add all Linear layers
                for layer in linear_layers:
                    self.layer_info.append({
                        'type': 'Linear',
                        'neurons': layer['out_features'],
                        'weights': layer['weights'],
                        'bias': layer['bias'],
                        'input_size': layer['in_features']
                    })
        else:
            # Model structure not supported
            self.layer_info.append({
                'type': 'Unsupported',
                'reason': 'Model must have nn.Sequential layers attribute'
            })

    def is_supported(self):
        """Check if the model is supported for visualization"""
        return len(self.layer_info) > 0 and all(layer['type'] != 'Unsupported' for layer in self.layer_info)

    def get_layer_activations(self, input_values):
        """
        Get actual activations for each layer by performing a forward pass through the model.

        Args:
            input_values: List of input values

        Returns:
            List of numpy arrays containing activations for each layer
        """
        if not self.is_supported():
            return []

        activations = []

        # Start with input layer activations
        if self.layer_info[0]['type'] == 'Input':
            if input_values:
                input_tensor = torch.tensor(input_values, dtype=torch.float32).unsqueeze(0)  # Add batch dimension
                activations.append(np.array(input_values))
            else:
                # No input values available - cannot proceed
                return []

        # Perform forward pass through the model
        if hasattr(self.model, 'layers') and isinstance(self.model.layers, nn.Sequential):
            x = input_tensor

            # Process each layer in the sequential model
            for module in self.model.layers:
                if isinstance(module, nn.Linear):
                    # Apply linear transformation
                    x = module(x)
                    # Store post-linear activations (before ReLU if any)
                    layer_output = x.detach().numpy().squeeze()  # Remove batch dimension
                    activations.append(layer_output)
                elif isinstance(module, (nn.ReLU, nn.Tanh, nn.Sigmoid)):
                    # Apply activation function
                    x = module(x)
                    # Update the last stored activation with post-activation values
                    if len(activations) > 1:  # Safety check
                        activations[-1] = x.detach().numpy().squeeze()

        return activations

    def create_network_visualization(self, input_values, width=800, height=500, target_values=None):
        """
        Create or update the network visualization as a FigureWidget.

        Args:
            input_values: List of input values for the network
            width: Width of the visualization
            height: Height of the visualization
            target_values: Optional target values to show error

        Returns:
            go.FigureWidget object
        """
        activations = self.get_layer_activations(input_values)

        # If model/input size changed, reset everything
        num_inputs = len(activations[0]) if self.layer_info and self.layer_info[0]['type'] == 'Input' else 0
        if (
            self._fig_widget is None or
            not hasattr(self, '_last_num_inputs') or
            self._last_num_inputs != num_inputs
        ):
            self._fig_widget = go.FigureWidget()
            self._all_layer_metadata = []
            self._last_num_inputs = num_inputs

            # Add a trace that CHANGES with input values to trigger Plotly redraws
            all_values = []
            for act in activations:
                all_values.extend(act.tolist())
            self._fig_widget.add_trace(go.Scatter(
                x=[0],
                y=[sum(all_values)],
                mode='markers',
                marker=dict(size=0.001, opacity=0),
                showlegend=False,
                hoverinfo='skip'
            ))

            # Custom vertical positions: input at 0, first neuron at 2.2, rest at increments of 1.6
            n_layers = len(self.layer_info)
            layer_y_positions = [0]
            if n_layers > 1:
                layer_y_positions += [2.2 + (i-1)*1.6 for i in range(1, n_layers)]

            # Draw each layer
            for layer_idx, (layer_info, layer_activations, y_pos) in enumerate(zip(self.layer_info, activations, layer_y_positions)):
                if layer_info['type'] == 'Input':
                    self._draw_input_layer(self._fig_widget, layer_activations, y_pos, layer_idx)
                else:
                    self._draw_neural_layer(self._fig_widget, layer_activations, y_pos, layer_idx)

            # Draw weight connections between layers
            self._draw_weight_connections(self._fig_widget, layer_y_positions)

            # Set up the figure layout with tighter bounds to remove empty space
            self._fig_widget.update_xaxes(
                range=[-2.5, 2.5], showgrid=False, zeroline=False, showticklabels=False, fixedrange=True
            )
            self._fig_widget.update_yaxes(
                range=[-0.5, layer_y_positions[-1] + 0.8],
                showgrid=False, zeroline=False, showticklabels=False, fixedrange=True,
                scaleanchor="x", scaleratio=1
            )
            self._fig_widget.update_layout(
                width=width, height=height, showlegend=False,
                margin=dict(l=5, r=5, t=10, b=0),
                title="",
                plot_bgcolor='white'
            )

            # Add error box if target values provided
            if target_values is not None:
                # Calculate error
                outputs = self.model(torch.tensor([input_values], dtype=torch.float32))
                error = np.mean(np.abs(outputs.detach().numpy()[0] - target_values))

                # Add error text annotation in top-right corner
                self._fig_widget.add_annotation(
                    x=0.95, y=0.95,
                    text=f"Error: {error:.4f}",
                    showarrow=False,
                    xref="paper", yref="paper",
                    xanchor="right", yanchor="top",
                    bgcolor="lightgray",
                    bordercolor="black",
                    borderwidth=1,
                    font=dict(size=10)
                )
        else:
            # Update existing visualization
            self._update_all_layers(activations)

        return self._fig_widget

    def _draw_input_layer(self, fig, activations, y_center, layer_idx):
        """Draw input layer as simple grey knobs with VU meter scales and needles"""
        num_knobs = len(activations)
        knob_width = 0.8
        knob_height = 0.8
        spacing = 1.0
        total_width = (num_knobs - 1) * spacing
        start_x = -total_width / 2

        for i, value in enumerate(activations):
            x_center = start_x + i * spacing
            meter_radius = knob_width * 0.35
            knob_y_center = y_center + 0.2

            # Box
            fig.add_shape(
                type="rect",
                x0=x_center - knob_width/2,
                y0=y_center - knob_height/2 + 0.2,
                x1=x_center + knob_width/2,
                y1=y_center + knob_height/2 + 0.2,
                fillcolor='rgb(200,200,200)',
                line=dict(color='rgb(120,120,120)', width=3),
            )

            # Circle
            fig.add_shape(
                type="circle",
                x0=x_center - meter_radius,
                y0=knob_y_center - meter_radius,
                x1=x_center + meter_radius,
                y1=knob_y_center + meter_radius,
                fillcolor='white',
                line=dict(color='black', width=0.5),
            )

            # Scale marks
            for angle_deg in [-90, -45, 0, 45, 90]:
                angle_rad = np.radians(angle_deg + 90)
                scale_radius = meter_radius * 0.85
                x_mark = x_center + scale_radius * np.cos(angle_rad)
                y_mark = knob_y_center + scale_radius * np.sin(angle_rad)
                x_end = x_center + meter_radius * 0.95 * np.cos(angle_rad)
                y_end = knob_y_center + meter_radius * 0.95 * np.sin(angle_rad)
                fig.add_shape(
                    type="line",
                    x0=x_mark, y0=y_mark,
                    x1=x_end, y1=y_end,
                    line=dict(color='black', width=0.5)
                )

            # Needle
            capped_value = np.clip(value, -1.5, 1.5)
            needle_angle_deg = -capped_value * 90
            needle_angle_rad = np.radians(needle_angle_deg + 90)
            needle_length = meter_radius * 0.75
            needle_width = meter_radius * 0.1
            x_tip = x_center + needle_length * np.cos(needle_angle_rad)
            y_tip = knob_y_center + needle_length * np.sin(needle_angle_rad)
            base_angle = needle_angle_rad + np.pi/2
            x_base1 = x_center + needle_width * np.cos(base_angle)
            y_base1 = knob_y_center + needle_width * np.sin(base_angle)
            x_base2 = x_center - needle_width * np.cos(base_angle)
            y_base2 = knob_y_center - needle_width * np.sin(base_angle)
            needle_path = f"M {x_tip} {y_tip} L {x_base1} {y_base1} L {x_base2} {y_base2} Z"
            needle_idx = len(fig.layout.shapes)
            fig.add_shape(
                type="path",
                path=needle_path,
                fillcolor='rgba(64,64,64,1)',
                line=dict(color='rgba(32,32,32,1)', width=0.5)
            )

            # Store metadata for updates
            self._all_layer_metadata.append((layer_idx, i, needle_idx, x_center, knob_y_center, meter_radius))

            # Value text
            fig.add_annotation(
                x=x_center,
                y=y_center - knob_height/2 - 0.05,
                text=f'{value:.2f}',
                showarrow=False,
                font=dict(size=10, color='black', family='monospace')
            )

    def _draw_neural_layer(self, fig, activations, y_center, layer_idx):
        """Draw neural layer with VU meters (horizontal arrangement)"""
        num_neurons = len(activations)
        neuron_width = 0.4
        neuron_height = 0.4
        spacing = 0.5

        # Calculate positions for centered layout
        total_width = (num_neurons - 1) * spacing
        start_x = -total_width / 2

        for i, value in enumerate(activations):
            x_center = start_x + i * spacing

            # Get colors based on value intensity
            intensity = abs(value)

            if intensity < 0.05:
                border_color = 'rgba(160,160,160,0.8)'
                bg_color = 'rgba(220,220,220,0.4)'
            elif value > 0:
                if self.palette == "blue-red":
                    border_color = f'rgba(255,0,0,{0.4 + intensity*0.6})'
                    bg_color = f'rgba(255,200,200,{0.3 + intensity*0.4})'
                else:
                    border_color = f'rgba(0,255,0,{0.4 + intensity*0.6})'
                    bg_color = f'rgba(200,255,200,{0.3 + intensity*0.4})'
            else:
                if self.palette == "blue-red":
                    border_color = f'rgba(0,0,255,{0.4 + intensity*0.6})'
                    bg_color = f'rgba(200,200,255,{0.3 + intensity*0.4})'
                else:
                    border_color = f'rgba(255,0,0,{0.4 + intensity*0.6})'
                    bg_color = f'rgba(255,200,200,{0.3 + intensity*0.4})'

            # Neuron rectangle
            fig.add_shape(
                type="rect",
                x0=x_center - neuron_width/2,
                y0=y_center - neuron_height/2,
                x1=x_center + neuron_width/2,
                y1=y_center + neuron_height/2,
                fillcolor=bg_color,
                line=dict(color=border_color, width=2),
            )

            # VU meter background
            meter_radius = min(neuron_width, neuron_height) * 0.35
            fig.add_shape(
                type="circle",
                x0=x_center - meter_radius,
                y0=y_center - meter_radius,
                x1=x_center + meter_radius,
                y1=y_center + meter_radius,
                fillcolor='white',
                line=dict(color='black', width=0.5),
            )

            # VU meter needle
            capped_value = np.clip(value, -1.5, 1.5)
            needle_angle_deg = -capped_value * 90
            needle_angle_rad = np.radians(needle_angle_deg + 90)
            needle_length = meter_radius * 0.75
            needle_width = meter_radius * 0.1

            x_tip = x_center + needle_length * np.cos(needle_angle_rad)
            y_tip = y_center + needle_length * np.sin(needle_angle_rad)

            base_angle = needle_angle_rad + np.pi/2
            x_base1 = x_center + needle_width * np.cos(base_angle)
            y_base1 = y_center + needle_width * np.sin(base_angle)
            x_base2 = x_center - needle_width * np.cos(base_angle)
            y_base2 = y_center - needle_width * np.sin(base_angle)

            needle_path = f"M {x_tip} {y_tip} L {x_base1} {y_base1} L {x_base2} {y_base2} Z"
            needle_idx = len(fig.layout.shapes)
            fig.add_shape(
                type="path",
                path=needle_path,
                fillcolor='rgba(64,64,64,1)',
                line=dict(color='rgba(32,32,32,1)', width=0.5)
            )

            # Store metadata for updates
            self._all_layer_metadata.append((layer_idx, i, needle_idx, x_center, y_center, meter_radius))

            # Value text for output layer only
            if layer_idx == len(self.layer_info) - 1:  # Output layer
                fig.add_annotation(
                    x=x_center,
                    y=y_center + neuron_height/2 + 0.15,
                    text=f'{value:.3f}',
                    showarrow=False,
                    font=dict(size=8, color='black', family='monospace')
                )

    def _draw_weight_connections(self, fig, layer_y_positions):
        """Draw colored lines representing weights between layers"""
        # For each pair of adjacent layers
        for layer_idx in range(len(self.layer_info) - 1):
            from_layer = self.layer_info[layer_idx]
            to_layer = self.layer_info[layer_idx + 1]

            # Skip if next layer is not a neural layer
            if to_layer['type'] != 'Linear':
                continue

            # Get weight matrix for this connection
            weights = to_layer['weights']  # Shape: (out_features, in_features)

            # Calculate neuron positions for both layers
            from_y = layer_y_positions[layer_idx]
            to_y = layer_y_positions[layer_idx + 1]

            # From layer positions
            if from_layer['type'] == 'Input':
                # Input layer uses different spacing
                num_from = from_layer['neurons']
                from_spacing = 1.0
                from_width = (num_from - 1) * from_spacing
                from_start_x = -from_width / 2
                knob_width = 0.8
                knob_height = 0.8
                # Start from top edge of input boxes
                from_positions = [(from_start_x + i * from_spacing, from_y + 0.2 + knob_height/2) for i in range(num_from)]
            else:
                # Neural layers
                num_from = from_layer['neurons']
                from_spacing = 0.5
                from_width = (num_from - 1) * from_spacing
                from_start_x = -from_width / 2
                neuron_width = 0.4
                neuron_height = 0.4
                # Start from top edge of neuron boxes
                from_positions = [(from_start_x + i * from_spacing, from_y + neuron_height/2) for i in range(num_from)]

            # To layer positions (always neural layer)
            num_to = to_layer['neurons']
            to_spacing = 0.5
            to_width = (num_to - 1) * to_spacing
            to_start_x = -to_width / 2
            neuron_width = 0.4
            neuron_height = 0.4
            # End at bottom edge of neuron boxes
            to_positions = [(to_start_x + i * to_spacing, to_y - neuron_height/2) for i in range(num_to)]

            # Draw connections
            for to_idx, (to_x, to_y_pos) in enumerate(to_positions):
                for from_idx, (from_x, from_y_pos) in enumerate(from_positions):
                    # Get weight value
                    weight = weights[to_idx, from_idx].item()

                    # Calculate color and width based on weight
                    weight_intensity = min(abs(weight), 1.0)
                    line_width = max(0.5, min(4.5, abs(weight) * 3))

                    if abs(weight) < 0.05:
                        line_color = 'rgba(160,160,160,0.5)'
                    elif weight > 0:
                        if self.palette == "blue-red":
                            line_color = f'rgba(255,0,0,{0.4 + weight_intensity * 0.6})'
                        else:
                            line_color = f'rgba(0,255,0,{0.4 + weight_intensity * 0.6})'
                    else:
                        if self.palette == "blue-red":
                            line_color = f'rgba(0,0,255,{0.4 + weight_intensity * 0.6})'
                        else:
                            line_color = f'rgba(255,0,0,{0.4 + weight_intensity * 0.6})'

                    # Draw line
                    fig.add_shape(
                        type="line",
                        x0=from_x, y0=from_y_pos,
                        x1=to_x, y1=to_y_pos,
                        line=dict(color=line_color, width=line_width)
                    )

    def _update_all_layers(self, activations):
        """Efficiently update ALL layer shapes (needles, colors, value texts) in the FigureWidget"""
        if not self._fig_widget or not self._all_layer_metadata:
            return

        with self._fig_widget.batch_update():
            # Update each neuron's needle based on its activation
            for layer_idx, layer_activations in enumerate(activations):
                for neuron_idx, value in enumerate(layer_activations):
                    # Find the metadata for this neuron
                    for meta in self._all_layer_metadata:
                        if meta[0] == layer_idx and meta[1] == neuron_idx:
                            _, _, needle_idx, x_center, y_center, meter_radius = meta
                            # Update needle
                            capped_value = np.clip(value, -1.5, 1.5)
                            needle_angle_deg = -capped_value * 90
                            needle_angle_rad = np.radians(needle_angle_deg + 90)
                            needle_length = meter_radius * 0.75
                            needle_width = meter_radius * 0.1
                            x_tip = x_center + needle_length * np.cos(needle_angle_rad)
                            y_tip = y_center + needle_length * np.sin(needle_angle_rad)
                            base_angle = needle_angle_rad + np.pi/2
                            x_base1 = x_center + needle_width * np.cos(base_angle)
                            y_base1 = y_center + needle_width * np.sin(base_angle)
                            x_base2 = x_center - needle_width * np.cos(base_angle)
                            y_base2 = y_center - needle_width * np.sin(base_angle)
                            needle_path = f"M {x_tip} {y_tip} L {x_base1} {y_base1} L {x_base2} {y_base2} Z"
                            self._fig_widget.layout.shapes[needle_idx].path = needle_path  # type: ignore

                            # Update neuron box colors for non-input layers
                            if layer_idx > 0:  # Not input layer
                                # Find the neuron box (should be 2 shapes before the needle)
                                box_idx = needle_idx - 2
                                if box_idx >= 0:
                                    intensity = abs(value)
                                    if intensity < 0.05:
                                        border_color = 'rgba(160,160,160,0.8)'
                                        bg_color = 'rgba(220,220,220,0.4)'
                                    elif value > 0:
                                        if self.palette == "blue-red":
                                            border_color = f'rgba(255,0,0,{0.4 + intensity*0.6})'
                                            bg_color = f'rgba(255,200,200,{0.3 + intensity*0.4})'
                                        else:
                                            border_color = f'rgba(0,255,0,{0.4 + intensity*0.6})'
                                            bg_color = f'rgba(200,255,200,{0.3 + intensity*0.4})'
                                    else:
                                        if self.palette == "blue-red":
                                            border_color = f'rgba(0,0,255,{0.4 + intensity*0.6})'
                                            bg_color = f'rgba(200,200,255,{0.3 + intensity*0.4})'
                                        else:
                                            border_color = f'rgba(255,0,0,{0.4 + intensity*0.6})'
                                            bg_color = f'rgba(255,200,200,{0.3 + intensity*0.4})'

                                    # Update box colors
                                    self._fig_widget.layout.shapes[box_idx].fillcolor = bg_color  # type: ignore
                                    self._fig_widget.layout.shapes[box_idx].line.color = border_color  # type: ignore
                            break

            # Update value text for ALL layers
            annotation_idx = 0
            for layer_idx, layer_activations in enumerate(activations):
                # Only update text for input layer (first) and output layer (last)
                if layer_idx == 0 or layer_idx == len(activations) - 1:
                    for neuron_idx, value in enumerate(layer_activations):
                        if annotation_idx < len(self._fig_widget.layout.annotations):  # type: ignore
                            # Format based on layer type
                            if layer_idx == 0:  # Input layer
                                text = f'{value:.2f}'
                            else:  # Output layer
                                text = f'{value:.3f}'
                            self._fig_widget.layout.annotations[annotation_idx].text = text  # type: ignore
                            annotation_idx += 1


class InteractiveNetworkVisualizer:
    """
    Original slider-based interactive visualization.
    Preserves all the functionality from the original NeuralNetworkVisualizer.
    """

    def __init__(self, model, palette="blue-red"):
        """
        Initialize the interactive visualizer with a PyTorch model.

        Args:
            model: PyTorch nn.Module
            palette: Color scheme "blue-red" or "red-green"
        """
        self.viz_core = NetworkVisualizationCore(model, palette)
        self.model = model
        self.input_values = []
        self.input_sliders = []
        self.network_output = None
        self.slider_box = None
        self.initialize_input_values()

    def initialize_input_values(self):
        """Initialize input values for interactivity"""
        if self.viz_core.is_supported() and len(self.viz_core.layer_info) > 0:
            # Get the number of input features from the first layer (Input layer)
            input_layer = self.viz_core.layer_info[0]
            if input_layer['type'] == 'Input':
                num_inputs = input_layer['neurons']
                # Initialize with zeros (neutral starting point)
                self.input_values = [0.0] * num_inputs
            else:
                self.input_values = []
        else:
            self.input_values = []

    def create_input_sliders(self):
        """Create interactive sliders for input values (centered, fixed-width, max 12)"""
        if not self.input_values:
            return

        max_sliders = 12
        n = len(self.input_values)
        if n > max_sliders:
            raise ValueError(f"Maximum supported input dials/sliders is {max_sliders}, got {n}.")

        self.input_sliders = []
        for i, value in enumerate(self.input_values):
            slider = FloatSlider(
                value=value,
                min=-1.0,
                max=1.0,
                step=0.01,
                description='',
                readout=False,
                orientation='vertical',
                continuous_update=False,
                layout=Layout(width='40px', height='60px', margin='0 auto')
            )
            def make_callback(index):
                def callback(change):
                    self.input_values[index] = change['new']
                    self.update_visualization()
                return callback
            slider.observe(make_callback(i), names='value')
            self.input_sliders.append(slider)

        # Center the sliders in a 12-column grid
        empty_cells = max_sliders - n
        left_empty = empty_cells // 2
        right_empty = empty_cells - left_empty
        from ipywidgets import Box
        grid_children = [Box([]) for _ in range(left_empty)] + self.input_sliders + [Box([]) for _ in range(right_empty)]
        self.slider_box = GridBox(
            grid_children,
            layout=Layout(
                grid_template_columns='repeat(12, 1fr)',
                justify_items='center',
                margin='0 0 10px 0',
                padding='0',
                width='800px',
                height='100px'
            )
        )

    def update_visualization(self):
        """Update the visualization when input values change"""
        if self.network_output:
            with self.network_output:
                clear_output(wait=True)
                fig = self.viz_core.create_network_visualization(self.input_values)
                if fig:
                    display(fig)

    def create_widget(self):
        """Create the interactive widget for the neural network"""
        # Check if model is supported
        if not self.viz_core.is_supported():
            error_msg = "❌ <b>Unsupported Model Structure</b><br><br>"

            if self.viz_core.layer_info and self.viz_core.layer_info[0]['type'] == 'Unsupported':
                if 'reason' in self.viz_core.layer_info[0]:
                    error_msg += f"Reason: {self.viz_core.layer_info[0]['reason']}<br><br>"
                elif 'layer_type' in self.viz_core.layer_info[0]:
                    error_msg += f"Unsupported layer type: {self.viz_core.layer_info[0]['layer_type']}<br><br>"

            error_msg += """
            <b>Supported model structure:</b><br>
            • Must inherit from nn.Module<br>
            • Must have 'layers' attribute with nn.Sequential<br>
            • Layers must contain only nn.Linear and nn.ReLU<br><br>

            <b>Example:</b><br>
            <code>
            class TorchPerceptron(nn.Module):<br>
            &nbsp;&nbsp;def __init__(self, layer_sizes):<br>
            &nbsp;&nbsp;&nbsp;&nbsp;super().__init__()<br>
            &nbsp;&nbsp;&nbsp;&nbsp;self.layers = nn.Sequential(...)<br>
            </code>
            """

            display(HTML(f"<h3>🚀 Neural Network Visualizer</h3>{error_msg}"))
            return

        # Create interactive sliders for input layer
        if len(self.viz_core.layer_info) > 0 and self.viz_core.layer_info[0]['type'] == 'Input':
            self.create_input_sliders()

        # Create output widget
        self.network_output = Output(layout=Layout(width='820px', height='520px'))

        # Model info
        total_params = sum(layer['weights'].numel() + (layer['bias'].numel() if layer['bias'] is not None else 0)
                          for layer in self.viz_core.layer_info if layer['weights'] is not None)

        model_info = f"""
        <b>Model Analysis:</b><br>
        • {len(self.viz_core.layer_info)} layers total (including input)<br>
        • Architecture: {' → '.join([str(layer['neurons']) for layer in self.viz_core.layer_info])}<br>
        • Total parameters: {total_params}<br>
        """

        # Display everything
        display(HTML(f"<h3>🚀 Interactive Neural Network Visualizer</h3>{model_info}"))
        display(self.network_output)

        # Initial visualization
        self.update_visualization()

        # Add sliders below the visualization
        if hasattr(self, 'slider_box') and self.slider_box is not None:
            display(self.slider_box)


class SimpleArithmeticDataset:
    """Generate complex 1D dataset for training: outputs are -x/2 and sin(x*freq)"""

    def __init__(self, size, sine_frequency=3, sine_amplitude=1.0):
        self.size = size
        self.sine_frequency = sine_frequency
        self.sine_amplitude = sine_amplitude

    def generate_samples(self):
        """Generate random samples"""
        # Single input in range [-1, 1]
        inputs = np.random.uniform(-1, 1, (self.size, 1))

        # Two outputs: -x/2 and sin(x*freq)
        targets = np.zeros((self.size, 2))
        targets[:, 0] = -inputs[:, 0] / 2  # First output: -x/2
        targets[:, 1] = self.sine_amplitude * np.sin(inputs[:, 0] * self.sine_frequency)  # Second output: amplitude * sin(x*freq)

        return inputs, targets

    def generate_grid_samples(self, n_points=100):
        """Generate evenly spaced samples for validation/plotting"""
        inputs = np.linspace(-1, 1, n_points).reshape(-1, 1)

        targets = np.zeros((n_points, 2))
        targets[:, 0] = -inputs[:, 0] / 2  # First output: -x/2
        targets[:, 1] = self.sine_amplitude * np.sin(inputs[:, 0] * self.sine_frequency)  # Second output: amplitude * sin(x*freq)

        return inputs, targets


class TrainingNetworkVisualizer:
    """
    Training visualization with 3D plots, heatmaps, and loss tracking.
    Shows the network learning arithmetic operations.
    """

    def __init__(self, layer_sizes, train_size=16, val_size=400, batch_size=8,
                 learning_rate=0.001, palette="blue-red", activation_function="Tanh", sine_frequency=3, sine_amplitude=1.0):
        """
        Initialize the training visualizer.

        Args:
            layer_sizes: List of layer sizes (e.g., [1, 8, 8, 2])
            train_size: Number of training samples
            val_size: Grid size for validation (will be val_size points)
            batch_size: Batch size for training
            learning_rate: Learning rate for optimizer
            palette: Color scheme for network visualization
            activation_function: Activation function ("ReLU", "Tanh", "Sigmoid")
            sine_frequency: Frequency multiplier for the sine function (default 3)
            sine_amplitude: Amplitude multiplier for the sine function (default 1.0)
        """
        # Create model with specified activation function
        self.layer_sizes = layer_sizes
        self.activation_function = activation_function
        self.sine_frequency = sine_frequency
        self.sine_amplitude = sine_amplitude
        self.model = self._create_model(layer_sizes, activation_function)

        self.viz_core = NetworkVisualizationCore(self.model, palette)
        self.train_size = train_size
        self.val_size = int(np.sqrt(val_size))  # Convert to grid size
        self.batch_size = batch_size
        self.learning_rate = learning_rate

        # Training components
        # Use Adam optimizer with good defaults
        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=learning_rate,
            betas=(0.9, 0.999),
            eps=1e-8,
            weight_decay=0  # Remove weight decay - it can hurt on small problems
        )
        self.criterion = nn.MSELoss()

        # Data storage
        self.train_dataset = SimpleArithmeticDataset(train_size, sine_frequency, sine_amplitude)
        self.val_dataset = SimpleArithmeticDataset(val_size, sine_frequency, sine_amplitude)
        self.current_epoch = 0
        self.current_batch = 0
        self.current_sample = 0
        self.total_samples_processed = 0  # Track total samples for X-axis
        self.loss_history = []  # Will store (samples_processed, loss) tuples
        self.train_predictions = []  # Store (input, output) for plotting

        # Generate datasets
        self.train_inputs, self.train_targets = self.train_dataset.generate_samples()
        self.val_inputs, self.val_targets = self.val_dataset.generate_grid_samples()

        # Widgets
        self.network_output = None
        self.sample_list = None
        self.plots_output = None
        self.loss_output = None
        self._plot_widgets = {}  # Store plot widgets for updates

    def create_sample_list(self):
        """Create a scrollable list of training samples"""
        # Format samples for display
        sample_strings = []
        for i, (inp, tgt) in enumerate(zip(self.train_inputs, self.train_targets)):
            sample_str = f"Sample {i:3d}: ({inp[0]:6.3f}) → [{tgt[0]:6.3f}, {tgt[1]:6.3f}]"
            sample_strings.append(sample_str)

        self.sample_list = SelectMultiple(
            options=sample_strings,
            value=[sample_strings[0]] if sample_strings else [],
            rows=10,
            description='',  # Remove the label
            layout=Layout(width='440px', height='200px')  # 1/3 of 620px
        )

        # Add unselect button
        self.unselect_button = Button(
            description='Clear Selection',
            button_style='',
            layout=Layout(width='120px', height='25px')
        )
        self.unselect_button.on_click(lambda b: setattr(self.sample_list, 'value', []))

        # Add observer to update network visualization when selection changes
        def on_sample_select(change):
            if change['new'] and len(change['new']) > 0:
                # Get the index from the selected string
                selected_str = change['new'][0]
                sample_idx = int(selected_str.split(':')[0].split()[1])
                # Update network visualization with selected sample and target
                self.update_network_visualization(self.train_inputs[sample_idx], self.train_targets[sample_idx])
                # Update selected sample markers in behavior plots
                self.update_selected_sample_markers(change['new'])
            else:
                # No selection - use zero input as neutral default
                zero_input = np.array([0.0])
                self.update_network_visualization(zero_input)
                self.update_selected_sample_markers([])

        self.sample_list.observe(on_sample_select, names='value')

    def _create_model(self, layer_sizes, activation_function):
        """Create a neural network model with specified activation function"""
        layers = []
        for i in range(len(layer_sizes) - 1):
            linear_layer = nn.Linear(layer_sizes[i], layer_sizes[i+1])

            # Apply appropriate initialization based on activation function
            if activation_function == "ReLU":
                nn.init.kaiming_normal_(linear_layer.weight, mode='fan_in', nonlinearity='relu')
                nn.init.zeros_(linear_layer.bias)
            elif activation_function == "Tanh":
                # Use proper gain for Tanh activation
                # Calculate gain using PyTorch's recommended value for tanh
                gain = nn.init.calculate_gain('tanh')  # This returns ~1.67
                nn.init.xavier_uniform_(linear_layer.weight, gain=gain)
                # Small random bias to break symmetry
                nn.init.uniform_(linear_layer.bias, -0.01, 0.01)
            elif activation_function == "Sigmoid":
                # Sigmoid benefits from uniform initialization and small biases
                nn.init.xavier_uniform_(linear_layer.weight, gain=1.0)
                # For Sigmoid, small random bias can help prevent saturation
                nn.init.uniform_(linear_layer.bias, -0.1, 0.1)

            layers.append(linear_layer)

            # Add activation after all layers except the last
            if i < len(layer_sizes) - 2:
                if activation_function == "ReLU":
                    layers.append(nn.ReLU())
                elif activation_function == "Tanh":
                    layers.append(nn.Tanh())
                elif activation_function == "Sigmoid":
                    layers.append(nn.Sigmoid())

        # Create a simple model class
        class DynamicModel(nn.Module):
            def __init__(self, layers):
                super().__init__()
                self.layers = nn.Sequential(*layers)

            def forward(self, x):
                return self.layers(x)

        return DynamicModel(layers)

    def create_activation_dropdown(self):
        """Create dropdown for selecting activation function"""
        from ipywidgets import Dropdown

        self.activation_dropdown = Dropdown(
            options=['ReLU', 'Tanh', 'Sigmoid'],
            value=self.activation_function,
            description='Activation:',
            style={'description_width': 'initial'},
            layout=Layout(width='200px')
        )
        self.activation_dropdown.observe(self.on_activation_change, names='value')
        return self.activation_dropdown

    def on_activation_change(self, change):
        """Handle activation function change"""
        if change['new'] != self.activation_function:
            self.activation_function = change['new']
            # Recreate model with new activation function
            self.model = self._create_model(self.layer_sizes, self.activation_function)

            # Ensure model has parameters before creating optimizer
            if list(self.model.parameters()):
                # Recreate optimizer for new model parameters
                self.optimizer = optim.Adam(
                    self.model.parameters(),
                    lr=self.learning_rate,
                    betas=(0.9, 0.999),
                    eps=1e-8,
                    weight_decay=0
                )
            else:
                raise ValueError(f"Model has no parameters after creation with activation {self.activation_function}")

            # Update visualization core
            self.viz_core = NetworkVisualizationCore(self.model, self.viz_core.palette)
            # Reset training state
            self.current_epoch = 0
            self.current_batch = 0
            self.current_sample = 0
            self.total_samples_processed = 0
            self.loss_history = []
            self.train_predictions = []
            # Update visualizations
            self.update_network_visualization(self.train_inputs[0] if len(self.train_inputs) > 0 else np.array([0.0]))
            self.add_training_points()
            self.update_validation_plots()
            self.record_initial_loss()

    def create_control_buttons(self):
        """Create training control buttons"""
        # Button width calculation: 940px container / 6 buttons = ~156px each
        button_width = '140px'

        self.step_button = Button(
            description='Step (1 sample)',
            button_style='primary',
            layout=Layout(width=button_width)
        )
        self.step_button.on_click(self.on_step_click)

        self.batch_button = Button(
            description=f'Batch ({self.batch_size} samples)',
            button_style='info',
            layout=Layout(width=button_width)
        )
        self.batch_button.on_click(self.on_batch_click)

        self.epoch_button = Button(
            description='Epoch (all samples)',
            button_style='success',
            layout=Layout(width=button_width)
        )
        self.epoch_button.on_click(self.on_epoch_click)

        self.epoch_10x_button = Button(
            description='10x Epochs',
            button_style='warning',
            layout=Layout(width=button_width)
        )
        self.epoch_10x_button.on_click(self.on_epoch_10x_click)

        self.epoch_100x_button = Button(
            description='100x Epochs',
            button_style='warning',
            layout=Layout(width=button_width)
        )
        self.epoch_100x_button.on_click(self.on_epoch_100x_click)

        self.run_to_50k_button = Button(
            description='Run to 50k',
            button_style='',  # Default style, will customize with CSS
            layout=Layout(width=button_width)
        )
        self.run_to_50k_button.on_click(self.on_run_to_50k_click)
        # Set darker orange style
        self.run_to_50k_button.style.button_color = '#cc7a00'  # Darker orange

        self.reinit_button = Button(
            description='Re-Initialize',
            button_style='danger',
            layout=Layout(width='120px')
        )
        self.reinit_button.on_click(self.on_reinit_click)

        # Update buttons removed - plots now update automatically after training

        return HBox([
            self.step_button,
            self.batch_button,
            self.epoch_button,
            self.epoch_10x_button,
            self.epoch_100x_button,
            self.run_to_50k_button
        ], layout=Layout(width='940px', justify_content='center', margin='10px 0'))

    def train_step(self, inputs, targets):
        """Perform a single training step"""
        self.optimizer.zero_grad()

        # Forward pass
        outputs = self.model(inputs)

        # Compute loss with optional per-output weighting
        # This helps balance learning between simple (-x/2) and complex (sin) outputs
        loss_output1 = self.criterion(outputs[:, 0], targets[:, 0])
        loss_output2 = self.criterion(outputs[:, 1], targets[:, 1])

        # Weight the sine loss more heavily since it's harder to learn
        #loss = loss_output1 + 2.0 * loss_output2
        loss = loss_output1 + 1.0 * loss_output2  # TODO

        # Backward pass
        loss.backward()

        # Check gradient norms before clipping (for debugging)
        if hasattr(self, '_debug_gradients') and self._debug_gradients:  # type: ignore
            total_norm = 0
            for p in self.model.parameters():
                if p.grad is not None:
                    param_norm = p.grad.data.norm(2)
                    total_norm += param_norm.item() ** 2
            total_norm = total_norm ** 0.5
            print(f"Gradient norm before clipping: {total_norm:.4f}")

        # Gradient clipping to prevent exploding gradients
        # Using a higher threshold to allow more gradient flow for complex functions
        torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=10.0)

        self.optimizer.step()

        return outputs, loss.item()

    def on_step_click(self, b):
        """Handle single step button click"""
        if self.total_samples_processed >= MAX_SAMPLES_PROCESSED:
            return

        # Disable buttons during computation
        self.step_button.disabled = True
        self.batch_button.disabled = True
        self.epoch_button.disabled = True
        self.epoch_10x_button.disabled = True
        self.epoch_100x_button.disabled = True
        self.run_to_50k_button.disabled = True
        self.reinit_button.disabled = True

        if self.current_sample >= self.train_size:
            self.current_sample = 0
            self.train_predictions = []  # Clear predictions when wrapping around

        # Get current sample
        inp = torch.tensor(self.train_inputs[self.current_sample:self.current_sample+1], dtype=torch.float32)
        tgt = torch.tensor(self.train_targets[self.current_sample:self.current_sample+1], dtype=torch.float32)

        # Train on this sample
        outputs, loss = self.train_step(inp, tgt)

        # Store prediction for plotting
        self.train_predictions.append((
            self.train_inputs[self.current_sample],
            outputs.detach().numpy()[0]
        ))

        # Update sample list selection
        if self.sample_list:
            self.sample_list.value = [self.sample_list.options[self.current_sample]]

        # Update visualization
        self.update_network_visualization(self.train_inputs[self.current_sample])
        self.add_training_points()
        self.update_validation_plots()

        # Select the sample that was just trained
        sample_str = f"Sample {self.current_sample:3d}: ({self.train_inputs[self.current_sample][0]:6.3f}) → [{self.train_targets[self.current_sample][0]:6.3f}, {self.train_targets[self.current_sample][1]:6.3f}]"
        self.sample_list.value = [sample_str]  # type: ignore

        self.current_sample += 1
        self.total_samples_processed += 1
        self.loss_history.append((self.total_samples_processed, loss))
        self.update_loss_plot()

        # Re-enable buttons
        self.step_button.disabled = False
        self.batch_button.disabled = False
        self.epoch_button.disabled = False
        self.epoch_10x_button.disabled = False
        self.epoch_100x_button.disabled = False
        self.run_to_50k_button.disabled = False
        self.reinit_button.disabled = False

    def on_batch_click(self, b):
        """Handle batch button click"""
        if self.total_samples_processed >= MAX_SAMPLES_PROCESSED:
            return

        # Disable buttons during computation
        self.step_button.disabled = True
        self.batch_button.disabled = True
        self.epoch_button.disabled = True
        self.epoch_10x_button.disabled = True
        self.epoch_100x_button.disabled = True
        self.run_to_50k_button.disabled = True
        self.reinit_button.disabled = True

        batch_losses = []
        batch_sample_indices = []  # Track which samples were processed in this batch

        for _ in range(self.batch_size):
            if self.current_sample >= self.train_size:
                self.current_sample = 0

            # Track this sample index for batch highlighting
            batch_sample_indices.append(self.current_sample)

            # Get current sample
            inp = torch.tensor(self.train_inputs[self.current_sample:self.current_sample+1], dtype=torch.float32)
            tgt = torch.tensor(self.train_targets[self.current_sample:self.current_sample+1], dtype=torch.float32)

            # Train on this sample
            outputs, loss = self.train_step(inp, tgt)
            batch_losses.append(loss)

            # Store prediction
            self.train_predictions.append((
                self.train_inputs[self.current_sample],
                outputs.detach().numpy()[0]
            ))

            self.current_sample += 1

        # Update visualization with last sample
        last_sample_idx = self.current_sample - 1
        self.update_network_visualization(self.train_inputs[last_sample_idx])
        self.add_training_points()
        self.update_validation_plots()

        # Select all samples that were processed in this batch
        batch_sample_strings = []
        for sample_idx in batch_sample_indices:
            sample_str = f"Sample {sample_idx:3d}: ({self.train_inputs[sample_idx][0]:6.3f}) → [{self.train_targets[sample_idx][0]:6.3f}, {self.train_targets[sample_idx][1]:6.3f}]"
            batch_sample_strings.append(sample_str)
        self.sample_list.value = batch_sample_strings  # type: ignore

        # Average batch loss
        avg_loss = np.mean(batch_losses)
        self.total_samples_processed += self.batch_size
        self.loss_history.append((self.total_samples_processed, avg_loss))
        self.update_loss_plot()

        # Re-enable buttons
        self.step_button.disabled = False
        self.batch_button.disabled = False
        self.epoch_button.disabled = False
        self.epoch_10x_button.disabled = False
        self.epoch_100x_button.disabled = False
        self.run_to_50k_button.disabled = False
        self.reinit_button.disabled = False

    def on_epoch_click(self, b):
        """Handle epoch button click - process batches until epoch complete"""
        if self.total_samples_processed >= MAX_SAMPLES_PROCESSED:
            return

        # Disable buttons during computation
        self.step_button.disabled = True
        self.batch_button.disabled = True
        self.epoch_button.disabled = True
        self.epoch_10x_button.disabled = True
        self.epoch_100x_button.disabled = True
        self.run_to_50k_button.disabled = True
        self.reinit_button.disabled = True

        self.train_predictions = []  # Clear predictions at start of epoch

        # Shuffle training data
        indices = np.random.permutation(self.train_size)

        # Process in batches
        for batch_start in range(0, self.train_size, self.batch_size):
            batch_end = min(batch_start + self.batch_size, self.train_size)
            batch_indices = indices[batch_start:batch_end]
            batch_losses = []

            for i in batch_indices:
                inp = torch.tensor(self.train_inputs[i:i+1], dtype=torch.float32)
                tgt = torch.tensor(self.train_targets[i:i+1], dtype=torch.float32)

                outputs, loss = self.train_step(inp, tgt)
                batch_losses.append(loss)

                # Store prediction
                self.train_predictions.append((
                    self.train_inputs[i],
                    outputs.detach().numpy()[0]
                ))

            # Record batch loss
            avg_batch_loss = np.mean(batch_losses)
            self.total_samples_processed += len(batch_indices)
            self.loss_history.append((self.total_samples_processed, avg_batch_loss))

        self.current_epoch += 1
        self.current_sample = 0

        # Update visualization once at end
        self.update_network_visualization(self.train_inputs[0])
        self.add_training_points()
        self.update_validation_plots()
        self.update_loss_plot()

        # Clear selection for epoch (as requested)
        self.sample_list.value = []

        # Re-enable buttons
        self.step_button.disabled = False
        self.batch_button.disabled = False
        self.epoch_button.disabled = False
        self.epoch_10x_button.disabled = False
        self.epoch_100x_button.disabled = False
        self.run_to_50k_button.disabled = False
        self.reinit_button.disabled = False

    def on_epoch_10x_click(self, b):
        """Handle 10x epoch button click"""
        if self.total_samples_processed >= MAX_SAMPLES_PROCESSED:
            return

        # Disable buttons during computation
        self.step_button.disabled = True
        self.batch_button.disabled = True
        self.epoch_button.disabled = True
        self.epoch_10x_button.disabled = True
        self.epoch_100x_button.disabled = True
        self.run_to_50k_button.disabled = True
        self.reinit_button.disabled = True

        # Run 10 epochs
        for _ in range(10):
            if self.total_samples_processed >= MAX_SAMPLES_PROCESSED:
                break

            # Process one epoch with shuffling (same logic as on_epoch_click)
            indices = np.random.permutation(self.train_size)

            # Process in batches
            for batch_start in range(0, self.train_size, self.batch_size):
                if self.total_samples_processed >= MAX_SAMPLES_PROCESSED:
                    break

                batch_end = min(batch_start + self.batch_size, self.train_size)
                batch_indices = indices[batch_start:batch_end]
                batch_losses = []

                for i in batch_indices:
                    inp = torch.tensor(self.train_inputs[i:i+1], dtype=torch.float32)
                    tgt = torch.tensor(self.train_targets[i:i+1], dtype=torch.float32)

                    _, loss = self.train_step(inp, tgt)
                    batch_losses.append(loss)

                # Record batch loss
                avg_batch_loss = np.mean(batch_losses)
                self.total_samples_processed += len(batch_indices)
                self.loss_history.append((self.total_samples_processed, avg_batch_loss))

            self.current_epoch += 1
            self.current_sample = 0

        # Update visualization once at end
        self.update_network_visualization(self.train_inputs[0])
        self.add_training_points()
        self.update_validation_plots()
        self.update_loss_plot()

        # Clear selection for 10x epochs
        self.sample_list.value = []

        # Re-enable buttons
        self.step_button.disabled = False
        self.batch_button.disabled = False
        self.epoch_button.disabled = False
        self.epoch_10x_button.disabled = False
        self.epoch_100x_button.disabled = False
        self.run_to_50k_button.disabled = False
        self.reinit_button.disabled = False

    def on_epoch_100x_click(self, b):
        """Handle 100x epoch button click"""
        if self.total_samples_processed >= MAX_SAMPLES_PROCESSED:
            return

        # Disable buttons during computation
        self.step_button.disabled = True
        self.batch_button.disabled = True
        self.epoch_button.disabled = True
        self.epoch_10x_button.disabled = True
        self.epoch_100x_button.disabled = True
        self.run_to_50k_button.disabled = True
        self.reinit_button.disabled = True

        # Run 100 epochs
        for _ in range(100):
            if self.total_samples_processed >= MAX_SAMPLES_PROCESSED:
                break

            # Process one epoch with shuffling (same logic as on_epoch_click)
            indices = np.random.permutation(self.train_size)

            # Process in batches
            for batch_start in range(0, self.train_size, self.batch_size):
                if self.total_samples_processed >= MAX_SAMPLES_PROCESSED:
                    break

                batch_end = min(batch_start + self.batch_size, self.train_size)
                batch_indices = indices[batch_start:batch_end]
                batch_losses = []

                for i in batch_indices:
                    inp = torch.tensor(self.train_inputs[i:i+1], dtype=torch.float32)
                    tgt = torch.tensor(self.train_targets[i:i+1], dtype=torch.float32)

                    _, loss = self.train_step(inp, tgt)
                    batch_losses.append(loss)

                # Record batch loss
                avg_batch_loss = np.mean(batch_losses)
                self.total_samples_processed += len(batch_indices)
                self.loss_history.append((self.total_samples_processed, avg_batch_loss))

            self.current_epoch += 1
            self.current_sample = 0

        # Update visualization once at end
        self.update_network_visualization(self.train_inputs[0])
        self.add_training_points()
        self.update_validation_plots()
        self.update_loss_plot()

        # Clear selection for 100x epochs
        self.sample_list.value = []

        # Re-enable buttons
        self.step_button.disabled = False
        self.batch_button.disabled = False
        self.epoch_button.disabled = False
        self.epoch_10x_button.disabled = False
        self.epoch_100x_button.disabled = False
        self.run_to_50k_button.disabled = False
        self.reinit_button.disabled = False

    def on_run_to_50k_click(self, b):
        """Handle run to 50k button click - train until 50k samples processed"""
        # Disable buttons during computation
        self.step_button.disabled = True
        self.batch_button.disabled = True
        self.epoch_button.disabled = True
        self.epoch_10x_button.disabled = True
        self.epoch_100x_button.disabled = True
        self.run_to_50k_button.disabled = True
        self.reinit_button.disabled = True

        # Run until 50k samples processed
        target_samples = 50000
        while self.total_samples_processed < target_samples:
            # Process one epoch with shuffling (same logic as on_epoch_click)
            indices = np.random.permutation(self.train_size)

            # Process in batches
            for batch_start in range(0, self.train_size, self.batch_size):
                if self.total_samples_processed >= target_samples:
                    break

                batch_end = min(batch_start + self.batch_size, self.train_size)
                batch_indices = indices[batch_start:batch_end]
                batch_losses = []

                for i in batch_indices:
                    inp = torch.tensor(self.train_inputs[i:i+1], dtype=torch.float32)
                    tgt = torch.tensor(self.train_targets[i:i+1], dtype=torch.float32)

                    _, loss = self.train_step(inp, tgt)
                    batch_losses.append(loss)

                # Record batch loss
                avg_batch_loss = np.mean(batch_losses)
                self.total_samples_processed += len(batch_indices)
                self.loss_history.append((self.total_samples_processed, avg_batch_loss))

            self.current_epoch += 1
            self.current_sample = 0

        # Update visualization once at end
        self.update_network_visualization(self.train_inputs[0])
        self.add_training_points()
        self.update_validation_plots()
        self.update_loss_plot()

        # Clear selection
        self.sample_list.value = []

        # Re-enable buttons
        self.step_button.disabled = False
        self.batch_button.disabled = False
        self.epoch_button.disabled = False
        self.epoch_10x_button.disabled = False
        self.epoch_100x_button.disabled = False
        self.run_to_50k_button.disabled = False
        self.reinit_button.disabled = False

    def on_reinit_click(self, b):
        """Handle re-initialization button click - reset model with new random weights"""
        # Disable buttons during re-initialization
        self.step_button.disabled = True
        self.batch_button.disabled = True
        self.epoch_button.disabled = True
        self.epoch_10x_button.disabled = True
        self.epoch_100x_button.disabled = True
        self.run_to_50k_button.disabled = True
        self.reinit_button.disabled = True

        # Re-create the model to get fresh random initialization
        self.model = self._create_model(self.layer_sizes, self.activation_function)
        self.viz_core = NetworkVisualizationCore(self.model, self.viz_core.palette)

        # Recreate optimizer for new model
        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=self.learning_rate,
            betas=(0.9, 0.999),
            eps=1e-8,
            weight_decay=0
        )

        # Reset training state
        self.current_epoch = 0
        self.current_batch = 0
        self.current_sample = 0
        self.total_samples_processed = 0
        self.loss_history = []
        self.train_predictions = []

        # Generate new training data (with different random samples)
        self.train_inputs, self.train_targets = self.train_dataset.generate_samples()

        # Update sample list with new data
        sample_strings = []
        for i, (inp, tgt) in enumerate(zip(self.train_inputs, self.train_targets)):
            sample_str = f"Sample {i:3d}: ({inp[0]:6.3f}) → [{tgt[0]:6.3f}, {tgt[1]:6.3f}]"
            sample_strings.append(sample_str)

        self.sample_list.options = sample_strings
        self.sample_list.value = []

        # Reset visualizations
        self.update_network_visualization(self.train_inputs[0] if len(self.train_inputs) > 0 else np.array([0.0]))
        self.add_training_points()
        self.update_validation_plots()
        self.record_initial_loss()

        # Re-enable buttons
        self.step_button.disabled = False
        self.batch_button.disabled = False
        self.epoch_button.disabled = False
        self.epoch_10x_button.disabled = False
        self.epoch_100x_button.disabled = False
        self.run_to_50k_button.disabled = False
        self.reinit_button.disabled = False

    def create_2d_plots(self):
        """Create plots showing current network behavior across full input range"""
        # Create subplots for the two functions
        fig = make_subplots(
            rows=1, cols=2,
            subplot_titles=('Function 1: -x/2', f'Function 2: sin(x*{self.sine_frequency})'),
            horizontal_spacing=0.12
        )

        # Add plots for each output function
        for col in range(1, 3):
            # Add true function line (green)
            x_vals = np.linspace(-1, 1, 100)
            if col == 1:  # -x/2
                y_vals = -x_vals / 2
            else:  # sin(x*freq)
                y_vals = np.sin(x_vals * self.sine_frequency)

            fig.add_trace(
                go.Scatter(x=x_vals, y=y_vals, mode='lines',
                          line=dict(color='green', width=3),
                          name='True Function', showlegend=(col==1)),
                row=1, col=col
            )

            # Add training predictions scatter (purple)
            fig.add_trace(
                go.Scatter(x=[None], y=[None], mode='markers',
                          marker=dict(size=6, color='purple'),
                          name='Training', showlegend=(col==1)),
                row=1, col=col
            )

            # Add validation predictions scatter (orange)
            fig.add_trace(
                go.Scatter(x=[], y=[], mode='markers',
                          marker=dict(size=4, color='orange'),
                          name='Validation', showlegend=(col==1)),
                row=1, col=col
            )

        # Set axis labels and ranges
        for col in range(1, 3):
            fig.update_xaxes(title_text="Input x", range=[-1, 1], row=1, col=col)
            if col == 1:  # -x/2 (left plot gets Y-axis label)
                fig.update_yaxes(title_text="Output", range=[-0.6, 0.6], row=1, col=col)
            else:  # sin(x*freq) (right plot gets no Y-axis label)
                fig.update_yaxes(title_text="", range=[-1.2, 1.2], row=1, col=col)

        # Add selected sample markers (initially empty)
        for col in range(1, 3):
            fig.add_trace(
                go.Scatter(x=[None], y=[None], mode='markers',
                          marker=dict(size=8, color='red'),
                          name='Selected', showlegend=(col==1)),
                row=1, col=col
            )

        # Update layout
        fig.update_layout(
            height=350,
            width=940,
            showlegend=True,
            margin=dict(l=50, r=50, t=10, b=50),
            legend=dict(x=0.5, y=-0.15, xanchor='center', orientation='h')
        )

        # Store figure widget
        self._plot_widgets['main'] = go.FigureWidget(fig)
        return self._plot_widgets['main']

    def update_network_visualization(self, current_input, target_values=None):
        """Update the network visualization with current input"""
        if self.network_output:
            with self.network_output:
                clear_output(wait=True)
                # Fit within container width to avoid horizontal scrollbar
                fig = self.viz_core.create_network_visualization(current_input.tolist(), width=440, height=600, target_values=target_values)
                if fig:
                    display(fig)

    def add_training_points(self):
        """Update training points in the 2D plots with current model predictions"""
        if 'main' not in self._plot_widgets:
            return

        fig = self._plot_widgets['main']

        # Get current model predictions on training set (same as validation approach)
        with torch.no_grad():
            train_inputs_tensor = torch.tensor(self.train_inputs, dtype=torch.float32)
            train_predictions = self.model(train_inputs_tensor).numpy()

        # Batch update for smoother rendering
        with fig.batch_update():
            # Update training scatter plots (traces 1 and 4 for the two outputs)
            for output_idx in range(2):
                # Training scatter traces: left=1, right=4 (formula: output_idx * 3 + 1)
                scatter_trace_idx = output_idx * 3 + 1

                # Use current model predictions on training data
                fig.data[scatter_trace_idx].x = self.train_inputs[:, 0]
                fig.data[scatter_trace_idx].y = train_predictions[:, output_idx]

    def update_training_plots(self):
        """Update all training plots with current predictions"""
        if 'main' not in self._plot_widgets:
            return

        fig = self._plot_widgets['main']

        # Clear and update training scatter plots
        for output_idx in range(2):
            scatter_trace_idx = output_idx * 3 + 1  # Training scatter traces at indices 1, 4

            # Get all predictions for this output
            x_vals = [pred[0][0] for pred in self.train_predictions]
            y_vals = [pred[1][output_idx] for pred in self.train_predictions]

            fig.data[scatter_trace_idx].x = x_vals
            fig.data[scatter_trace_idx].y = y_vals

    def update_validation_plots(self):
        """Update validation plots with current model predictions"""
        if 'main' not in self._plot_widgets:
            return

        fig = self._plot_widgets['main']

        # Get model predictions on validation set
        with torch.no_grad():
            val_inputs_tensor = torch.tensor(self.val_inputs, dtype=torch.float32)
            val_predictions = self.model(val_inputs_tensor).numpy()

        # Batch update for smoother rendering
        with fig.batch_update():
            # Update validation scatter plots
            for output_idx in range(2):
                # Validation scatter traces: left=2, right=5 (formula: output_idx * 3 + 2)
                scatter_trace_idx = output_idx * 3 + 2

                # Update predictions scatter
                fig.data[scatter_trace_idx].x = self.val_inputs[:, 0]
                fig.data[scatter_trace_idx].y = val_predictions[:, output_idx]

    def update_selected_sample_markers(self, selected_strings):
        """Update selected sample markers in behavior plots"""
        if 'main' not in self._plot_widgets:
            return

        fig = self._plot_widgets['main']

        # Extract selected sample indices and get their inputs/targets
        selected_inputs = []
        selected_outputs = []

        if selected_strings:  # Only if there are selections
            for selected_str in selected_strings:
                sample_idx = int(selected_str.split(':')[0].split()[1])
                inp = self.train_inputs[sample_idx]
                tgt = self.train_targets[sample_idx]
                selected_inputs.append(inp[0])  # x value
                selected_outputs.append(tgt)    # [y1, y2] values

        # Update selected sample markers (traces 6 and 7 - added after the main loops)
        with fig.batch_update():
            for output_idx in range(2):
                marker_trace_idx = 6 + output_idx  # Selected markers: left=6, right=7
                if selected_outputs:
                    y_vals = [out[output_idx] for out in selected_outputs]
                    fig.data[marker_trace_idx].x = selected_inputs
                    fig.data[marker_trace_idx].y = y_vals
                else:
                    # Keep legend visible with invisible point when no selection
                    fig.data[marker_trace_idx].x = [None]
                    fig.data[marker_trace_idx].y = [None]

    def create_combined_loss_plot(self):
        """Create combined loss plot with linear and log subplots"""
        from plotly.subplots import make_subplots

        fig = make_subplots(
            rows=2, cols=1,
            vertical_spacing=0.05,  # Minimal space between subplots
            shared_xaxes=True,
            subplot_titles=('', '')  # No titles to save space
        )

        # Add linear scale trace (top)
        fig.add_trace(
            go.Scatter(x=[], y=[], mode='lines', name='Linear'),
            row=1, col=1
        )

        # Add log scale trace (bottom)
        fig.add_trace(
            go.Scatter(x=[], y=[], mode='lines', name='Log'),
            row=2, col=1
        )

        # Update axes
        fig.update_xaxes(title_text='', row=1, col=1)  # No x-axis title for top
        fig.update_xaxes(title_text='Samples Processed', row=2, col=1)  # Only bottom gets x-axis title
        fig.update_yaxes(title_text='MSE Loss', range=[0.0, 1.0], row=1, col=1)  # Linear scale
        fig.update_yaxes(title_text='MSE Loss', type='log', range=[-5, 0], row=2, col=1)  # Log scale starts at 10µ

        # Update layout
        fig.update_layout(
            height=380,  # Total height for both subplots
            width=440,
            margin=dict(l=40, r=20, t=10, b=30),
            showlegend=False,
            font=dict(size=10)  # Smaller font for all text
        )

        # Update axis title and tick fonts
        fig.update_xaxes(title_font_size=10, tickfont_size=9)
        fig.update_yaxes(title_font_size=10, tickfont_size=9)

        self._plot_widgets['loss_combined'] = go.FigureWidget(fig)
        return self._plot_widgets['loss_combined']

    def record_initial_loss(self):
        """Record initial loss before any training"""
        # Get all training samples and compute average initial loss
        inp = torch.tensor(self.train_inputs, dtype=torch.float32)
        tgt = torch.tensor(self.train_targets, dtype=torch.float32)

        with torch.no_grad():
            outputs = self.model(inp)
            # Use the same weighted loss calculation as in training
            loss_output1 = self.criterion(outputs[:, 0], tgt[:, 0])
            loss_output2 = self.criterion(outputs[:, 1], tgt[:, 1])
            loss = (loss_output1 + 2.0 * loss_output2).item()

        self.loss_history.append((0, loss))  # 0 samples processed initially
        self.update_loss_plot()

    def update_loss_plot(self):
        """Update combined loss plot with current history"""
        if self.loss_history and 'loss_combined' in self._plot_widgets:
            # Extract samples processed and loss values from tuples
            samples_processed = [item[0] for item in self.loss_history]
            loss_values = [item[1] for item in self.loss_history]

            fig = self._plot_widgets['loss_combined']
            # Update linear scale (trace 0)
            fig.data[0].x = samples_processed
            fig.data[0].y = loss_values
            # Update log scale (trace 1)
            fig.data[1].x = samples_processed
            fig.data[1].y = loss_values

    def create_widget(self):
        """Create the complete training visualization widget"""
        # Check if model is supported
        if not self.viz_core.is_supported():
            display(HTML("❌ <b>Unsupported Model Structure</b>"))
            return

        # Create components
        self.create_sample_list()
        control_buttons = self.create_control_buttons()
        activation_dropdown = self.create_activation_dropdown()

        # Create output areas - 50/50 column split
        self.network_output = Output(layout=Layout(width='450px', height='620px'))
        self.plots_output = Output(layout=Layout(width='100%'))
        self.loss_output = Output(layout=Layout(width='450px', height='380px'))

        # Model info
        model_info = f"""
        <b>Training Data:</b> {self.train_size} samples, batch={self.batch_size}, lr={self.learning_rate}
        """

        # Layout - 50/50 column split
        network_column = VBox([
            HTML("<b>Network Architecture:</b>"),
            self.network_output
        ], layout=Layout(width='470px'))

        # Create horizontal box for activation dropdown, clear button, and re-initialize button
        # Target: align right edge with listbox (440px)
        # Layout: 200px(dropdown) + 120px(clear) + 120px(reinit) = 440px total
        dropdown_and_reinit = HBox([
            activation_dropdown,  # 200px
            self.unselect_button,  # 120px
            self.reinit_button     # 120px
        ], layout=Layout(width='440px'))

        # Training column - 50/50 split with proper spacing
        # Heights: 30 (info) + 35 (dropdown+clear+reinit) + 200 (list) + 15 (spacer) + 25 (label) + 380 (loss) = 685px total
        training_column = VBox([
            HTML(model_info),
            dropdown_and_reinit,
            self.sample_list,
            HTML("<div style='height: 15px;'></div>"),  # Spacer
            HTML("<b>Training Loss</b>"),
            self.loss_output
        ], layout=Layout(width='470px'))

        # Top section with network on left, training on right
        top_section = HBox([
            network_column,
            training_column
        ], layout=Layout(justify_content='flex-start'))

        # Display everything
        display(HTML("<h3>🎯 Training Network Visualizer</h3>"))
        display(top_section)
        display(control_buttons)
        display(HTML("<h4>Current Network Behavior</h4>"))
        display(self.plots_output)

        # Initial visualization
        self.update_network_visualization(self.train_inputs[0])

        # Create plots
        with self.plots_output:
            display(self.create_2d_plots())

        with self.loss_output:
            display(self.create_combined_loss_plot())

        # Initial plot updates and first loss measurement
        self.update_validation_plots()
        self.add_training_points()
        self.record_initial_loss()


# For backward compatibility, keep the original class name
NeuralNetworkVisualizer = InteractiveNetworkVisualizer
