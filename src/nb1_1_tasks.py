#!/usr/bin/env python3
"""
NB1.1-specific task verification and progress tracking.
Contains all logic specific to the ANN Basics notebook.
"""

import numpy as np
from .fruit_progress import unlock_fruit, get_notebook_progress_simple
from .hint_system import (
    show_help_options,
    get_ai_prompt,
    get_solution,
    set_error_context,
    clear_error_context,
    get_error_context,
    CONTEXT_HINTS,
    AI_PROMPTS,
    SOLUTIONS
)

# Error context is now managed by hint_system.py

# Use hints from hint_system.py instead of duplicating them

# Task mappings for progress tracking
TASK_PROGRESS_MAP = {
    'numpy_neuron': ('coding', 'task1_numpy_neuron'),
    'neuron_demo': ('labs', 'task1b_neuron_demo'),
    'interactive_widget': ('labs', 'task2_interactive_lab'),
    'numpy_layer': ('coding', 'task3_numpy_layer'),
    'layer_demo': ('labs', 'task3b_layer_demo')
}

def update_progress(task_name):
    """Update progress for NB1.1 tasks - display based on current state"""
    if task_name not in TASK_PROGRESS_MAP:
        print(f"⚠️  Unknown task: {task_name}")
        return

    fruit_type, activity_name = TASK_PROGRESS_MAP[task_name]

    # Update the state in fruit_progress
    from fruit_progress import _fruit_tracker
    from pathlib import Path

    # CRITICAL: Ensure JSON file exists before any operations
    if not Path("course_progress.json").exists():
        # Force creation of JSON file with initial state
        _fruit_tracker._save_progress()

    # Now update the fruit
    _fruit_tracker.unlock_fruit('NB1.1', fruit_type, activity_name)

    # Now display based on CURRENT STATE from JSON file
    _show_state_based_feedback('NB1.1', task_name)

def _show_state_based_feedback(notebook, current_task):
    """Show feedback based on current state FROM JSON FILE, not in-memory state"""
    from fruit_progress import _fruit_tracker
    from IPython.display import display, HTML
    import json
    from pathlib import Path

    # CRITICAL: Read state from JSON file, NOT from in-memory state
    progress_file = Path("course_progress.json")
    if not progress_file.exists():
        # No JSON file = no progress = no banners
        return

    try:
        with open(progress_file, 'r') as f:
            json_data = json.load(f)
    except (json.JSONDecodeError, IOError):
        # Can't read JSON = no banners
        return

    # Get state FROM THE JSON FILE
    nb_data = json_data.get('notebooks', {}).get(notebook, {})
    requirements = _fruit_tracker.notebook_requirements[notebook]

    # Count completed items FROM JSON
    coding_done = len(nb_data.get('coding', []))
    quiz_done = len(nb_data.get('quizzes', []))
    lab_done = len(nb_data.get('labs', []))

    # Check if complete
    is_complete = (coding_done >= requirements['coding'] and
                   quiz_done >= requirements['quizzes'] and
                   lab_done >= requirements['labs'])

    # Only show green banner if this specific task is actually completed IN THE JSON
    task_fruit_type, task_activity = TASK_PROGRESS_MAP[current_task]
    task_completed = task_activity in nb_data.get(task_fruit_type, [])

    if task_completed:
        _show_task_banner(current_task)

    # Only show milestone and dashboard for the FINAL task (layer_demo) if notebook is complete
    if is_complete and current_task == 'layer_demo':
        _show_nb1_1_milestone()
        _fruit_tracker.show_progress_dashboard()

def _show_task_banner(task_name):
    """Show green completion banner for a task"""
    from IPython.display import display, HTML

    # Map task names to display messages
    task_messages = {
        'numpy_neuron': '✅ NumpyNeuron implementation is correct!',
        'neuron_demo': '🍊 Neuron Demo completed!',
        'interactive_widget': '🍊 Interactive Lab completed! Try different activation functions above.',
        'numpy_layer': '✅ NumpyLayer implementation is correct!\n   Successfully vectorized computation for multiple neurons',
        'layer_demo': '🍊 Layer Demo completed!'
    }

    message = task_messages.get(task_name, f'✅ {task_name} completed!')

    display(HTML(f"""
    <div style="background: linear-gradient(135deg, #4CAF50, #45a049); border-radius: 10px; padding: 20px; margin: 10px 0; text-align: center; color: white; box-shadow: 0 2px 4px rgba(0,0,0,0.2);">
        <h2 style="margin: 0; font-size: 24px;">{message}</h2>
    </div>
    """))





def _show_nb1_1_milestone():
    """Show the milestone text for NB1.1 completion"""
    from IPython.display import display, HTML

    display(HTML("""
    <div style="background: #f8f9fa; border-radius: 10px; padding: 20px; margin: 20px 0; border: 2px solid #dee2e6;">
        <h2 style="color: #495057; margin: 0 0 15px 0;">⭐️ Milestone: ANN Basics Completed!</h2>

        <h3 style="color: #343a40; margin: 20px 0 10px 0;">🎓 What You've Accomplished</h3>
        <p style="color: #495057; line-height: 1.6;">
            <strong>You've mastered the fundamentals!</strong> You built neural networks from scratch using a standard math library, NumPy:
        </p>
        <ul style="color: #495057; line-height: 1.8;">
            <li><strong>Single Neuron</strong>: Weights, bias, activation functions</li>
            <li><strong>Neural Layer</strong>: Matrix operations, vectorization, multiple neurons</li>
            <li><strong>Mathematical Foundation</strong>: You understand what happens under the hood</li>
        </ul>

        <h3 style="color: #343a40; margin: 20px 0 10px 0;">🚀 Why PyTorch is the Next Step</h3>
        <p style="color: #495057; line-height: 1.6;">
            <strong>NumPy is nice for learning basics, but has severe limitations for real ML.</strong>
        </p>

        <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
            <tr style="background: #e9ecef;">
                <th style="padding: 10px; text-align: left; border: 1px solid #dee2e6;">Challenge</th>
                <th style="padding: 10px; text-align: left; border: 1px solid #dee2e6;">NumPy Reality</th>
                <th style="padding: 10px; text-align: left; border: 1px solid #dee2e6;">PyTorch Solution</th>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #dee2e6;">🔥 GPU Acceleration</td>
                <td style="padding: 10px; border: 1px solid #dee2e6;">CPU-only, slow for large networks</td>
                <td style="padding: 10px; border: 1px solid #dee2e6;">Seamless GPU support with .cuda()</td>
            </tr>
            <tr style="background: #f8f9fa;">
                <td style="padding: 10px; border: 1px solid #dee2e6;">📈 Gradient Computation</td>
                <td style="padding: 10px; border: 1px solid #dee2e6;">Manual backprop math (ouch!)</td>
                <td style="padding: 10px; border: 1px solid #dee2e6;">Automatic differentiation with .backward()</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #dee2e6;">🏗️ Network Architecture</td>
                <td style="padding: 10px; border: 1px solid #dee2e6;">Build everything from scratch</td>
                <td style="padding: 10px; border: 1px solid #dee2e6;">Pre-built layers: nn.Linear, nn.Conv2d</td>
            </tr>
            <tr style="background: #f8f9fa;">
                <td style="padding: 10px; border: 1px solid #dee2e6;">📊 Data Loading</td>
                <td style="padding: 10px; border: 1px solid #dee2e6;">Manual batch processing</td>
                <td style="padding: 10px; border: 1px solid #dee2e6;">Efficient DataLoader with multiprocessing</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #dee2e6;">🎛️ Training Loop</td>
                <td style="padding: 10px; border: 1px solid #dee2e6;">Write optimizer by hand</td>
                <td style="padding: 10px; border: 1px solid #dee2e6;">Ready-to-use optimizers: Adam, SGD</td>
            </tr>
        </table>

        <div style="background: #e7f3ff; border-left: 4px solid #2196F3; padding: 15px; margin: 20px 0;">
            <h4 style="color: #1976D2; margin: 0 0 10px 0;">The PyTorch Advantage</h4>
            <p style="color: #495057; margin: 0; line-height: 1.6;">
                <strong>Same concepts, but supercharged!</strong><br>
                • Your NumPy knowledge transfers directly<br>
                • Focus on model design, not implementation details<br>
                • Scale from prototype to production seamlessly
            </p>
        </div>

        <p style="color: #495057; font-size: 1.1em; margin: 20px 0 0 0;">
            <strong>Ready to level up?</strong> Let's transition to PyTorch and build something useful!
        </p>
    </div>
    """))

def _show_completion_banner(task_name):
    """Show green completion banner for a task"""
    from IPython.display import display, HTML

    if task_name not in TASK_PROGRESS_MAP:
        return

    fruit_type, activity_name = TASK_PROGRESS_MAP[task_name]
    fruit_emoji = {'coding': '🍒', 'quizzes': '🍓', 'labs': '🍊'}
    fruit_name = {'coding': 'Coding Challenge', 'quizzes': 'Quiz', 'labs': 'Lab'}

    display(HTML(f"""
    <div style="background: linear-gradient(135deg, #4CAF50, #45a049); border-radius: 10px; padding: 20px; margin: 10px 0; text-align: center; color: white; box-shadow: 0 2px 4px rgba(0,0,0,0.2);">
        <h2 style="margin: 0; font-size: 24px;">{fruit_emoji[fruit_type]} {fruit_name[fruit_type]} Complete</h2>
        <p style="margin: 8px 0; font-size: 16px;">{activity_name}</p>
    </div>
    """))

def _is_milestone_completed(task_name):
    """Check if prerequisite milestones are completed for NB1.1"""
    # For NB1.1, we don't have complex dependencies
    # This is a placeholder for consistency with the framework
    return True

def _check_numpy_neuron(NumpyNeuron):
    """Verify NumpyNeuron implementation"""
    # Error context handled by hint_system.py
    clear_error_context()

    try:
        # Test basic instantiation
        neuron = NumpyNeuron(num_inputs=3)

        # Check attributes exist
        if not hasattr(neuron, 'weights'):
            set_error_context('missing_weights')
            return False, "Missing 'weights' attribute"
        if not hasattr(neuron, 'bias'):
            set_error_context('missing_bias')
            return False, "Missing 'bias' attribute"
        if not hasattr(neuron, 'forward'):
            set_error_context('missing_forward')
            return False, "Missing 'forward' method"
        if not hasattr(neuron, 'activation'):
            set_error_context('missing_activation')
            return False, "Missing 'activation' method"

        # Check shapes first
        if neuron.weights.shape != (3,):
            set_error_context('wrong_weights_shape')
            return False, f"Wrong weights shape: expected (3,), got {neuron.weights.shape}"
        if not np.isscalar(neuron.bias):
            set_error_context('wrong_bias_type')
            return False, f"Bias should be scalar, got shape {getattr(neuron.bias, 'shape', 'unknown')}"

        # Check if activation method is implemented BEFORE testing forward pass
        try:
            activation_test = neuron.activation(0.0)  # Test with simple input
            if activation_test is None:
                set_error_context('missing_activation')
                return False, "Activation method returns None - needs implementation"
        except Exception:
            set_error_context('missing_activation')
            return False, "Activation method has error - needs implementation"

        # Test forward pass
        test_input = np.array([1.0, 2.0, -1.0])
        output = neuron.forward(test_input)

        if not np.isscalar(output):
            set_error_context('wrong_output_type')
            return False, f"Output should be scalar, got shape {getattr(output, 'shape', 'unknown')}"
        if not isinstance(output, (int, float, np.number)) or not (0 <= float(output) <= 1):
            set_error_context('math_error')
            return False, f"Sigmoid-type activation output should be in [0,1], got {output}"

        # Test 2: Precise mathematical checks with known values - COPIED FROM OLD SYSTEM
        # Create a test neuron with known weights
        test_neuron = NumpyNeuron(num_inputs=3)
        test_neuron.weights = np.array([0.5, -0.3, 0.8])
        test_neuron.bias = 0.1
        test_input = np.array([2.0, 1.0, -1.0])

        # we need to report that instead of mathematical errors
        try:
            test_activation = test_neuron.activation(0.0)
            if test_activation is None:
                set_error_context('missing_activation')
                return False, "Activation method returns None - needs implementation"
        except Exception:
            set_error_context('missing_activation')
            return False, "Activation method has error - needs implementation"

        # Expected: z = 0.5*2 + (-0.3)*1 + 0.8*(-1) + 0.1 = 1.0 - 0.3 - 0.8 + 0.1 = 0.0
        # activation(0.0) = 0.5 for sigmoid
        expected_output = 0.5
        actual_output = test_neuron.forward(test_input)

        if not np.isclose(actual_output, expected_output, rtol=1e-10):
            set_error_context('math_error')
            return False, f"Mathematical computation incorrect: expected activation(0.0) = 0.5 for sigmoid, got {actual_output}"

        # Test 3: Edge cases - very large positive input (should saturate to 1.0)
        edge_neuron = NumpyNeuron(num_inputs=1)
        edge_neuron.weights = np.array([1.0])
        edge_neuron.bias = 0.0

        # Check activation function on edge test neuron too
        try:
            edge_activation_test = edge_neuron.activation(0.0)
            if edge_activation_test is None:
                set_error_context('missing_activation')
                return False, "Activation method returns None - needs implementation"
        except Exception:
            set_error_context('missing_activation')
            return False, "Activation method has error - needs implementation"

        large_output = edge_neuron.forward(np.array([100.0]))

        if not np.isclose(large_output, 1.0, rtol=1e-10):
            set_error_context('math_error')
            return False, f"Sigmoid-type activation should saturate to 1.0 for large inputs, got {large_output}"

        # Test 4: Edge cases - very large negative input (should saturate to 0.0)
        small_output = edge_neuron.forward(np.array([-100.0]))

        if not np.isclose(small_output, 0.0, rtol=1e-10):
            set_error_context('math_error')
            return False, f"Sigmoid-type activation should saturate to 0.0 for large negative inputs, got {small_output}"

        return True, "All tests passed"

    except Exception as e:
        set_error_context('forward_error')
        return False, f"Error during testing: {str(e)}"

def _check_numpy_layer(NumpyLayer):
    """Verify NumpyLayer implementation"""
    try:
        # Test basic instantiation
        layer = NumpyLayer(num_inputs=5, num_neurons=3)

        # Check attributes exist
        if not hasattr(layer, 'weights'):
            return False, "Missing 'weights' attribute"
        if not hasattr(layer, 'biases'):
            return False, "Missing 'biases' attribute"
        if not hasattr(layer, 'forward'):
            return False, "Missing 'forward' method"

        # Check shapes
        if layer.weights.shape != (5, 3):
            return False, f"Wrong weights shape: expected (5, 3), got {layer.weights.shape}"
        if layer.biases.shape != (3,):
            return False, f"Wrong biases shape: expected (3,), got {layer.biases.shape}"

        # Test forward pass
        test_input = np.array([1.0, 2.0, -1.0, 0.5, -0.5])
        output = layer.forward(test_input)

        if output.shape != (3,):
            return False, f"Output shape should be (3,), got {output.shape}"
        if not np.all((0 <= output) & (output <= 1)):
            return False, f"All sigmoid-type activation outputs should be in [0,1], got {output}"

        # Test with different input sizes
        small_layer = NumpyLayer(num_inputs=2, num_neurons=4)
        small_input = np.array([1.0, -1.0])
        small_output = small_layer.forward(small_input)
        if small_output.shape != (4,):
            return False, f"Small layer output shape should be (4,), got {small_output.shape}"

        return True, "All tests passed"

    except Exception as e:
        return False, f"Error during testing: {str(e)}"

def verify_numpy_neuron(NumpyNeuron):
    """Verify and provide feedback for NumpyNeuron implementation"""
    success, message = _check_numpy_neuron(NumpyNeuron)

    if success:
        print("✅ NumpyNeuron implementation is correct!")
        update_progress('numpy_neuron')  # Always shows green banner now
        return True
    else:
        print(f"❌ NumpyNeuron verification failed: {message}")
        show_help_options('numpy_neuron')
        return False

def verify_numpy_layer(NumpyLayer):
    """Verify and provide feedback for NumpyLayer implementation"""
    # Check prerequisites - verify NumpyNeuron task was completed
    from fruit_progress import _fruit_tracker

    prerequisite_met = False
    if 'NB1.1' in _fruit_tracker.progress_data['notebooks']:
        nb_data = _fruit_tracker.progress_data['notebooks']['NB1.1']
        prerequisite_met = 'task1_numpy_neuron' in nb_data['coding']

    if not prerequisite_met:
        print("⚠️  Complete Task 1 (NumpyNeuron) first!")
        return False

    success, message = _check_numpy_layer(NumpyLayer)

    if success:
        print("✅ NumpyLayer implementation is correct!")
        print("   Successfully vectorized computation for multiple neurons")
        update_progress('numpy_layer')  # Always shows green banner now
        return True
    else:
        print(f"❌ NumpyLayer verification failed: {message}")
        show_help_options('numpy_layer')
        return False

def get_nb1_1_progress_simple():
    """Get simplified progress display for NB1.1"""
    from fruit_progress import get_notebook_progress_simple as _get_progress
    return _get_progress('NB1.1')

# All hints, prompts, and solutions are now imported from hint_system.py
