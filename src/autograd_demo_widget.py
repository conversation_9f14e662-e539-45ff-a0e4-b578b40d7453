#!/usr/bin/env python3
# autograd_demo_widget.py
"""
Interactive Automatic Differentiation Demonstration Widget
Reusable class for visualizing PyTorch neural network models

NOTE: This file now imports from neural_network_visualizers.py for the modular implementation.
The original NeuralNetworkVisualizer is preserved for backward compatibility.
"""

# Import the modular implementation
from .neural_network_visualizers import (
    InteractiveNetworkVisualizer as NeuralNetworkVisualizer,
    NetworkVisualizationCore,
    TrainingNetworkVisualizer,
    SimpleArithmeticDataset
)

# For backward compatibility, also expose the original imports that notebooks might use
import numpy as np
import torch
import torch.nn as nn
import plotly.graph_objects as go
from ipywidgets import VBox, HBox, HTML, Output, Button, FloatSlider, Layout, GridBox
from IPython.display import display, clear_output
from IPython.display import display as ipy_display, HTML as IPyHTML

# The original implementation has been refactored into three modular components:
# 1. NetworkVisualizationCore - Core network diagram visualization
# 2. InteractiveNetworkVisualizer - Slider-based interactive mode (original functionality)
# 3. TrainingNetworkVisualizer - New training mode with 3D plots and loss tracking
