#!/usr/bin/env python3
"""
Progress Verification System - Handles all task verification logic
Independent from progress recording and visualization
"""

import numpy as np
from .hint_system import set_error_context, clear_error_context


def verify_numpy_neuron(NumpyNeuron):
    """
    Verify NumpyNeuron implementation
    Returns: (success: bool, message: str, error_context: str or None)
    """
    clear_error_context()

    # PHASE 1: Test basic instantiation
    try:
        neuron = NumpyNeuron(num_inputs=3)
    except Exception as e:
        set_error_context('init_error')
        return False, f"Failed to create NumpyNeuron: {str(e)}", 'init_error'

    # PHASE 2: Check that all required attributes/methods exist
    if not hasattr(neuron, 'weights'):
        set_error_context('missing_weights')
        return False, "Missing 'weights' attribute", 'missing_weights'
    if not hasattr(neuron, 'bias'):
        set_error_context('missing_bias')
        return False, "Missing 'bias' attribute", 'missing_bias'
    if not hasattr(neuron, 'forward'):
        set_error_context('missing_forward')
        return False, "Missing 'forward' method", 'missing_forward'
    if not hasattr(neuron, 'activation'):
        set_error_context('missing_activation')
        return False, "Missing 'activation' method", 'missing_activation'

    # PHASE 3: Check attribute types and shapes
    try:
        if neuron.weights.shape != (3,):
            set_error_context('wrong_weights_shape')
            return False, f"Wrong weights shape: expected (3,), got {neuron.weights.shape}", 'wrong_weights_shape'
    except AttributeError:
        set_error_context('wrong_weights_shape')
        return False, "Weights should be a numpy array with shape attribute", 'wrong_weights_shape'

    if not np.isscalar(neuron.bias):
        set_error_context('wrong_bias_type')
        bias_shape = getattr(neuron.bias, 'shape', 'unknown')
        return False, f"Bias should be scalar, got shape {bias_shape}", 'wrong_bias_type'

    # PHASE 4: Check activation function returns correct type
    try:
        activation_test = neuron.activation(0.0)  # Test with simple input
        if activation_test is None:
            set_error_context('missing_activation')
            return False, "Activation function returns None - needs implementation", 'missing_activation'
        # Check if activation returns a number
        if not isinstance(activation_test, (int, float, np.number)):
            set_error_context('activation_wrong_type')
            return False, f"Activation function should return a number, but returned {type(activation_test).__name__}: {repr(activation_test)}", 'activation_wrong_type'
    except Exception as e:
        set_error_context('missing_activation')
        return False, f"Activation function raised error: {str(e)}", 'missing_activation'

    # PHASE 5: Check forward pass returns correct type
    test_input = np.array([1.0, 2.0, -1.0])
    try:
        output = neuron.forward(test_input)
    except Exception as e:
        set_error_context('forward_error')
        return False, f"Forward pass raised error: {str(e)}", 'forward_error'

    if output is None:
        set_error_context('wrong_output_type')
        return False, "Forward method returns None - did you forget to return the result?", 'wrong_output_type'
    if not np.isscalar(output):
        set_error_context('wrong_output_type')
        output_shape = getattr(output, 'shape', 'unknown')
        return False, f"Forward output should be scalar, got shape {output_shape}", 'wrong_output_type'
    if not isinstance(output, (int, float, np.number)):
        set_error_context('wrong_output_type')
        return False, f"Forward output should be a number, but got {type(output).__name__}: {repr(output)}", 'wrong_output_type'

    # PHASE 6: Check mathematical constraints (sigmoid range)
    try:
        output_float = float(output)
        if not (0 <= output_float <= 1):
            set_error_context('math_error')
            return False, f"Sigmoid-type activation output should be in [0,1], got {output}", 'math_error'
    except (TypeError, ValueError):
        set_error_context('wrong_output_type')
        return False, f"Forward output should be numeric, got {type(output).__name__}", 'wrong_output_type'

    # PHASE 6.5: Check if forward is actually computing (not just returning constant)
    # Test with multiple different inputs to see if output changes
    test_inputs = [
        np.array([0.5, -1.0, 2.0]),
        np.array([-2.0, 3.0, 0.1]),
        np.array([0.0, 0.0, 0.0])
    ]
    outputs = [output]  # Include the first output
    for test_in in test_inputs:
        outputs.append(neuron.forward(test_in))

    # If all outputs are exactly the same constant (especially 0, 1, or simple fractions)
    if len(set(outputs)) == 1 and outputs[0] in [0, 1, 0.5, 0.25, 0.75]:
        set_error_context('forward_not_computing')
        return False, f"Forward method returns constant {outputs[0]} - it should compute: z = np.dot(inputs, self.weights) + self.bias, then return self.activation(z)", 'forward_not_computing'

    # PHASE 7: Precise mathematical checks with known values
    # Create a test neuron with known weights
    test_neuron = NumpyNeuron(num_inputs=3)
    test_neuron.weights = np.array([0.5, -0.3, 0.8])
    test_neuron.bias = 0.1
    test_input = np.array([2.0, 1.0, -1.0])

    # Check activation function on test neuron too
    try:
        test_activation = test_neuron.activation(0.0)
        if test_activation is None:
            set_error_context('missing_activation')
            return False, "Activation function returns None - needs implementation", 'missing_activation'
        if not isinstance(test_activation, (int, float, np.number)):
            set_error_context('activation_wrong_type')
            return False, f"Activation function should return a number, but returned {type(test_activation).__name__}", 'activation_wrong_type'
    except Exception:
        set_error_context('missing_activation')
        return False, "Activation function has error - needs implementation", 'missing_activation'

    # Expected: z = 0.5*2 + (-0.3)*1 + 0.8*(-1) + 0.1 = 1.0 - 0.3 - 0.8 + 0.1 = 0.0
    # activation(0.0) = 0.5
    expected_output = 0.5
    actual_output = test_neuron.forward(test_input)

    # First check if the test neuron also returns a constant (indicates forward not using inputs)
    test_input2 = np.array([0.0, 0.0, 0.0])
    actual_output2 = test_neuron.forward(test_input2)

    if actual_output == actual_output2 and actual_output in [0, 1, 0.5, 0.25, 0.75]:
        set_error_context('forward_not_computing')
        return False, f"Forward method returns constant {actual_output} - it should compute: z = np.dot(inputs, self.weights) + self.bias, then return self.activation(z)", 'forward_not_computing'

    if not np.isclose(actual_output, expected_output, rtol=1e-10):
        set_error_context('math_error')
        return False, f"Mathematical computation incorrect: expected Activation(0.0) = 0.5, got {actual_output}", 'math_error'

    # PHASE 8: Edge cases - very large positive input (should saturate to 1.0)
    edge_neuron = NumpyNeuron(num_inputs=1)
    edge_neuron.weights = np.array([1.0])
    edge_neuron.bias = 0.0

    # Check activation function on edge test neuron too
    try:
        edge_activation_test = edge_neuron.activation(0.0)
        if edge_activation_test is None:
            set_error_context('missing_activation')
            return False, "Activation function returns None - needs implementation", 'missing_activation'
        if not isinstance(edge_activation_test, (int, float, np.number)):
            set_error_context('activation_wrong_type')
            return False, f"Activation function should return a number, but returned {type(edge_activation_test).__name__}", 'activation_wrong_type'
    except Exception:
        set_error_context('missing_activation')
        return False, "Activation function has error - needs implementation", 'missing_activation'

    large_output = edge_neuron.forward(np.array([100.0]))

    if not np.isclose(large_output, 1.0, rtol=1e-10):
        set_error_context('math_error')
        return False, f"Activation of type sigmoid should saturate to 1.0 for large inputs, got {large_output}", 'math_error'

    # PHASE 9: Edge cases - very large negative input (should saturate to 0.0)
    small_output = edge_neuron.forward(np.array([-100.0]))

    if not np.isclose(small_output, 0.0, rtol=1e-10):
        set_error_context('math_error')
        return False, f"Activation of type sigmoid should saturate to 0.0 for large negative inputs, got {small_output}", 'math_error'

    clear_error_context()
    return True, "NumpyNeuron implementation is correct!", None


def verify_numpy_layer(NumpyLayer):
    """
    Verify NumpyLayer implementation
    Returns: (success: bool, message: str, error_context: str or None)
    """
    clear_error_context()

    try:
        # Test basic instantiation
        layer = NumpyLayer(num_inputs=5, num_neurons=3)

        # Check attributes exist
        if not hasattr(layer, 'weights'):
            set_error_context('missing_weights')
            return False, "Missing 'weights' attribute", 'missing_weights'
        if not hasattr(layer, 'biases'):
            set_error_context('missing_biases')
            return False, "Missing 'biases' attribute", 'missing_biases'
        if not hasattr(layer, 'forward'):
            set_error_context('missing_forward')
            return False, "Missing 'forward' method", 'missing_forward'

        # Check shapes
        if layer.weights.shape != (5, 3):
            set_error_context('wrong_weights_shape')
            return False, f"Wrong weights shape: expected (5, 3), got {layer.weights.shape}", 'wrong_weights_shape'
        if layer.biases.shape != (3,):
            set_error_context('wrong_biases_shape')
            return False, f"Wrong biases shape: expected (3,), got {layer.biases.shape}", 'wrong_biases_shape'

        # Test forward pass
        test_input = np.array([1.0, 2.0, -1.0, 0.5, -0.5])
        output = layer.forward(test_input)

        if output.shape != (3,):
            set_error_context('wrong_output_shape')
            return False, f"Output shape should be (3,), got {output.shape}", 'wrong_output_shape'
        if not np.all((0 <= output) & (output <= 1)):
            set_error_context('math_error')
            return False, f"All activation outputs for sigmoid should be in [0,1], got {output}", 'math_error'

        # Test with different input sizes
        small_layer = NumpyLayer(num_inputs=2, num_neurons=4)
        small_input = np.array([1.0, -1.0])
        small_output = small_layer.forward(small_input)
        if small_output.shape != (4,):
            set_error_context('wrong_output_shape')
            return False, f"Small layer output shape should be (4,), got {small_output.shape}", 'wrong_output_shape'

        clear_error_context()
        return True, "All tests passed", None

    except Exception as e:
        set_error_context('forward_error')
        return False, f"Error during testing: {str(e)}", 'forward_error'


# Task verification registry
TASK_VERIFIERS = {
    'numpy_neuron': verify_numpy_neuron,
    'numpy_layer': verify_numpy_layer,
    # Add more verifiers as needed
}


def verify_task(task_name, implementation):
    """
    Verify a task implementation

    Args:
        task_name: Name of the task to verify
        implementation: The class or object to verify

    Returns:
        (success: bool, message: str, error_context: str or None)
    """
    if task_name not in TASK_VERIFIERS:
        return False, f"Unknown task: {task_name}", None

    verifier = TASK_VERIFIERS[task_name]
    return verifier(implementation)
