#!/usr/bin/env python3
# numpy_neuron_widget.py
"""
Interactive Neuron Visualization with Mandatory Guided Experiments
"""

import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from ipywidgets import Float<PERSON>lider, VBox, HBox, HTML, Output, Button, Dropdown, Layout, Checkbox, Accordion
from IPython.display import display, clear_output

def handle_skip_override():
    """Handle skip checkbox CHECKED - add progress"""
    try:
        from src.progress_recording import mark_lab_complete
        result = mark_lab_complete('interactive_widget', 'NB1.1')
        return True
    except Exception:
        return False

def handle_skip_remove():
    """Handle skip checkbox UNCHECKED - remove progress ONLY if experiments aren't actually completed"""
    try:
        import json
        import os
        from datetime import datetime

        progress_file = "course_progress.json"
        if not os.path.exists(progress_file):
            return False

        # Check if experiments are legitimately completed in the current widget instance
        # This requires access to the experiment_state from the widget
        # We'll use a global flag that gets set by the widget when experiments are truly completed
        if hasattr(__main__, 'widget_experiments_completed') and getattr(__main__, 'widget_experiments_completed', False):
            # Experiments are legitimately completed - don't remove progress
            return False

        with open(progress_file, 'r') as f:
            data = json.load(f)

        # Remove from labs array only if experiments aren't actually completed
        # NOTE: 'interactive_widget' maps to 'task2_interactive_lab' in TASK_PROGRESS_MAP
        if 'notebooks' in data and 'NB1.1' in data['notebooks'] and 'labs' in data['notebooks']['NB1.1']:
            labs = data['notebooks']['NB1.1']['labs']
            if 'task2_interactive_lab' in labs:
                labs.remove('task2_interactive_lab')

        # Update last activity
        data['last_activity'] = datetime.now().isoformat()

        with open(progress_file, 'w') as f:
            json.dump(data, f, indent=2)

        return True

    except Exception:
        return False

# Make functions available in global namespace for JavaScript access
import __main__
__main__.handle_skip_override = handle_skip_override  # type: ignore
__main__.handle_skip_remove = handle_skip_remove  # type: ignore

def clear_widget_interference():
    """
    Clear any JavaScript interference from widgets that might block Cursor input.
    Call this function if Cursor chat becomes unresponsive.
    """
    from IPython.display import display, HTML

    cleanup_html = HTML("""
    <script>
    console.log('🧹 Clearing widget interference...');

    // Remove any lingering event handlers
    const skipCheckboxes = document.querySelectorAll('#skip-experiments');
    skipCheckboxes.forEach(cb => cb.remove());

    // Clear global variables
    ['skip_experiments', 'widget_experiments_completed'].forEach(prop => {
        if (window[prop] !== undefined) {
            delete window[prop];
        }
    });

    // Force cleanup of any blocking elements
    document.querySelectorAll('.hidden-skip-add-trigger, .hidden-skip-remove-trigger').forEach(el => el.remove());

    console.log('✅ Widget interference cleared - Cursor should be responsive now');
    </script>
    <div style="padding: 10px; background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; color: #155724; margin: 10px 0;">
        <strong>🧹 Widget Interference Cleared</strong><br>
        Cursor chat should now be responsive. You can delete this cell after confirming it works.
    </div>
    """)

    display(cleanup_html)

# Make cleanup function available globally
__main__.clear_widget_interference = clear_widget_interference  # type: ignore

def create_interactive_neuron_demo(palette):
    """
    Create the interactive neuron visualization with mandatory guided experiments.

    Returns:
        None (displays the interactive widget)
    """

    # Initialize global flag to track legitimate completion
    __main__.widget_experiments_completed = False  # type: ignore

    palette = palette or ""
    COLOR_NEGATIVE = 'rgb(255,0,0)' if palette == "red-green" else 'rgb(0,0,255)' if palette == "blue-red" else 'rgb(128,128,255)'
    COLOR_NEUTRAL = 'rgb(128,128,128)'
    COLOR_POSITIVE = 'rgb(0,255,0)' if palette == "red-green" else 'rgb(255,0,0)' if palette == "blue-red" else 'rgb(255,128,128)'

    # Experiment tracking state
    experiment_state = {
        'bias_detective': False,
        'activation_explorer': False,
        'saturation_explorer': False,
        'initial_bias': None,
        'activations_tried_while_negative': set(),  # Only count when z < 0
        'observed_saturation': False,
        'achieved_positive_with_zero_inputs': False,
        'achieved_negative_with_zero_inputs': False
    }

    def check_experiments():
        """Check and update experiment completion status"""
        # Get current values
        inputs = [slider.value for slider in input_sliders]
        weights = [slider.value for slider in weight_sliders]
        bias = bias_slider.value
        activation = activation_dropdown.value

        # Store initial bias on first check
        if experiment_state['initial_bias'] is None:
            experiment_state['initial_bias'] = bias

        # Calculate current output for sweet spot check
        z = sum(inp * w for inp, w in zip(inputs, weights)) + bias
        output = apply_activation(z, activation)

        # 1. Bias Detective: All inputs = 0, achieve both positive and negative outputs
        if all(abs(inp) < 0.05 for inp in inputs):  # All inputs are zero
            if output > 0.1:  # Positive activation output
                experiment_state['achieved_positive_with_zero_inputs'] = True
            elif output < -0.1:  # Negative activation output (impossible with sigmoid!)
                experiment_state['achieved_negative_with_zero_inputs'] = True

        # Complete when both positive and negative achieved with zero inputs
        if (experiment_state['achieved_positive_with_zero_inputs'] and
            experiment_state['achieved_negative_with_zero_inputs']):
            experiment_state['bias_detective'] = True

        # 2. Activation Explorer: Try at least 3 different activations WHILE ∑ node is negative
        if z < -0.1:  # Only count activations when ∑ node is negative
            experiment_state['activations_tried_while_negative'].add(activation)
        if len(experiment_state['activations_tried_while_negative']) >= 3:
            experiment_state['activation_explorer'] = True


        # 4. Saturation Explorer: Achieve saturation with sigmoid/tanh, then try non-saturating
        if activation in ['sigmoid', 'tanh']:
            if output > 0.95 or output < 0.05:  # Near saturation for sigmoid/tanh
                experiment_state['observed_saturation'] = True
        if activation in ['relu', 'linear'] and experiment_state['observed_saturation']:
            experiment_state['saturation_explorer'] = True

        # Update experiment display
        update_experiment_display()

        # Check if all experiments completed
        if all(experiment_state[key] for key in ['bias_detective', 'activation_explorer']):
            # Set global flag to indicate experiments are legitimately completed
            __main__.widget_experiments_completed = True  # type: ignore
            mark_interactive_complete()

    def update_experiment_display():
        """Update the visual display of experiment completion"""
        experiments_html = f"""
        <div style='font-size: 13px; line-height: 1.4; padding: 10px; background-color: #f8f9ff; border-radius: 6px; border-left: 4px solid #4285f4; margin-bottom: 10px;'>
            <h4 style='margin: 0 0 8px 0; color: #1a73e8;'>🧪 Discovery Experiments</h4>

            <div style='margin: 10px 0; padding: 8px; background: {"#e8f5e8" if experiment_state["bias_detective"] else "#fff"}; border-radius: 4px;'>
                <strong>{"✅" if experiment_state["bias_detective"] else "⭕"} 1. 🔍 Bias Detective:</strong> Set all inputs to <code>0</code> and adjust the bias to make the output switch between positive and negative.
                <br><em style='color: #666; font-size: 12px;'>→ Demonstrate how bias controls output when there's no input contribution. 💡 Hint: Sigmoid cannot give negative output.</em>
            </div>

            <div style='margin: 10px 0; padding: 8px; background: {"#e8f5e8" if experiment_state["activation_explorer"] else "#fff"}; border-radius: 4px;'>
                <strong>{"✅" if experiment_state["activation_explorer"] else "⭕"} 2. ⚡ Activation Explorer:</strong> Keep the ∑ node negative, then try at least 3 different activation functions.
                <br><em style='color: #666; font-size: 12px;'>→ Compare how ReLU, Sigmoid, Tanh handle negative linear combinations</em>
            </div>

            <div class='progress-status' style='margin-top: 8px; padding: 6px; background: {"#d4edda; border: 1px solid #c3e6cb; color: #155724" if all(experiment_state[key] for key in ["bias_detective", "activation_explorer"]) else "#fff3cd; border: 1px solid #ffeaa7; color: #856404"}; border-radius: 3px; font-weight: bold; text-align: center; font-size: 12px;'>
                {"🎉 All Experiments Complete! Progress Unlocked!" if all(experiment_state[key] for key in ["bias_detective", "activation_explorer"]) else f"Progress: {sum(experiment_state[key] for key in ['bias_detective', 'activation_explorer'])}/2 experiments completed"}
            </div>
        </div>
        """
        experiments_display.value = experiments_html

    def mark_interactive_complete():
        """Mark the interactive widget as complete in the progress system"""
        try:
            from .progress_recording import mark_lab_complete
            mark_lab_complete('interactive_widget', 'NB1.1')
            print("🎉 Interactive Lab completed! All guided experiments finished.")
        except Exception as e:
            print(f"Note: Could not auto-update progress: {e}")

    # Global flag to track skip state
    skip_override_active = False

    # Create vertical input sliders for top-left (8% of total width = ~75px)
    # Values are displayed in the neuron architecture plot, so no readout needed
    input_sliders = [
        FloatSlider(value=1.0, min=-3, max=3, step=0.1, description='',  # No description text
                   layout=Layout(width='20px', height='200px', margin='1px 1px'),  # Much closer together, taller
                   readout=False, orientation='vertical') for i in range(3)  # Vertical sliders
    ]

    # Create sliders for weights (ultra-compact to prevent scrollbars)
    weight_sliders = [
        FloatSlider(value=np.random.randn(), min=-2, max=2, step=0.1,
                   description=f'W{i+1}:', layout=Layout(width='260px', height='24px', margin='0.5px 0px'),
                   style={'description_width': '30px'})  # Ultra-compact
        for i in range(3)
    ]

    # Create bias slider (same width as weight sliders)
    bias_slider = FloatSlider(value=np.random.randn(), min=-2, max=2, step=0.1,
                             description='Bias:', layout=Layout(width='260px', height='24px', margin='0.5px 0px'),
                             style={'description_width': '40px'})

    # Create activation function dropdown (ultra-compact to prevent scrollbars)
    activation_dropdown = Dropdown(
        options=[('Sigmoid', 'sigmoid'), ('ReLU', 'relu'), ('Tanh', 'tanh'),
                ('GELU', 'gelu'), ('Swish/SiLU', 'swish'), ('LeakyReLU', 'leaky_relu'),
                ('ELU', 'elu'), ('Linear', 'linear')],
        value='sigmoid',
        description='Activation:',
        layout=Layout(width='260px', height='24px', margin='1px 0px'),  # Ultra-compact
        style={'description_width': '70px'}
    )

    # Preset buttons removed per user request

    # Create separate output widgets for the two canvases with precise dimensions
    neuron_architecture_output = Output(layout=Layout(width='855px', height='280px'))  # Row 1 right (75px + 10px + 855px = 940px)
    activation_plot_output = Output(layout=Layout(width='553px', height='300px'))      # Row 2 right - adjusted for spacer column (340px + 47px + 553px = 940px)

    # Create widget to display activation function definition
    activation_definition = HTML("")

    # Create experiments display widget
    experiments_display = HTML("")

    def get_activation_definition(activation_type):
        """Get mathematical definition of activation functions"""
        definitions = {
            'sigmoid': 'σ(z) = 1 / (1 + e<sup>-z</sup>)<br><br>Used in: Binary classification, older networks',
            'relu': 'ReLU(z) = max(0, z)<br><br>Used in: Most hidden layers, CNNs',
            'tanh': 'tanh(z) = (e<sup>z</sup> - e<sup>-z</sup>) / (e<sup>z</sup> + e<sup>-z</sup>)<br><br>Used in: RNNs, LSTMs, centered data',
            'gelu': 'GELU(z) = z × Φ(z)<br><i>(where Φ is CDF of standard normal)</i><br><br>Used in: Modern transformers, BERT, GPT',
            'swish': 'Swish(z) = z × σ(z)<br><i>(also called SiLU)</i><br><br>Used in: EfficientNet, mobile networks',
            'leaky_relu': 'LeakyReLU(z) = max(0.2 × z, z)<br><br>Used in: GANs, avoiding dead neurons',
            'elu': 'ELU(z) = z if z > 0, else e<sup>z</sup> - 1<br><br>Used in: Deep networks, noise robustness',
            'linear': 'Linear(z) = z<br><br>Used in: Regression output layers'
        }
        return definitions.get(activation_type, 'Unknown function')

    def get_border_color_for_value(value, max_abs=2.0):
        """Get border color based on value (using configurable color scheme)"""
        if abs(value) < 0.05:
            return COLOR_NEUTRAL  # Gray (or other neutral color)
        elif value > 0:
            return COLOR_POSITIVE  # Positive values
        else:
            return COLOR_NEGATIVE  # Negative values

    def get_line_width_for_value(value, max_width=8):
        """Get line width based on any value magnitude"""
        return max(2, min(max_width, abs(value) * 3))

    def apply_activation(z, activation_type):
        """Apply the selected activation function"""
        if activation_type == 'sigmoid':
            return 1 / (1 + np.exp(-np.clip(z, -500, 500)))  # Clip to prevent overflow
        elif activation_type == 'relu':
            return np.maximum(0, z)
        elif activation_type == 'tanh':
            return np.tanh(z)
        elif activation_type == 'gelu':
            # GELU: x * Φ(x) where Φ is CDF of standard normal
            return 0.5 * z * (1 + np.tanh(np.sqrt(2/np.pi) * (z + 0.044715 * z**3)))
        elif activation_type == 'swish':
            # Swish/SiLU: x * sigmoid(x)
            return z * (1 / (1 + np.exp(-np.clip(z, -500, 500))))
        elif activation_type == 'leaky_relu':
            # LeakyReLU: max(0.2*x, x)
            return np.where(z > 0, z, 0.2 * z)
        elif activation_type == 'elu':
            # ELU: x if x > 0, else α(exp(x) - 1) with α=1
            return np.where(z > 0, z, np.exp(np.clip(z, -500, 500)) - 1)
        elif activation_type == 'linear':
            return z
        else:
            return z

    def update_neuron_architecture():
        """Update the dedicated neuron architecture visualization"""
        # Get current values
        inputs = np.array([slider.value for slider in input_sliders])
        weights = np.array([slider.value for slider in weight_sliders])
        bias = bias_slider.value
        activation_type = activation_dropdown.value

        # Compute forward pass
        weighted_sum = np.dot(inputs, weights) + bias
        output = apply_activation(weighted_sum, activation_type)

        # Create single-panel figure for neuron architecture
        fig = go.Figure()

        # === NEURON ARCHITECTURE (Horizontal Flow) ===

        # Add background box for neuron architecture panel
        fig.add_shape(
            type="rect",
            x0=-3.0, y0=-3.2, x1=19.0, y1=3.2,
            fillcolor='rgba(200,200,200)',
            line=dict(color='rgba(200,200,200)', width=0)
        )

        # Input circles
        input_x = [0, 0, 0]
        input_y = [2, 0, -2]

        # Input circles using shapes (data coordinates)
        input_radius = 0.8  # Doubled for proper visual size
        for i, (x, y, inp_val) in enumerate(zip(input_x, input_y, inputs)):
            # Input circle shape
            fig.add_shape(
                type="circle",
                x0=x-input_radius, y0=y-input_radius,
                x1=x+input_radius, y1=y+input_radius,
                fillcolor='white',
                line=dict(width=4, color=get_border_color_for_value(inp_val))
            )
            # Input text
            fig.add_annotation(
                x=x, y=y, text=f'{inp_val:.2f}',
                showarrow=False,
                font=dict(size=12, color='black')
            )
            # Input labels
            fig.add_annotation(
                x=x-1.6, y=y, text=f'x<sub>{i+1}</sub>',
                showarrow=False, font=dict(size=14)
            )

        # Neuron circle using shape (data coordinates)
        neuron_x, neuron_y = 8, 0  # Doubled horizontal spacing
        neuron_radius = 1.15
        fig.add_shape(
            type="circle",
            x0=neuron_x-neuron_radius, y0=neuron_y-neuron_radius,
            x1=neuron_x+neuron_radius, y1=neuron_y+neuron_radius,
            fillcolor='lightyellow',
            line=dict(width=4, color=get_border_color_for_value(weighted_sum)),

        )

        # Add two-line text to neuron circle
        fig.add_annotation(
            x=neuron_x, y=neuron_y + 0.2,
            text='z = Σ + b',
            showarrow=False,
            font=dict(size=12, color='black'),

        )
        fig.add_annotation(
            x=neuron_x, y=neuron_y - 0.35,
            text=f'{weighted_sum:.2f}',
            showarrow=False,
            font=dict(size=12, color='black', family='monospace'),

        )

        # Output circle using shape (data coordinates)
        output_x, output_y = 17, 0  # Doubled horizontal spacing
        output_radius = 0.9  # Doubled for proper visual size
        fig.add_shape(
            type="circle",
            x0=output_x-output_radius, y0=output_y-output_radius,
            x1=output_x+output_radius, y1=output_y+output_radius,
            fillcolor='white',
            line=dict(width=4, color=get_border_color_for_value(output)),

        )
        # Output text
        fig.add_annotation(
            x=output_x, y=output_y, text=f'{output:.2f}',
            showarrow=False,
            font=dict(size=12, color='black'),

        )

        # Activation function box (centered on line between neuron and output)
        activation_x = (neuron_x + output_x) / 2
        activation_y = 0
        fig.add_shape(
            type="rect",
            x0=activation_x - 1.5, y0=activation_y - 0.7,
            x1=activation_x + 1.5, y1=activation_y + 0.7,
            fillcolor='lightyellow',
            line=dict(color='orange', width=3),

        )

        # Get compact activation symbol for display
        activation_symbols = {
            'sigmoid': 'σ',
            'relu': 'ReLU',
            'tanh': 'tanh',
            'gelu': 'GELU',
            'swish': 'Swish',
            'leaky_relu': 'LeakyReLU',  # No "y =" prefix for this one
            'elu': 'ELU',
            'linear': 'Linear'
        }
        if activation_type in activation_symbols:
            activation_symbol = activation_symbols[activation_type]  #type: ignore
        else:
            activation_symbol = activation_type

        # Special handling for leaky_relu (too long for "y = " prefix)
        if activation_type == 'leaky_relu':
            display_text = f'{activation_symbol}(z)'
        else:
            display_text = f'y = {activation_symbol}(z)'

        fig.add_annotation(
            x=activation_x, y=activation_y,
            text=display_text,
            showarrow=False,
            font=dict(size=13, color='black'),

        )

        # Connections with two-segment coloring (shapes use data coordinates!)
        for i, (inp_x, inp_y, weight) in enumerate(zip(input_x, input_y, weights)):
            # Calculate connection from input circle edge to neuron circle edge
            dx = neuron_x - inp_x
            dy = neuron_y - inp_y
            distance = np.sqrt(dx**2 + dy**2)

            # Simple edge calculations using known radii in data units
            start_x = inp_x + (dx / distance) * input_radius
            start_y = inp_y + (dy / distance) * input_radius
            end_x = neuron_x - (dx / distance) * neuron_radius
            end_y = neuron_y - (dy / distance) * neuron_radius

            # Weight box position (midpoint)
            mid_x = (start_x + end_x) / 2
            mid_y = (start_y + end_y) / 2

            # Calculate weighted input value for second segment
            inp_val = inputs[i]
            weighted_input = inp_val * weight

            # First segment: Input circle to weight box (colored and sized by input value)
            fig.add_trace(go.Scatter(
                x=[start_x, mid_x],
                y=[start_y, mid_y],
                mode='lines',
                line=dict(
                    color=get_border_color_for_value(inp_val),
                    width=get_line_width_for_value(inp_val)
                ),
                showlegend=False,
                hoverinfo='skip'
                            ))

            # Second segment: Weight box to neuron circle (colored and sized by weighted input)
            fig.add_trace(go.Scatter(
                x=[mid_x, end_x],
                y=[mid_y, end_y],
                mode='lines',
                line=dict(
                    color=get_border_color_for_value(weighted_input),
                    width=get_line_width_for_value(weighted_input)
                ),
                showlegend=False,
                hoverinfo='skip'
                            ))

            # Fixed-size weight box (doubled + 30% width for better text fit)
            box_width = 2.08
            box_height = 0.6
            fig.add_shape(
                type="rect",
                x0=mid_x - box_width/2, y0=mid_y - box_height/2,
                x1=mid_x + box_width/2, y1=mid_y + box_height/2,
                fillcolor='white',
                line=dict(color=get_border_color_for_value(weight), width=2),

            )
            # Weight text (separate from box)
            fig.add_annotation(
                x=mid_x, y=mid_y,
                text=f'w<sub>{i+1}</sub>={weight:.2f}',
                showarrow=False,
                font=dict(size=10, color='black'),

            )

        # Fixed-size bias box (same dimensions as weight boxes)
        bias_x = neuron_x
        bias_y = neuron_y - 1.8
        fig.add_shape(
            type="rect",
            x0=bias_x - box_width/2, y0=bias_y - box_height/2,
            x1=bias_x + box_width/2, y1=bias_y + box_height/2,
            fillcolor='white',
            line=dict(color=get_border_color_for_value(bias), width=2),

        )
        # Bias text (separate from box)
        fig.add_annotation(
            x=bias_x, y=bias_y,
            text=f'b = {bias:.2f}',
            showarrow=False,
            font=dict(size=10, color='black'),

        )

        # Connection from neuron to activation function box (dynamic color+width based on weighted_sum)
        activation_box_width = 1.5
        fig.add_trace(go.Scatter(
            x=[neuron_x + neuron_radius, activation_x - activation_box_width],
            y=[neuron_y, activation_y],
            mode='lines',
            line=dict(
                color=get_border_color_for_value(weighted_sum),
                width=get_line_width_for_value(weighted_sum)
            ),
            showlegend=False,
            hoverinfo='skip'
                        ))

        # Add "z" label centered below the neuron-to-activation line
        z_line_center_x = (neuron_x + neuron_radius + activation_x - activation_box_width) / 2
        z_line_center_y = (neuron_y + activation_y) / 2
        fig.add_annotation(
            x=z_line_center_x, y=z_line_center_y - 0.3,
            text='z', showarrow=False,
            font=dict(size=12, color='black'),

        )

        # Connection from activation function box to output (dynamic color+width based on output)
        fig.add_trace(go.Scatter(
            x=[activation_x + activation_box_width, output_x - output_radius],
            y=[activation_y, output_y],
            mode='lines',
            line=dict(
                color=get_border_color_for_value(output),
                width=get_line_width_for_value(output)
            ),
            showlegend=False,
            hoverinfo='skip'
                        ))

        # Add "y" label centered below the activation-to-output line
        y_line_center_x = (activation_x + activation_box_width + output_x - output_radius) / 2
        y_line_center_y = (activation_y + output_y) / 2
        fig.add_annotation(
            x=y_line_center_x, y=y_line_center_y - 0.3,
            text='y', showarrow=False,
            font=dict(size=12, color='black'),

        )

        # Triangular legend centered on output circle
        tri_height = 0.6  # 50% higher (0.4 → 0.6)
        tri_width = 1.25  # 25% longer (1.0 → 1.25)
        gap_width = 0.18  # Grey segment width (3x longer horizontally)
        gap_height = 0.06  # Grey segment height (keep original)
        # Center legend on output circle (total width = 2*tri_width + gap_width = 2.68)
        legend_x = output_x - (2 * tri_width + gap_width) / 2  # Center on output_x = 17
        legend_y = 2.5

        # Meeting point where triangles meet tip to tip
        meeting_point = legend_x + tri_width

        # Blue triangle pointing right with "-" (negative values)
        fig.add_shape(
            type="path",
            path=f"M {legend_x} {legend_y - tri_height/2} L {meeting_point} {legend_y} L {legend_x} {legend_y + tri_height/2} Z",
            fillcolor=COLOR_NEGATIVE, line=dict(color=COLOR_NEGATIVE, width=0),

        )
        fig.add_annotation(
            x=legend_x + tri_width/4, y=legend_y,  # Moved outward (1/3 → 1/4)
            text='−', showarrow=False, font=dict(size=10, color='white'),

        )

        # Red triangle pointing left with "+" (positive values) - starts at meeting point
        fig.add_shape(
            type="path",
            path=f"M {meeting_point + tri_width} {legend_y - tri_height/2} L {meeting_point} {legend_y} L {meeting_point + tri_width} {legend_y + tri_height/2} Z",
            fillcolor=COLOR_POSITIVE, line=dict(color=COLOR_POSITIVE, width=0),

        )
        fig.add_annotation(
            x=meeting_point + tri_width*3/4, y=legend_y,  # Moved outward (2/3 → 3/4)
            text='+', showarrow=False, font=dict(size=10, color='white'),

        )

        # Grey segment painted on top of triangle tips (overwrites the narrow parts)
        fig.add_shape(
            type="rect",
            x0=meeting_point - gap_width/2, y0=legend_y - gap_height/2,
            x1=meeting_point + gap_width/2, y1=legend_y + gap_height/2,
            fillcolor=COLOR_NEUTRAL, line=dict(color=COLOR_NEUTRAL, width=0),

        )

        # Architecture panel settings with proper aspect ratio and locked zoom
        fig.update_xaxes(range=[-3, 19], showgrid=False, zeroline=False, showticklabels=False,
                        fixedrange=True)
        fig.update_yaxes(range=[-3, 3], showgrid=False, zeroline=False, showticklabels=False,
                        scaleanchor="x", scaleratio=1,  # Make circles truly circular!
                        fixedrange=True)

        # Layout for dedicated neuron architecture canvas (fixed title clipping with more top margin)
        fig.update_layout(
            height=280,  # Keep current height for architecture visualization
            showlegend=False,
            margin=dict(t=40, b=20, l=20, r=20),  # Increased top margin to prevent title clipping
            title=dict(text="Neuron Architecture", font=dict(size=16)),
            plot_bgcolor='whitesmoke'
        )

        return fig

    def update_activation_plot():
        """Update the dedicated activation function plot"""
        # Get current values
        inputs = np.array([slider.value for slider in input_sliders])
        weights = np.array([slider.value for slider in weight_sliders])
        bias = bias_slider.value
        activation_type = activation_dropdown.value

        # Compute forward pass
        weighted_sum = np.dot(inputs, weights) + bias

        # Create single-panel figure for activation function
        fig = go.Figure()

        # === ACTIVATION FUNCTION PLOT ===
        z_range = np.linspace(-5, 5, 100)
        y_range = apply_activation(z_range, activation_type)

        # Plot the activation function curve
        fig.add_trace(go.Scatter(
            x=z_range, y=y_range,
            mode='lines',
            line=dict(color='blue', width=3),
            name=f'{activation_type.title()} Function',  # type: ignore
            showlegend=False,
            hoverinfo='skip'
        ))

        # Add current point
        current_y = apply_activation(weighted_sum, activation_type)
        fig.add_trace(go.Scatter(
            x=[weighted_sum], y=[current_y],
            mode='markers',
            marker=dict(color='red', size=12, symbol='circle'),
            name='Current Point',
            showlegend=False,
            hoverinfo='skip'
        ))

        # Add reference lines
        fig.add_hline(y=0, line_dash="dash", line_color="gray", opacity=0.5)  # type: ignore
        fig.add_vline(x=0, line_dash="dash", line_color="gray", opacity=0.5)  # type: ignore

        if activation_type == 'sigmoid':
            fig.add_hline(y=0.5, line_dash="dot", line_color="orange", opacity=0.7)  # type: ignore

        # Update axes (with locked zoom)
        fig.update_xaxes(title_text="Input Value (z)", range=[-5, 5], fixedrange=True)
        fig.update_yaxes(title_text=f"Output = {activation_type}(z)",
                        range=[-0.1, 1.1] if activation_type == 'sigmoid' else
                              [-1.1, 1.1] if activation_type == 'tanh' else
                              [-0.5, 5] if activation_type == 'relu' else [-5, 5],
                        fixedrange=True)

        # Layout for dedicated activation plot canvas (expanded height to use available space)
        fig.update_layout(
            height=300,  # Much more expanded height to better use available vertical space (was 200px)
            showlegend=False,
            margin=dict(t=30, b=30, l=40, r=20),  # Reduced bottom margin to maximize plot area
            title=dict(text=f"{activation_type.title()} Activation Function", font=dict(size=14)),  # type: ignore
            plot_bgcolor='white'
        )

        return fig

    def update_computation_details():
        """Update the computation details panel with current values"""
        inputs = np.array([slider.value for slider in input_sliders])
        weights = np.array([slider.value for slider in weight_sliders])
        bias = bias_slider.value
        activation_type = activation_dropdown.value

        # Compute forward pass
        weighted_sum = np.dot(inputs, weights) + bias
        output = apply_activation(weighted_sum, activation_type)

        computation_html = f"""
        <div style='display: flex; font-family: monospace; font-size: 10px; line-height: 1.2; height: 100%; align-items: stretch; background-color: #f0f8ff; border-radius: 5px;'>
            <div style='flex: 1; padding: 5px; margin-right: 10px;'>
                <b>Weighted Sum:</b><br>
                z = {weights[0]:.2f}×{inputs[0]:.2f} + {weights[1]:.2f}×{inputs[1]:.2f} + {weights[2]:.2f}×{inputs[2]:.2f} + {bias:.2f}<br>
                <b>z = {weighted_sum:.2f}</b>
            </div>
            <div style='flex: 1; padding: 5px; margin-left: 10px;'>
                <b>Activation Function:</b><br>
                y = {activation_type}({weighted_sum:.2f})<br>
                <b>y = {output:.2f}</b>
            </div>
        </div>
        """
        computation_details.value = computation_html

    def update_callback(change=None):
        """Callback function for slider changes"""
        # Update activation function definition
        activation_definition.value = f"<div style='font-size: 11px; color: #666; margin-left: 20px; line-height: 1.3; margin-top: 2px;'>{get_activation_definition(activation_dropdown.value)}</div>"

        # Update computation details
        update_computation_details()

        # Check experiments after every interaction
        check_experiments()

        # Update neuron architecture canvas
        with neuron_architecture_output:
            clear_output(wait=True)
            arch_fig = update_neuron_architecture()
            arch_fig.show()

        # Update activation plot canvas
        with activation_plot_output:
            clear_output(wait=True)
            act_fig = update_activation_plot()
            act_fig.show()

    # Preset functionality removed per user request

    # Preset button handling removed

    # Connect sliders and dropdown to callback
    for slider in input_sliders + weight_sliders + [bias_slider]:
        slider.observe(update_callback, names='value')
    activation_dropdown.observe(update_callback, names='value')

    # Create input controls for row 1 left (8% width = 75px) - aligned with grey plot area
    input_controls = HBox(input_sliders,  # Horizontal box to hold 3 vertical sliders side by side
                         layout=Layout(margin='45px 10px 5px 0px', width='75px'))  # Top margin to align with plot area

    # Create weight controls for row 2 left (increased height to eliminate scrollbars)
    weight_sliders_container = VBox(weight_sliders + [bias_slider],
                                   layout=Layout(margin='10px 0px 0px 0px'))  # Top margin for vertical centering
    weight_controls = VBox([weight_sliders_container],
                          layout=Layout(border='3px solid darkgrey', padding='5px', border_radius='8px',
                                      margin='5px 0px 0px 0px', width='310px', height='130px'))  # Taller to prevent scrollbars
    # Add small spacer for better visual separation (increased by 20%)
    spacer = HTML("<div style='height: 1.8px;'></div>")

    # Wikipedia link for more information (spacing increased by 20%)
    wiki_link = HTML('<div style="font-size: 9px; margin-top: 1.8px; margin-left: 20px;"><a href="https://en.wikipedia.org/wiki/Activation_function" target="_blank" style="color: #4285f4; text-decoration: none;">📖 Learn more on Wikipedia</a></div>')

    # Activation controls panel (increased height to eliminate scrollbars)
    activation_controls = VBox([activation_dropdown, spacer, activation_definition, wiki_link],
                              layout=Layout(border='3px solid orange', padding='5px', border_radius='8px',
                                          width='310px', height='145px', margin='5px 20px 5px 0px'))  # Taller to prevent scrollbars

    # Create ROW 1: Input controls + Neuron Architecture (75px + 10px + 855px = 940px)
    row_1 = HBox([input_controls, neuron_architecture_output],
                 layout=Layout(justify_content='flex-start', width='940px'))

    # Create ROW 2 LEFT: Activation controls + Weight controls (stacked vertically)
    row_2_left = VBox([activation_controls, weight_controls],
                      layout=Layout(width='340px', height='300px', overflow='hidden'))  # Fixed height to match plot

    # Create spacer column (5% of 940px = 47px)
    spacer_column = HTML("", layout=Layout(width='47px', height='300px'))

    # Create ROW 2: (Activation + Weight) + Spacer + Activation Plot (340px + 47px + 553px = 940px)
    row_2 = HBox([row_2_left, spacer_column, activation_plot_output],
                 layout=Layout(justify_content='flex-start', width='940px'))

    # Create main layout: Row 1 stacked on Row 2
    main_layout = VBox([row_1, row_2],
                       layout=Layout(width='940px'))

    # Create computation details panel
    computation_details = HTML("", layout=Layout(
        width='940px',
        height='50px',  # Further reduced since there's empty space
        border='3px solid steelblue',
        border_radius='8px',
        margin='10px auto',  # Center the panel horizontally
        padding='0px'  # Remove padding to avoid border/background mismatch
    ))

    # Initialize activation function definition
    activation_definition.value = f"<div style='font-size: 11px; color: #666; margin-left: 20px; line-height: 1.3; margin-top: 2px;'>{get_activation_definition(activation_dropdown.value)}</div>"

    # Initialize computation details
    update_computation_details()

    # Initialize experiments display
    update_experiment_display()

    # Check if experiments are already completed on widget load
    if all(experiment_state[key] for key in ['bias_detective', 'activation_explorer']):
        __main__.widget_experiments_completed = True  # type: ignore

    # Initial plots for both canvases
    with neuron_architecture_output:
        arch_fig = update_neuron_architecture()
        arch_fig.show()

    with activation_plot_output:
        act_fig = update_activation_plot()
        act_fig.show()

    # Simple Python checkbox for skip functionality - no JavaScript interference
    skip_checkbox = Checkbox(
        value=False,
        description='Skip experiments',
        style={'description_width': 'initial', 'font_size': '9px'},
        layout=Layout(width='150px', margin='2px 0')
    )

    def skip_checkbox_handler(change):
        """Handle skip checkbox changes in pure Python"""
        if change['new']:  # Checkbox was checked
            handle_skip_override()
        elif hasattr(__main__, 'widget_experiments_completed') and __main__.widget_experiments_completed:  # type: ignore
            # If experiments were legitimately completed, don't remove progress
            pass
        else:
            # Only remove progress if experiments were not legitimately completed
            handle_skip_remove()

        # Update the display after state change
        update_experiment_display()

    skip_checkbox.observe(skip_checkbox_handler, names='value')

    # Display everything with new two-row layout structure - no JavaScript interference
    display(HTML("<h3 style='margin-bottom: 10px;'>Interactive Neuron Visualization</h3>"))
    display(experiments_display)        # Show experiments first (always visible)
    display(main_layout)                # Main layout: Row 1 (input + arch) stacked on Row 2 (controls + plot)
    display(computation_details)        # Full-width computation details (940px x 60px)
    display(skip_checkbox)              # Skip checkbox at the very bottom
