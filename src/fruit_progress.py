"""
Fruit-based Progress Tracking System - Pac-Man Inspired!
🍒 Coding Challenges, 🍓 Quizzes, 🍊 Labs → 🟡 Gold Coins per notebook
"""

import json
from pathlib import Path
import datetime
from IPython.display import display, HTML

class FruitProgressTracker:
    def __init__(self, progress_file="course_progress.json"):
        self.progress_file = Path(progress_file)

        # Define notebook requirements
        self.notebook_requirements = {
            'NB1.1': {
                'coding': 2,  # 🍒 2 coding challenges (NumPy Neuron + Layer)
                'quizzes': 0,  # 🍓 0 quizzes
                'labs': 3,    # 🍊 3 labs (Neuron demo + Interactive widget + Layer demo)
            },
            'NB1.2': {
                'coding': 2,  # 🍒 PyTorch implementation tasks
                'quizzes': 1,  # 🍓 AutoGrad quiz
                'labs': 1,    # 🍊 Training visualization
            },
            'NB1.3': {
                'coding': 3,  # 🍒 ML pipeline tasks
                'quizzes': 1,  # 🍓 Theory quiz
                'labs': 2,    # 🍊 Performance optimization + MLOps
            }
        }

        # Load or initialize progress
        self.progress_data = self._load_progress()

    def _load_progress(self):
        """Load progress from JSON file"""
        if self.progress_file.exists():
            try:
                with open(self.progress_file, 'r') as f:
                    data = json.load(f)
                # Migrate old format if needed
                if 'total_coins' in data and 'notebooks' not in data:
                    # Convert old format
                    data = {
                        'notebooks': {},
                        'total_coins': 0,
                        'start_date': data.get('start_date', datetime.datetime.now().isoformat()),
                        'last_activity': datetime.datetime.now().isoformat()
                    }
                return data
            except (json.JSONDecodeError, IOError):
                pass

        # Return default structure
        return {
            'notebooks': {},
            'total_coins': 0,
            'start_date': datetime.datetime.now().isoformat(),
            'last_activity': datetime.datetime.now().isoformat()
        }

    def _save_progress(self):
        """Save progress to JSON file"""
        self.progress_data['last_activity'] = datetime.datetime.now().isoformat()
        with open(self.progress_file, 'w') as f:
            json.dump(self.progress_data, f, indent=2)

    def unlock_fruit(self, notebook, fruit_type, activity_name):
        """Update state only - no display logic here"""
        if notebook not in self.progress_data['notebooks']:
            self.progress_data['notebooks'][notebook] = {
                'coding': [],
                'quizzes': [],
                'labs': [],
                'coin_earned': False
            }

        nb_data = self.progress_data['notebooks'][notebook]

        # Add fruit if not already earned
        if activity_name not in nb_data[fruit_type]:
            nb_data[fruit_type].append(activity_name)

            # Check if notebook is complete and award coin
            requirements = self.notebook_requirements[notebook]
            coding_complete = len(nb_data['coding']) >= requirements['coding']
            quiz_complete = len(nb_data['quizzes']) >= requirements['quizzes']
            lab_complete = len(nb_data['labs']) >= requirements['labs']

            if coding_complete and quiz_complete and lab_complete and not nb_data['coin_earned']:
                nb_data['coin_earned'] = True
                self.progress_data['total_coins'] += 1

            self._save_progress()
            return True
        return False



    def get_notebook_progress_display(self, notebook):
        """Get progress display string like: 🍒🍒⚪ 🍓⚪ 🍊⚪ → 🟡"""
        if notebook not in self.notebook_requirements:
            return ""

        requirements = self.notebook_requirements[notebook]
        current = self.progress_data['notebooks'].get(notebook, {'coding': [], 'quizzes': [], 'labs': []})

        display_parts = []

        # Coding challenges (🍒)
        coding_earned = len(current['coding'])
        coding_needed = requirements['coding']
        if coding_needed > 0:
            display_parts.append('🍒' * coding_earned + '⚪' * (coding_needed - coding_earned))

        # Quizzes (🍓)
        quiz_earned = len(current['quizzes'])
        quiz_needed = requirements['quizzes']
        if quiz_needed > 0:
            display_parts.append('🍓' * quiz_earned + '⚪' * (quiz_needed - quiz_earned))

        # Labs (🍊)
        lab_earned = len(current['labs'])
        lab_needed = requirements['labs']
        if lab_needed > 0:
            display_parts.append('🍊' * lab_earned + '⚪' * (lab_needed - lab_earned))

        # Gold coin status
        coin_status = '🟡' if current.get('coin_earned', False) else '⚪'

        return ' '.join(display_parts) + ' → ' + coin_status

    def get_notebook_progress_simple(self, notebook):
        """Get simplified progress display without the coin arrow: 🍒🍒⚪ 🍓⚪ 🍊⚪"""
        if notebook not in self.notebook_requirements:
            return ""

        requirements = self.notebook_requirements[notebook]
        current = self.progress_data['notebooks'].get(notebook, {'coding': [], 'quizzes': [], 'labs': []})

        display_parts = []

        # Coding challenges (🍒)
        coding_earned = len(current['coding'])
        coding_needed = requirements['coding']
        if coding_needed > 0:
            display_parts.append('🍒' * coding_earned + '⚪' * (coding_needed - coding_earned))

        # Quizzes (🍓)
        quiz_earned = len(current['quizzes'])
        quiz_needed = requirements['quizzes']
        if quiz_needed > 0:
            display_parts.append('🍓' * quiz_earned + '⚪' * (quiz_needed - quiz_earned))

        # Labs (🍊)
        lab_earned = len(current['labs'])
        lab_needed = requirements['labs']
        if lab_needed > 0:
            display_parts.append('🍊' * lab_earned + '⚪' * (lab_needed - lab_earned))

        return ' '.join(display_parts)

    def show_progress_dashboard(self):
        """Display the fruit-based progress dashboard"""
        total_coins = self.progress_data['total_coins']

        # Generate notebook progress displays
        notebook_displays = []
        for notebook in ['NB1.1', 'NB1.2', 'NB1.3']:
            progress_display = self.get_notebook_progress_display(notebook)
            notebook_displays.append(f"📚 {notebook}: {progress_display}")

        display(HTML(f"""
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 15px; padding: 25px; margin: 20px 0; color: white; box-shadow: 0 6px 12px rgba(0,0,0,0.3);">
            <h1 style="text-align: center; margin: 0 0 20px 0;">🍒🍓🍊 Progress Tracker</h1>

            <div style="background: rgba(255,255,255,0.1); border-radius: 10px; padding: 20px; margin: 20px 0;">
                <h3 style="margin: 0 0 15px 0;">Notebook Progress:</h3>
                <div style="font-family: monospace; font-size: 16px; line-height: 1.8;">
                    {'<br>'.join(notebook_displays)}
                </div>
            </div>

            <div style="text-align: center; margin: 20px 0;">
                <h2 style="margin: 0; font-size: 2.5em;">{total_coins} 🟡</h2>
                <p style="margin: 5px 0; font-size: 1.2em;">Gold Coins Earned</p>
                <p style="margin: 5px 0; font-size: 1em; opacity: 0.8;">{total_coins}/3 Notebooks Complete</p>
            </div>

            <div style="background: rgba(255,255,255,0.1); border-radius: 10px; padding: 15px; margin: 20px 0;">
                <h4 style="margin: 0 0 10px 0;">Legend:</h4>
                <p style="margin: 5px 0;">🍒 = Coding Challenge | 🍓 = Quiz | 🍊 = Lab | ⚪ = Not completed</p>
                <p style="margin: 5px 0;">Complete all fruits in a notebook → Earn 1 🟡 Gold Coin!</p>
            </div>
        </div>
        """))

# Global instance
_fruit_tracker = FruitProgressTracker()

# Convenience functions
def unlock_fruit(notebook, fruit_type, activity_name):
    """Unlock a fruit: fruit_type = 'coding', 'quizzes', or 'labs'"""
    return _fruit_tracker.unlock_fruit(notebook, fruit_type, activity_name)

def show_progress():
    """Show the fruit progress dashboard"""
    _fruit_tracker.show_progress_dashboard()

def get_notebook_progress(notebook):
    """Get notebook progress display string"""
    return _fruit_tracker.get_notebook_progress_display(notebook)

def get_notebook_progress_simple(notebook):
    """Get simplified notebook progress without coin arrow"""
    return _fruit_tracker.get_notebook_progress_simple(notebook)
