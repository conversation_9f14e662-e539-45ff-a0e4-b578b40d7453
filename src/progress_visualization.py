#!/usr/bin/env python3
"""
Progress Visualization System - 100% independent, only reads from JSON
No dependencies on any other modules or in-memory state
"""

import json
import datetime
from pathlib import Path
from IPython.display import display, HTML  # type: ignore


# Notebook requirements are duplicated here for complete independence
_NOTEBOOK_REQUIREMENTS = {
    'NB1.1': {
        'coding': 2,  # 🍒 2 coding challenges
        'quizzes': 0,  # 🍓 0 quizzes
        'labs': 3,    # 🍊 3 labs
    },
    'NB1.2': {
        'coding': 2,  # 🍒 PyTorch implementation tasks
        'quizzes': 1,  # 🍓 AutoGrad quiz
        'labs': 1,    # 🍊 Training visualization
    },
    'NB1.3': {
        'coding': 3,  # 🍒 ML pipeline tasks
        'quizzes': 1,  # 🍓 Theory quiz
        'labs': 2,    # 🍊 Performance optimization + MLOps
    }
}


def _ensure_progress_file_exists(progress_file="course_progress.json"):
    """Ensure the progress file exists with proper structure"""
    progress_file = Path(progress_file)

    if not progress_file.exists():
        # Create new file with default structure
        default_data = {
            'notebooks': {},
            'start_date': datetime.datetime.now().isoformat(),
            'last_activity': datetime.datetime.now().isoformat()
        }
        with open(progress_file, 'w') as f:
            json.dump(default_data, f, indent=2)
        return default_data

    # Load existing file
    try:
        with open(progress_file, 'r') as f:
            return json.load(f)
    except (json.JSONDecodeError, IOError):
        # File exists but is corrupted, recreate it
        default_data = {
            'notebooks': {},
            'start_date': datetime.datetime.now().isoformat(),
            'last_activity': datetime.datetime.now().isoformat()
        }
        with open(progress_file, 'w') as f:
            json.dump(default_data, f, indent=2)
        return default_data


def _read_progress_from_json(progress_file="course_progress.json"):
    """Read progress data from JSON file - fresh read every time"""
    progress_file = Path(progress_file)

    if not progress_file.exists():
        return None

    try:
        with open(progress_file, 'r') as f:
            return json.load(f)
    except (json.JSONDecodeError, IOError):
        return None


def get_notebook_progress_display(notebook, progress_file="course_progress.json"):
    """Get progress display string like: 🍒🍒⚪ 🍓⚪ 🍊⚪ → 🟡"""
    # Fresh read from JSON
    data = _read_progress_from_json(progress_file)
    if not data:
        return ""

    if notebook not in _NOTEBOOK_REQUIREMENTS:
        return ""

    requirements = _NOTEBOOK_REQUIREMENTS[notebook]
    current = data.get('notebooks', {}).get(notebook, {'coding': [], 'quizzes': [], 'labs': []})

    display_parts = []

    # Coding challenges (🍒)
    coding_earned = len(current.get('coding', []))
    coding_needed = requirements['coding']
    if coding_needed > 0:
        display_parts.append('🍒' * coding_earned + '⚪' * (coding_needed - coding_earned))

    # Quizzes (🍓)
    quiz_earned = len(current.get('quizzes', []))
    quiz_needed = requirements['quizzes']
    if quiz_needed > 0:
        display_parts.append('🍓' * quiz_earned + '⚪' * (quiz_needed - quiz_earned))

    # Labs (🍊)
    lab_earned = len(current.get('labs', []))
    lab_needed = requirements['labs']
    if lab_needed > 0:
        display_parts.append('🍊' * lab_earned + '⚪' * (lab_needed - lab_earned))

    # Gold coin status
    coin_status = '🟡' if current.get('coin_earned', False) else '⚪'

    return ' '.join(display_parts) + ' → ' + coin_status


def get_notebook_progress_simple(notebook, progress_file="course_progress.json"):
    """Get simplified progress display without the coin arrow: 🍒🍒⚪ 🍓⚪ 🍊⚪"""
    # Fresh read from JSON
    data = _read_progress_from_json(progress_file)
    if not data:
        return ""

    if notebook not in _NOTEBOOK_REQUIREMENTS:
        return ""

    requirements = _NOTEBOOK_REQUIREMENTS[notebook]
    current = data.get('notebooks', {}).get(notebook, {'coding': [], 'quizzes': [], 'labs': []})

    display_parts = []

    # Coding challenges (🍒)
    coding_earned = len(current.get('coding', []))
    coding_needed = requirements['coding']
    if coding_needed > 0:
        display_parts.append('🍒' * coding_earned + '⚪' * (coding_needed - coding_earned))

    # Quizzes (🍓)
    quiz_earned = len(current.get('quizzes', []))
    quiz_needed = requirements['quizzes']
    if quiz_needed > 0:
        display_parts.append('🍓' * quiz_earned + '⚪' * (quiz_needed - quiz_earned))

    # Labs (🍊)
    lab_earned = len(current.get('labs', []))
    lab_needed = requirements['labs']
    if lab_needed > 0:
        display_parts.append('🍊' * lab_earned + '⚪' * (lab_needed - lab_earned))

    return ' '.join(display_parts)


def show_progress_dashboard(progress_file="course_progress.json"):
    """Display the fruit-based progress dashboard"""
    # Fresh read from JSON
    data = _read_progress_from_json(progress_file)

    if not data:
        display(HTML("""
        <div style="background: #f8d7da; border-radius: 10px; padding: 20px; margin: 10px 0; color: #721c24;">
            <h3>No Progress Data Found</h3>
            <p>Start completing tasks to track your progress!</p>
        </div>
        """))
        return

    # Calculate total coins dynamically from notebook completion states
    total_coins = sum(1 for nb_data in data.get('notebooks', {}).values()
                     if nb_data.get('coin_earned', False))

    # Generate notebook progress displays
    notebook_displays = []
    for notebook in ['NB1.1', 'NB1.2', 'NB1.3']:
        progress_display = get_notebook_progress_display(notebook, progress_file)
        notebook_displays.append(f"📚 {notebook}: {progress_display}")

    display(HTML(f"""
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 15px; padding: 25px; margin: 20px 0; color: white; box-shadow: 0 6px 12px rgba(0,0,0,0.3);">
        <h1 style="text-align: center; margin: 0 0 20px 0;">🍒🍓🍊 Progress Tracker</h1>

        <div style="background: rgba(255,255,255,0.1); border-radius: 10px; padding: 20px; margin: 20px 0;">
            <h3 style="margin: 0 0 15px 0;">Notebook Progress:</h3>
            <div style="font-family: monospace; font-size: 16px; line-height: 1.8;">
                {'<br>'.join(notebook_displays)}
            </div>
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <h2 style="margin: 0; font-size: 2.5em;">{total_coins} 🟡</h2>
            <p style="margin: 5px 0; font-size: 1.2em;">Gold Coins Earned</p>
            <p style="margin: 5px 0; font-size: 1em; opacity: 0.8;">{total_coins}/3 Notebooks Complete</p>
        </div>

        <div style="background: rgba(255,255,255,0.1); border-radius: 10px; padding: 15px; margin: 20px 0;">
            <h4 style="margin: 0 0 10px 0;">Legend:</h4>
            <p style="margin: 5px 0;">🍒 = Coding Challenge | 🍓 = Quiz | 🍊 = Lab | ⚪ = Not completed</p>
            <p style="margin: 5px 0;">Complete all fruits in a notebook → Earn 1 🟡 Gold Coin!</p>
        </div>
    </div>
    """))


def show_task_completion_banner(task_name, notebook='NB1.1', progress_file="course_progress.json"):
    """Show green completion banner for a specific task"""
    # Map task names to display messages
    task_messages = {
        'numpy_neuron': '✅ NumpyNeuron implementation is correct!',
        'neuron_demo': '🍊 Neuron Usage Demo completed!',
        'interactive_widget': '🍊 Interactive Neuron Lab completed!',
        'numpy_layer': '✅ NumpyLayer implementation is correct!\n   Successfully vectorized computation for multiple neurons',
        'layer_demo': '🍊 Layer Usage Demo completed!'
    }

    message = task_messages.get(task_name, f'✅ {task_name} completed!')

    display(HTML(f"""
    <div style="background: linear-gradient(135deg, #4CAF50, #45a049); border-radius: 10px; padding: 20px; margin: 10px 0; text-align: center; color: white; box-shadow: 0 2px 4px rgba(0,0,0,0.2);">
        <h2 style="margin: 0; font-size: 24px;">{message}</h2>
    </div>
    """))


def show_notebook_milestone(notebook, progress_file="course_progress.json"):
    """Show milestone message when a notebook is completed"""
    # Fresh read from JSON
    data = _read_progress_from_json(progress_file)
    if not data:
        return

    nb_data = data.get('notebooks', {}).get(notebook, {})

    if not nb_data.get('coin_earned', False):
        return  # Not complete yet

    if notebook == 'NB1.1':
        display(HTML("""
        <div style="background: #f8f9fa; border-radius: 10px; padding: 20px; margin: 20px 0; border: 2px solid #dee2e6;">
            <h2 style="color: #495057; margin: 0 0 15px 0;">⭐️ Milestone: ANN Basics Completed!</h2>

            <h3 style="color: #343a40; margin: 20px 0 10px 0;">🎓 What You've Accomplished</h3>
            <p style="color: #495057; line-height: 1.6;">
                <strong>You've mastered the fundamentals!</strong> You built neural networks from scratch using a standard math library, NumPy:
            </p>
            <ul style="color: #495057; line-height: 1.8;">
                <li><strong>Single Neuron</strong>: Weights, bias, activation functions</li>
                <li><strong>Neural Layer</strong>: Matrix operations, vectorization, multiple neurons</li>
                <li><strong>Mathematical Foundation</strong>: You understand what happens under the hood</li>
            </ul>

            <h3 style="color: #343a40; margin: 20px 0 10px 0;">🚀 Why PyTorch is the Next Step</h3>
            <p style="color: #495057; line-height: 1.6;">
                <strong>NumPy is nice for learning basics, but has severe limitations for real ML.</strong>
            </p>

            <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
                <tr style="background: #e9ecef;">
                    <th style="padding: 10px; text-align: left; border: 1px solid #dee2e6;">Challenge</th>
                    <th style="padding: 10px; text-align: left; border: 1px solid #dee2e6;">NumPy Reality</th>
                    <th style="padding: 10px; text-align: left; border: 1px solid #dee2e6;">PyTorch Solution</th>
                </tr>
                <tr>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">🔥 GPU Acceleration</td>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">CPU-only, slow for large networks</td>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">Seamless GPU support with .cuda()</td>
                </tr>
                <tr style="background: #f8f9fa;">
                    <td style="padding: 10px; border: 1px solid #dee2e6;">📈 Gradient Computation</td>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">Manual backprop math (ouch!)</td>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">Automatic differentiation with .backward()</td>
                </tr>
                <tr>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">🏗️ Network Architecture</td>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">Build everything from scratch</td>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">Pre-built layers: nn.Linear, nn.Conv2d</td>
                </tr>
                <tr style="background: #f8f9fa;">
                    <td style="padding: 10px; border: 1px solid #dee2e6;">📊 Data Loading</td>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">Manual batch processing</td>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">Efficient DataLoader with multiprocessing</td>
                </tr>
                <tr>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">🎛️ Training Loop</td>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">Write optimizer by hand</td>
                    <td style="padding: 10px; border: 1px solid #dee2e6;">Ready-to-use optimizers: Adam, SGD</td>
                </tr>
            </table>

            <div style="background: #e7f3ff; border-left: 4px solid #2196F3; padding: 15px; margin: 20px 0;">
                <h4 style="color: #1976D2; margin: 0 0 10px 0;">The PyTorch Advantage</h4>
                <p style="color: #495057; margin: 0; line-height: 1.6;">
                    <strong>Same concepts, but supercharged!</strong><br>
                    • Your NumPy knowledge transfers directly<br>
                    • Focus on model design, not implementation details<br>
                    • Scale from prototype to production seamlessly
                </p>
            </div>

            <p style="color: #495057; font-size: 1.1em; margin: 20px 0 0 0;">
                <strong>Ready to level up?</strong> Let's transition to PyTorch and build something useful!
            </p>
        </div>
        """))


def show_state_based_feedback(notebook, current_task, progress_file="course_progress.json"):
    """
    Show feedback based on current state FROM JSON FILE
    This combines task completion banner, milestone, and dashboard as appropriate
    """
    # Fresh read from JSON
    data = _read_progress_from_json(progress_file)
    if not data:
        return

    nb_data = data.get('notebooks', {}).get(notebook, {})
    requirements = _NOTEBOOK_REQUIREMENTS.get(notebook, {})

    # Map task to fruit type (duplicated here for independence)
    task_map = {
        'numpy_neuron': ('coding', 'task1_numpy_neuron'),
        'neuron_demo': ('labs', 'task1b_neuron_demo'),
        'interactive_widget': ('labs', 'task2_interactive_lab'),
        'numpy_layer': ('coding', 'task3_numpy_layer'),
        'layer_demo': ('labs', 'task3b_layer_demo')
    }

    # Check if current task is completed
    if current_task in task_map:
        fruit_type, activity_name = task_map[current_task]
        task_completed = activity_name in nb_data.get(fruit_type, [])

        if task_completed:
            show_task_completion_banner(current_task, notebook, progress_file)

    # Check if notebook is complete
    coding_done = len(nb_data.get('coding', []))
    quiz_done = len(nb_data.get('quizzes', []))
    lab_done = len(nb_data.get('labs', []))

    is_complete = (coding_done >= requirements.get('coding', 0) and
                   quiz_done >= requirements.get('quizzes', 0) and
                   lab_done >= requirements.get('labs', 0))

    # Note: Milestone and dashboard are now handled separately by check_notebook_complete()
    # This function only shows task completion banners


def check_notebook_complete(notebook='NB1.1', progress_file="course_progress.json"):
    """
    Check if notebook is complete and show appropriate feedback
    Always shows the purple progress banner, milestone only if complete
    Creates progress file if it doesn't exist
    """
    # Ensure progress file exists first, then read from it
    data = _ensure_progress_file_exists(progress_file)

    nb_data = data.get('notebooks', {}).get(notebook, {})
    requirements = _NOTEBOOK_REQUIREMENTS.get(notebook, {})

    # Check if notebook is complete
    coding_done = len(nb_data.get('coding', []))
    quiz_done = len(nb_data.get('quizzes', []))
    lab_done = len(nb_data.get('labs', []))

    is_complete = (coding_done >= requirements.get('coding', 0) and
                   quiz_done >= requirements.get('quizzes', 0) and
                   lab_done >= requirements.get('labs', 0))

    # Only show milestone if notebook is actually complete
    if is_complete:
        show_notebook_milestone(notebook, progress_file)

    # Always show the purple progress dashboard
    show_progress_dashboard(progress_file)
