#!/usr/bin/env python3
import torch
import numpy as np

print("PyTorch version:", torch.__version__)

# Test device availability
if torch.backends.mps.is_available():
    device = torch.device("mps")
    print("✅ MPS (Apple GPU) is available!")
elif torch.cuda.is_available():
    device = torch.device("cuda")
    print("✅ CUDA GPU is available!")
else:
    device = torch.device("cpu")
    print("⚠️  No GPU available, using CPU")

# Test basic operations
x = torch.randn(1000, 1000, device=device)
y = torch.randn(1000, 1000, device=device)

# Time a matrix multiplication
import time
start = time.time()
z = torch.matmul(x, y)
torch.mps.synchronize() if device.type == 'mps' else None
end = time.time()

print(f"Matrix multiplication (1000x1000) took: {end-start:.4f} seconds on {device}")

# Test if all required packages are available
try:
    import torchvision
    import matplotlib
    import seaborn
    import sklearn
    print("✅ All required packages installed!")
except ImportError as e:
    print(f"❌ Missing package: {e}")
