#!/usr/bin/env python3
import sys
print(f"Python: {sys.version}")
print(f"Executable: {sys.executable}\n")

try:
    import torch
    print(f"✅ PyTorch {torch.__version__}")
    
    # Check device
    if torch.backends.mps.is_available():
        print("✅ Apple MPS (GPU) available")
        device = "mps"
    elif torch.cuda.is_available():
        print("✅ CUDA GPU available")
        device = "cuda"
    else:
        print("⚠️  CPU only")
        device = "cpu"
    
    # Test computation
    x = torch.randn(100, 100, device=device)
    y = x @ x.T
    print(f"✅ {device.upper()} computation works!")
    
except ImportError as e:
    print(f"❌ PyTorch import failed: {e}")

# Check other packages
packages = ['torchvision', 'numpy', 'matplotlib', 'seaborn', 'sklearn', 'notebook']
for pkg in packages:
    try:
        __import__(pkg)
        print(f"✅ {pkg}")
    except ImportError:
        print(f"❌ {pkg} missing")

