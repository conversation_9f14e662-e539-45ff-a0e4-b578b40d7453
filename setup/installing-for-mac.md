
# --------------------------------

# Create virtual environment
python3 -m venv pytorch_course_env

# Activate it
source pytorch_course_env/bin/activate

# Verify you're in the venv (should show the venv path)
which python

# Upgrade pip first
pip install --upgrade pip

# --------------------------------

# Install PyTorch with MPS support (for Apple Silicon)
# This automatically detects if you need MPS or CPU-only
pip install torch torchvision torchaudio

# Install all other requirements
pip install numpy matplotlib seaborn scikit-learn jupyter notebook

# --------------------------------

# Optional but useful
pip install ipywidgets tqdm

# Save current environment
pip freeze > requirements.txt

# Run the quick verification script
python verify_setup.py

# --------------------------------

# Make sure you're in the activated venv
source pytorch_course_env/bin/activate  # if not already activated

# Launch Jupyter
jupyter notebook

# Or if you prefer Jupyter Lab
//pip install jupyterlab
//jupyter lab


