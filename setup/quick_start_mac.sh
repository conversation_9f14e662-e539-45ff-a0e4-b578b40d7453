#!/bin/bash

# quick_start_mac.sh
# (A.k.a. setup_env.sh)
# Mac PyTorch Course Environment Setup

echo "🍎 Setting up PyTorch course environment..."

# Create venv
python3 -m venv pytorch_course_env
source pytorch_course_env/bin/activate

# Upgrade pip
pip install --upgrade pip

# Install PyTorch
echo "📦 Installing PyTorch..."
pip install torch torchvision torchaudio

# Install other requirements
echo "📦 Installing other packages..."
pip install numpy matplotlib seaborn scikit-learn jupyter notebook ipykernel jupytext

# Create Jupyter kernel
echo "🔧 Creating Jupyter kernel..."
python -m ipykernel install --user --name pytorch_course --display-name "PyTorch Course"

# Save requirements
pip freeze > requirements.txt

# Verify installation
echo "✅ Verifying installation..."
python -c "import torch; print(f'PyTorch {torch.__version__} installed!'); print('MPS available:', torch.backends.mps.is_available())"

echo "✨ Setup complete! To activate: source pytorch_course_env/bin/activate"
echo ""
echo "------------------------------------------------------------------------------------------------"
echo "Always activate the venv first: source pytorch_course_env/bin/activate"
echo "Then launch Jupyter: jupyter notebook"
echo "When done, deactivate the python venv: deactivate
echo "------------------------------------------------------------------------------------------------"
echo ""


